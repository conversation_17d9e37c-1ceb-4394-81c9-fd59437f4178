var cc__assign = __assign;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BaseSkill = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2GameatrCfg = require("GameatrCfg");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2BulletVoPool = require("BulletVoPool");
var $2Game = require("Game");
// var $2BulletMagazine = require("BulletMagazine");
var d = new $2GameSeting.TMap();
var g = [];
cc.Vec2.ZERO;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc__decorator.menu;
var exp_BaseSkill = function () {
  function _ctor() {
    this._launchPoint = cc.v2();
    this.lv = 1;
    this._id = 0;
    this._param = [];
    this._cutExcuteNum = 0;
    this.subSkill = [];
    this.index = -1;
    this.isAuto = false;
    this.isStartSkill = false;
    this.bulletTime = 0;
    this.isExcuteIng = false;
    this.atkNum = 0;
    this.atkExcuteNum = 0;
    this.extraReleaseCount = 0;
    this.cutBullet = [];
    this.cutEffect = [];
    this._cd = 0;
    this.releaseTime = 0;
    this._targetList = [];
    this._cutMagazineNum = 1;
    this.magazineReloadTime = .3;
  }
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "mgr", {
    get: function () {
      return this._owner.skillMgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "launchPoint", {
    get: function () {
      this._launchPoint.set(this._owner.position).addSelf(this.mgr.launchPoint);
      if (this.cutVo.releasePos) {
        this._launchPoint.x += this.cutVo.releasePos[0] || 0;
        this._launchPoint.y += this.cutVo.releasePos[1] || 0;
      }
      return this._launchPoint;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "id", {
    get: function () {
      return this._id;
    },
    set: function (e) {
      this._id = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "skillCfg", {
    get: function () {
      return this._skillCfg;
    },
    set: function (e) {
      var t;
      var o;
      var i;
      var n;
      var r;
      var a;
      var s;
      var c;
      var l;
      this.skillMainID = 20 * Math.floor(e.id / 20);
      this.id = e.id;
      this._skillCfg = $2GameUtil.GameUtil.deepCopy(e);
      (t = this._skillCfg).buffId || (t.buffId = []);
      (o = this._skillCfg).reflexCount || (o.reflexCount = 0);
      (i = this._skillCfg).barrangeSpeed || (i.barrangeSpeed = 800);
      (n = this._skillCfg).releaseCount || (n.releaseCount = 1);
      (r = this._skillCfg).releaseCd || (r.releaseCd = .2);
      (a = this._skillCfg).skillCrit || (a.skillCrit = 0);
      (s = this._skillCfg).skillCritNum || (s.skillCritNum = 0);
      this._skillCfg.subRelease = "number" == typeof this._skillCfg.subRelease ? this._skillCfg.subRelease : 99;
      (c = this._skillCfg).subId || (c.subId = []);
      (l = this._skillCfg).barrageTime || (l.barrageTime = this._skillCfg.dur || 2);
      this.cutVo = this._skillCfg;
      this._param = this._skillCfg.param;
      this.lv = this._cutVo.lv;
      this._skillCfg.soundId && (this.audioID = this._skillCfg.soundId);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cutVo", {
    get: function () {
      return this._cutVo;
    },
    set: function (e) {
      this._cutVo = cc__assign({}, e);
      this._cutVo.buffId = cc__spreadArrays(e.buffId);
      this._cutVo.subId = cc__spreadArrays(e.subId);
      e.subId.find(function (t) {
        return t[0] == e.id;
      }) || this.checkGain();
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bulletNum", {
    get: function () {
      var e = this;
      return Array.from(this.game.bulletList, function (e) {
        return e.vo.bulletId;
      }).filter(function (t) {
        return t == e.cutVo.bulletId;
      }).length;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "subSkillId", {
    get: function () {
      var e;
      g.length = 0;
      null === (e = this._owner.buffMgr) || undefined === e || e.getSpecificBuff({
        skillID: this.id
      }).forEach(function (e) {
        e.specialMap.forEach(function (e) {
          e.type == $2GameatrCfg.GameatrDefine.buffSubSkill && g.push(e.data);
        });
      });
      return cc__spreadArrays(this._cutVo.subId, g);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "curBuffId", {
    get: function () {
      var e;
      var t = cc__spreadArrays(this.skillCfg.buffId);
      ((null === (e = this._owner.buffMgr) || undefined === e ? undefined : e.getSpecificBuff({
        skillID: this.id
      }).filter(function (e) {
        return e.attrMap.has($2GameatrCfg.GameatrDefine.buffEffect) && e.isWeight;
      })) || []).forEach(function (e) {
        t.push.apply(t, e.specialMap.filter(function (e) {
          return e.type == $2GameatrCfg.GameatrDefine.buffEffect;
        }).map(function (e) {
          return e.data;
        }));
      });
      return t;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "dis", {
    get: function () {
      return this.cutVo.dis / 1334 * $2GameUtil.GameUtil.getDesignSize.height + this._owner.property.cut.atkArea;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "owner", {
    get: function () {
      return this._owner;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "hurt", {
    get: function () {
      var e = this._owner.getHurt(this._cutVo.dam);
      e.ownerSkill = this;
      e.critRate += this.cutVo.skillCrit;
      e.critValRate += this.cutVo.skillCritNum;
      e.hitBack = this.cutVo.repelDic;
      e.hurCd = this.cutVo.damCd;
      return e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "showTier", {
    get: function () {
      if (this._cutVo.showTier) {
        if (1 == this._cutVo.showTier) {
          return this.game._botEffectNode;
        } else {
          if (2 == this._cutVo.showTier) {
            return this.game._entityNode;
          } else {
            return this.game._bulletNode;
          }
        }
      } else {
        return this.game._bulletNode;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "skillBuffItem", {
    get: function () {
      var e;
      if (null === (e = this._owner.buffMgr) || undefined === e) {
        return undefined;
      } else {
        return e.getSpecificBuff({
          skillID: this.skillMainID
        });
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "skillBuff", {
    get: function () {
      var e;
      return (null === (e = this._owner.buffMgr) || undefined === e ? undefined : e.getSpecificBuffAttr({
        skillID: this.skillMainID
      })) || d;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.checkGain = function () {
    var e = this.skillCfg;
    var t = this.skillBuff;
    this._cutVo.dam = e.dam * (1 + t.getor($2GameatrCfg.GameatrDefine.skilldam, 0));
    this._cutVo.skillCrit = e.skillCrit + t.getor($2GameatrCfg.GameatrDefine.skillCrit, 0);
    this._cutVo.skillCritNum = e.skillCritNum + t.getor($2GameatrCfg.GameatrDefine.skillCritNum, 0);
    this._cutVo.barrageNum = Math.max(0, e.barrageNum + t.getor($2GameatrCfg.GameatrDefine.barrageNum, 0));
    this._cutVo.repelDic = e.repelDic + t.getor($2GameatrCfg.GameatrDefine.repel, 0);
    this._cutVo.barrageTime = e.barrageTime + t.getor($2GameatrCfg.GameatrDefine.bulletTime, 0);
    this._cutVo.barrangeSpeed = e.barrangeSpeed * (1 + t.getor($2GameatrCfg.GameatrDefine.barrageSpeed, 0));
    this._cutVo.isPen = e.isPen + t.getor($2GameatrCfg.GameatrDefine.penNum, 0);
    this._cutVo.scale = e.scale * (1 + t.getor($2GameatrCfg.GameatrDefine.scale, 0));
    this._cutVo.cd = e.cd * (1 + t.getor($2GameatrCfg.GameatrDefine.cd, 0)) + t.getor($2GameatrCfg.GameatrDefine.skillcdval, 0);
    this._cutVo.dur = e.dur + t.getor($2GameatrCfg.GameatrDefine.dur, 0);
    this._cutVo.damCd = Math.max(e.damCd + t.getor($2GameatrCfg.GameatrDefine.damcd, 0), .1);
    this._cutVo.releaseCount = (e.releaseCount || 1) + t.getor($2GameatrCfg.GameatrDefine.combo, 0);
    this._cutVo.subRelease = e.subRelease + t.getor($2GameatrCfg.GameatrDefine.subSkillTrigger, 0);
    this._cutVo.dis = e.dis * (1 + t.getor($2GameatrCfg.GameatrDefine.attackarea, 0));
    this._cutVo.reflexCount = e.reflexCount + t.getor($2GameatrCfg.GameatrDefine.reflexCount, 0);
    this.resetSubSkill();
    this._cutVo.buffId.length = 0;
    this._cutVo.buffId = this.curBuffId;
    this.atkNum = this.cutVo.barrageNum * this.cutVo.releaseCount;
  };
  _ctor.prototype.resetSubSkill = function () {
    var e;
    var t = this;
    this.subSkill.length = 0;
    (e = this.subSkill).push.apply(e, this.mgr.analysisData(this.subSkillId, this.owner));
    this.subSkill.forEach(function (e) {
      var o = $2Game.ModeCfg.Skill.get(e.skill.skillCfg.id);
      e.skill.skillCfg.dam = t.skillCfg.dam * o.dam;
      e.canFireNum = t.cutVo.subRelease;
    });
  };
  _ctor.prototype.load = function () {
    this.clearAllBullet();
    this.excuteBuffToEnemy(this._owner, $2GameSeting.GameSeting.Release.OnLoad);
    this.excuteBuffToEnemy(this._owner, $2GameSeting.GameSeting.Release.OnBinding);
    this.mySkeleton = this.owner.mySkeleton;
    var e = this.getBulletVo();
    var t = e.bulletPath;
    $2Manager.Manager.loader.preloadRes(t);
    $2BulletVoPool.BulletVoPool.despawn(e);
    this.curBuffId.forEach(function (e) {
      var t = $2Game.ModeCfg.Buff.get(e[0]);
      t && t.res && $2Manager.Manager.loader.preloadRes(t.res[0]);
    });
  };
  _ctor.prototype.unload = function () {
    this.clearAllBullet();
    this.excuteBuffToEnemy(this._owner, $2GameSeting.GameSeting.Release.OnBinding);
  };
  _ctor.prototype.checkTarget = function () {
    0 == this.cutMagazineNum && (this.cutMagazineNum = this.totalMagazineNum);
    var e = this.targetList;
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  _ctor.prototype.excute = function (e) {
    this.audioID && $2Manager.Manager.audio.playAudio(this.audioID);
    this.bulletTime = this._cutVo.dur + (this._cutVo.barrageNum - 1) * this._cutVo.barrageCd;
    this.isExcuteIng = true;
    this.atkExcuteNum = 0;
    this.owner.node.emit($2ListenID.ListenID.Fight_OnSkill, this, e);
    this.checkSubSkill($2GameSeting.GameSeting.Release.Start, {
      pos: this.launchPoint
    });
    this.excuteBuffToEnemy(this._owner, $2GameSeting.GameSeting.Release.Start);
    this.cutVo.animation && this._owner.mySkeleton.playQueue([this.cutVo.animation, "move"]);
  };
  _ctor.prototype.fire = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
    cc.warn("[" + this.skillCfg.skillClass + "][fire]未定义:" + this.skillCfg.id, this);
  };
  _ctor.prototype.excuteEnd = function () {
    if (this.isExcuteIng) {
      this._cutExcuteNum++;
      if (this._cutExcuteNum < this.cutVo.releaseCount + this.extraReleaseCount) {
        this.releaseTime = this.cutVo.releaseCd;
      } else {
        this.setCoolingTime();
      }
    }
  };
  _ctor.prototype.setCoolingTime = function () {
    var e = this;
    this._cd = 0;
    this._cutExcuteNum = 0;
    this.isExcuteIng = false;
    this.checkSubSkill($2GameSeting.GameSeting.Release.SetCoolingTime, {
      pos: this.launchPoint
    });
    this.excuteBuffToEnemy(this._owner, $2GameSeting.GameSeting.Release.SetCoolingTime);
    this._owner.node.emit($2ListenID.ListenID.Fight_EntityUseSkillEnd, this.cutVo);
    this.subSkill.forEach(function (t) {
      t.canFireNum = isFinite(e.cutVo.subRelease) ? e.cutVo.subRelease : 99;
    });
  };
  _ctor.prototype.excuteBuffToEnemy = function (e, t) {
    var o;
    var i = this;
    undefined === t && (t = $2GameSeting.GameSeting.Release.Hit);
    null === (o = this._cutVo.buffId) || undefined === o || o.forEach(function (o) {
      if (o[1] == t) {
        if (o[2] == $2GameSeting.GameSeting.SpawningPos.Self) {
          i.createBuffTo(i.owner, o[0]);
        } else if (o[2] == $2GameSeting.GameSeting.SpawningPos.ToParent) {
          i.createBuffTo(i.owner.parent, o[0]);
        } else {
          o[2];
          $2GameSeting.GameSeting.SpawningPos.ToBullet;
          i.createBuffTo(e, o[0]);
        }
      }
    });
  };
  _ctor.prototype.createBuffTo = function (e, t) {
    var o;
    var i = $2Game.ModeCfg.Buff.get(t);
    if (!i.weight || $2Game.Game.weightFloat(i.weight)) {
      var n = e.addBuffByData(i);
      if (n) {
        var r = (null === (o = this._owner.buffMgr) || undefined === o ? undefined : o.getSpecificBuffAttr({
          buffID: t
        })) || d;
        n.checkGain(n.cutVo, n._buffCfg, r);
        n.setCaster(this._owner);
      }
    }
  };
  _ctor.prototype.clearAllBullet = function () {
    var e;
    var t;
    for (var o = this.cutBullet.length - 1; o >= 0; o--) {
      null === (e = this.cutBullet[o]) || undefined === e || e.removeEntityToUpdate();
      this.cutBullet.splice(o, 1);
    }
    for (o = this.cutEffect.length - 1; o >= 0; o--) {
      null === (t = this.cutEffect[o]) || undefined === t || t.removeEntityToUpdate();
      this.cutEffect.splice(o, 1);
    }
  };
  Object.defineProperty(_ctor.prototype, "cutCDLimt", {
    get: function () {
      return this._cutVo.cd;
    },
    set: function (e) {
      this._cutVo.cd = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cdSchedule", {
    get: function () {
      return Math.max(0, (this.cutCDLimt - this._cd) / this.cutCDLimt);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isReady", {
    get: function () {
      return !this.isExcuteIng && this._cd >= this.cutCDLimt;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onUpdate = function (e) {
    if (this.isExcuteIng) {
      if (this.releaseTime > 0) {
        this.releaseTime -= e;
        this.releaseTime <= 0 && this.checkTarget();
      } else {
        (this.bulletTime -= e) < 0 && this.excuteEnd();
      }
    } else {
      if (this._cd < this.cutCDLimt) {
        this._cd += e, this._cd >= this.cutCDLimt && this.onReady();
      }
      this.isReady && this.owner.canFire && this.isAuto && this.checkTarget();
    }
  };
  _ctor.prototype.onReady = function () {
    this.mgr.onReady(this.id);
  };
  _ctor.prototype.onBuff = function () {
    this.checkGain();
  };
  _ctor.prototype.getOwner = function () {
    return this._owner;
  };
  _ctor.prototype.setOwner = function (e) {
    this._owner = e;
  };
  _ctor.prototype.getBulletVo = function (e) {
    undefined === e && (e = {});
    this.atkExcuteNum++;
    var t = $2BulletVoPool.BulletVoPool.spawn();
    var o = this.skillBuff;
    var i = this._cutVo;
    t.isForever = false;
    t.lifeTime = i.barrageTime;
    t.hurt.clone(this.hurt);
    t.crossNum = i.isPen;
    t.scale = i.scale;
    t.hitBack = i.repelDic;
    t.belong = this._owner.entityType;
    t.belongSkill = this;
    t.campType = this._owner.campType;
    t.speed = i.barrangeSpeed || 500;
    t.bulletId = o.getor($2GameatrCfg.GameatrDefine.replaceBullet, i.bulletId);
    t.startPos.set(e.startPos || this.owner.bodyPosition);
    e.shootDir && t.shootDir.set(e.shootDir);
    t.startPos.addSelf(this.barrageOffset);
    t.hurt.bulletVo = t;
    for (var n in e) {
      ["startPos", "shootDir"].includes(n) || (t[n] = e[n]);
    }
    this.atkExcuteNum == this.atkNum && this._owner.buffMgr && (t.hurt.critRate += o.getor($2GameatrCfg.GameatrDefine.lastAttackCrit, 0));
    return t;
  };
  _ctor.prototype.spawnBlankBullet = function (e, t, o) {
    var i = this;
    undefined === t && (t = {});
    undefined === o && (o = "Blank");
    return new Promise(function (n) {
      var r = i.getBulletVo(t);
      r.startPos = e;
      if ("Blank" == o) {
        $2Game.Game.Mgr.instance.spawnBullet("entity/fight/Bullet/Bullet_Blank", r).then(function (e) {
          n(e);
        });
      } else {
        $2Game.Game.Mgr.instance.spawnBullet("entity/fight/Bullet/Bullet_" + o, r).then(function (e) {
          n(e);
        });
      }
    });
  };
  Object.defineProperty(_ctor.prototype, "joytPos", {
    get: function () {
      return this._owner.forwardDirection;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.radioScattering = function (e) {
    undefined === e && (e = this.joytPos.clone());
    var t = this._cutVo;
    var o = t.barrageNum;
    var i = e.normalizeSelf();
    if (1 == o) {
      return [i];
    }
    var n = t.barrageAngle;
    var r = $2GameUtil.GameUtil.GetAngle(i) - n * (o - 1) / 2;
    var a = [];
    for (var s = 0; s < o; s++) {
      var c = (r + n * s) * Math.PI / 180;
      a.push(cc.v2(Math.cos(c), Math.sin(c)));
    }
    return a;
  };
  Object.defineProperty(_ctor.prototype, "barrageOffset", {
    get: function () {
      if (1 == this.cutVo.offSet) {
        return cc.Vec2.ZERO;
      } else {
        if (0 == this.cutVo.barrageAngle) {
          return cc.Vec2.ZERO;
        } else {
          return cc.v2($2Game.Game.random(-this.cutVo.barrageAngle, this.cutVo.barrageAngle), $2Game.Game.random(-this.cutVo.barrageAngle, this.cutVo.barrageAngle));
        }
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "radioAngle", {
    get: function () {
      g.length = 0;
      g = $2GameUtil.GameUtil.getRandomInArray([0, 30, 50, 80, 120, 160, 200, 240, 290, 330], this._cutVo.barrageNum);
      var e = $2Game.Game.random(1, 360);
      for (var t = 0; t < g.length; t++) {
        g[t] += e;
      }
      return g;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bisectionAngle", {
    get: function () {
      var e = 360 / this._cutVo.barrageNum;
      var t = [];
      for (var o = 0; o < this._cutVo.barrageNum; o++) {
        t.push(o * e + 0);
      }
      return t;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "rangeRadio", {
    get: function () {
      var e = [];
      for (var t = 0; t < this._cutVo.barrageNum; t++) {
        e.push($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), $2Game.Game.random(this._owner.radius, this.dis)).clone());
      }
      return e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "targetList", {
    get: function () {
      var e;
      var t = this;
      this._targetList.length = 0;
      (e = this._targetList).push.apply(e, [$2GameSeting.GameSeting.SelectType.Nearby, $2GameSeting.GameSeting.SelectType.RandomTarget, $2GameSeting.GameSeting.SelectType.JoytPos].includes(this.cutVo.tartype) ? $2Game.Game.mgr.getTarget({
        target: this._owner,
        radius: this.dis
      }) : []);
      if (this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.Nearby) {
        return this._targetList.splice(0, this.cutVo.barrageNum);
      } else {
        if (this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.RandomTarget) {
          return this._targetList = $2GameUtil.GameUtil.getRandomSDiffInArray(this._targetList, this.cutVo.barrageNum), this._targetList.length > 0 && this._targetList.length < this.cutVo.barrageNum && (this._targetList = Array.from({
            length: this.cutVo.barrageNum
          }, function () {
            return t._targetList[0];
          })), this._targetList;
        } else {
          if (this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.Self) {
            return [this._owner];
          } else {
            if (this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.RandomPos) {
              return this.rangeRadio.map(function (e) {
                return {
                  position: e
                };
              });
            } else {
              if (this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.JoytPos) {
                if (0 != this.cutVo.dis && 0 == this._targetList.length) {
                  return this._targetList;
                } else {
                  return this._targetList.length = 0, this.cutVo.barrageAngle && this.cutVo.barrageNum > 1 ? this.radioScattering().forEach(function (e) {
                    t._targetList.push({
                      position: e.mulSelf(1e5).addSelf(t.launchPoint)
                    });
                  }) : this._targetList = Array(this.cutVo.barrageNum).fill({
                    position: this.launchPoint.add(this.joytPos.mul(1e5))
                  }), this._targetList;
                }
              } else {
                return this._targetList.splice(0, this.cutVo.barrageNum);
              }
            }
          }
        }
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.checkSubSkill = function (e, t) {
    var o = this;
    undefined === t && (t = {});
    this.subSkill.forEach(function (i) {
      if (i.release == e) {
        t.belongSkillID = o.skillCfg.id;
        i.use(t);
      }
    });
  };
  Object.defineProperty(_ctor.prototype, "totalMagazineNum", {
    get: function () {
      return this.cutVo.barrageNum * this.cutVo.releaseCount;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cutMagazineNum", {
    get: function () {
      return this._cutMagazineNum;
    },
    set: function (e) {
      var t;
      this._cutMagazineNum = e;
      null === (t = this.magazineUI) || undefined === t || t.resetState(this._cutMagazineNum, this.totalMagazineNum);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.loadBulletMagazine = function (e) {
    // var t;
    // var o;
    // var i = this;
    // undefined === e && (e = {});
    // e.nodeAttr || (e.nodeAttr = {});
    // (t = e.nodeAttr).parent || (t.parent = this.owner.topEffectBox);
    // (o = e.nodeAttr).position || (o.position = this.owner._haedPosition);
    // $2Manager.Manager.loader.loadPrefab("ui/fight/BulletMagazine", this.game.gameNode).then(function (t) {
    //   if (i.game) {
    //     t.setAttribute(e.nodeAttr);
    //     var o = t.getComponent($2BulletMagazine.default);
    //     i.magazineUI = o;
    //     i.cutMagazineNum = i.totalMagazineNum;
    //   }
    // });
  };
  return cc__decorate([ccp_ccclass("BaseSkill")], _ctor);
}();
exports.BaseSkill = exp_BaseSkill;