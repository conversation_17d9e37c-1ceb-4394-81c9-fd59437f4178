var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BaseSdk = require("BaseSdk");
var $2SelectAlertAdapter = require("SelectAlertAdapter");
var $2GameUtil = require("GameUtil");
var $2Manager = require("Manager");
var $2StorageID = require("StorageID");
var $2Md5 = require("Md5");
var def_WebDev = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.logout = function () {
    cc.game.emit("GameLogOut", true);
  };
  _ctor.prototype.setShareList = function (e) {
    console.log("获取到分享配置", e);
    this.shareList = e;
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    cc.game.setFrameRate(59);
    this.setMeta();
  };
  _ctor.prototype.setMeta = function () {
    [{
      "http-equiv": "Cache-Control",
      content: "no-cache, no-store, must-revalidate"
    }, {
      "http-equiv": "Pragma",
      content: "no-cache"
    }, {
      "http-equiv": "Expires",
      content: "0"
    }].forEach(function (e) {
      var t = document.createElement("meta");
      for (var o in e) {
        t.setAttribute(o, e[o]);
      }
      document.head.appendChild(t);
    });
  };
  _ctor.prototype.share = function (e, t, o, i) {
    var n = {
      title: "提示(模拟分享)",
      desc: $2GameUtil.GameUtil.weightGetValue(this.shareList, "weight").title,
      confirmText: "分享成功",
      cancelText: "分享失败",
      confirm: function () {
        o && o();
      },
      cancel: function () {
        i && i("分享失败");
      }
    };
    $2SelectAlertAdapter.SdkAlertAdapter.showAlert(n);
  };
  _ctor.prototype.login = function () {
    return new Promise(function (e) {
      var t = $2Manager.Manager.storage.getObject($2StorageID.StorageID.UserData, null);
      if (t && t.openId) {
        $2Manager.Manager.vo.openId = t.openId;
      } else {
        cc.log("重置OPENID");
        $2Manager.Manager.vo.openId = $2Md5.default.md5($2StorageID.StorageID.GameTag + "-" + wonderSdk.BMS_APP_NAME + "-" + navigator.userAgent);
      }
      e(true);
    });
  };
  _ctor.prototype.showBannerWithStyle = function () {};
  _ctor.prototype.showBannerWithNode = function () {};
  _ctor.prototype.hideBanner = function () {};
  _ctor.prototype.destroyBanner = function () {};
  _ctor.prototype.showVideoAD = function (e, t) {
    t && t($2BaseSdk.VideoAdCode.COMPLETE, "");
  };
  _ctor.prototype.sendEvent = function () {};
  return _ctor;
}($2BaseSdk.BaseSdk);
exports.default = def_WebDev;