var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ItemController = undefined;
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2AlertManager = require("AlertManager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2ItemModel = require("ItemModel");
var exp_ItemController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.ViewPath = "ui/ModeBackpackHero/M20_Pop_GameRewardView";
    t.setup($2ItemModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "ItemController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.loginFinish, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GetReward, this.onGetReward, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Item_User, this.onItem_UseCall, this);
  };
  _ctor.prototype.loginFinish = function () {
    if (!$2Manager.Manager.vo.knapsackVo.has("newUserReward")) {
      $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, 30);
      $2Manager.Manager.vo.knapsackVo.addGoods("newUserReward", 1, $2GameSeting.GameSeting.GoodsType.System);
    }
  };
  _ctor.prototype.onGetReward = function (e, t) {
    undefined === t && (t = true);
    e.forEach(function (e) {
      if (e.type == $2GameSeting.GameSeting.GoodsType.Money) {
        $2Manager.Manager.vo.knapsackVo.add(e);
      } else if (e.type == $2GameSeting.GameSeting.GoodsType.Fragment) {
        $2ModeBackpackHeroModel.default.instance.addFragment(e.id, e.num), $2ModeBackpackHeroModel.default.instance.setAutoChipChangeUnEquip(e.id);
      }
    });
    if (t) {
      if ($2UIManager.UIManager.getView(this.ViewPath)) {
        $2UIManager.UIManager.OpenInQueue(this.ViewPath, $2MVC.MVC.openArgs().setParam({
          data: e
        }));
      } else {
        $2UIManager.UIManager.Open(this.ViewPath, $2MVC.MVC.openArgs().setParam({
          data: e
        }));
      }
    }
  };
  _ctor.prototype.onItem_UseCall = function (e) {
    var t = $2Cfg.Cfg.CurrencyConfig.get(e.type);
    if ($2Manager.Manager.vo.knapsackVo.has(e.type) < e.val) {
      $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("%s不足", t.name));
    } else {
      $2Manager.Manager.vo.knapsackVo.useUp(e.type, e.val);
      e.call($2GameSeting.GameSeting.ProgressCode.COMPLETE);
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.ItemController = exp_ItemController;