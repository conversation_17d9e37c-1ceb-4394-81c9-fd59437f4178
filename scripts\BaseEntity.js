var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.EntityType = exports.CampType = undefined;
var s;
var c;
var $2FCollider = require("FCollider");
var $2Game = require("Game");
var p = 0;
(function(e) {
    e[e.Not = 0] = "Not";
    e[e.One = 1] = "One";
    e[e.Two = 2] = "Two";
    e[e.Three = 3] = "Three";
})(s = exports.CampType || (exports.CampType = {}));
(function(e) {
    e[e.DefauleEntity = 1 << p++] = "DefauleEntity";
    e[e.Decorate = 1 << p++] = "Decorate";
    e[e.Role = 1 << p++] = "Role";
    e[e.Monster = 1 << p++] = "Monster";
    e[e.Neutrality = 1 << p++] = "Neutrality";
    e[e.Bomb = 1 << p++] = "Bomb";
    e[e.Bullet = 1 << p++] = "Bullet";
    e[e.Goods = 1 << p++] = "Goods";
    e[e.Effect = 1 << p++] = "Effect";
    e[e.Npc = 1 << p++] = "Npc";
    e[e.Pet = 1 << p++] = "Pet";
})(c = exports.EntityType || (exports.EntityType = {}));
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_BaseEntity = function(e) {
    function _ctor() {
        var t = null !== e && e.apply(this, arguments) || this;
        t._id = -1;
        t._isDead = false;
        t.removeTime = 0;
        t._rTime = 0;
        t._isActive = false;
        t.collider = null;
        t.colliderScaleSet = {
            w: 1,
            h: 1
        };
        t._isInit = false;
        t.atkCamp = s.Not;
        t._campType = s.Not;
        t._entityType = c.DefauleEntity;
        t._radius = 32;
        t._haedPosition = cc.v2();
        t._bodyPosition = cc.v2();
        t._position = cc.v2();
        t._isTag = false;
        t.roleDir = 1;
        t._horDir = 1;
        return t;
    }
    var o;
    cc__extends(_ctor, e);
    o = _ctor;
    Object.defineProperty(_ctor.prototype, "ID", {
        get: function() {
            return this._id;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "isDead", {
        get: function() {
            return this._isDead;
        },
        set: function(e) {
            var t;
            this._isDead = e;
            this._rTime = 0;
            null === (t = this.collider) || undefined === t || t.setActive(!e);
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.removeEntityToUpdate = function() {
        var e;
        this.unuse();
        null === (e = this.game) || undefined === e || e.pushToDestroyList(this);
    };
    Object.defineProperty(_ctor.prototype, "isActive", {
        get: function() {
            return this._isActive && !this.isDead && this.isValid;
        },
        set: function(e) {
            this._isActive = e;
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.unuse = function() {
        var e;
        var t;
        null === (e = this.node) || undefined === e || e.targetOff(this.node);
        null === (t = this.node) || undefined === t || t.setActive(false);
        this.cleanEvent();
    };
    _ctor.prototype.reuse = function() {
        this.node.active = true;
    };
    Object.defineProperty(_ctor.prototype, "campType", {
        get: function() {
            return this._campType;
        },
        set: function(e) {
            this._campType = e;
            this.atkCamp = e == s.Two ? s.One : s.Two;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "entityType", {
        get: function() {
            return this._entityType;
        },
        set: function(e) {
            this._entityType = e;
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.init = function() {
        var e = this;
        this._id = o.nextVaildID++;
        this._isInit = true;
        this._isDead = false;
        this._isActive = true;
        this.radius = this.node.width / 2;
        this.entityType != c.Bullet && this.delayByGame(function() {
            var t;
            null === (t = e.collider) || undefined === t || t.setActive(true);
        });
    };
    Object.defineProperty(_ctor.prototype, "radius", {
        get: function() {
            return this._radius;
        },
        set: function(e) {
            this._radius = e;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "haedPosition", {
        get: function() {
            return this.position.add(this._haedPosition);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "bodyPosition", {
        get: function() {
            return this.position.add(this._bodyPosition);
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "position", {
        get: function() {
            cc.Vec2.set(this._position, this.node.x, this.node.y);
            return this._position;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "game", {
        get: function() {
            return $2Game.Game.mgr;
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.setPosition = function(e) {
        Number.isFinite(e.x) && Number.isFinite(e.y) && this.node.setPosition(e);
    };
    Object.defineProperty(_ctor.prototype, "scale", {
        get: function() {
            return this.node.scale;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "angle", {
        get: function() {
            return this.node.angle;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "isTag", {
        get: function() {
            return this._isTag;
        },
        set: function(e) {
            this._isTag = e;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "settingScale", {
        get: function() {
            return 1;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(_ctor.prototype, "horDir", {
        get: function() {
            return this._horDir;
        },
        set: function(e) {
            this._horDir = e;
            this.node && (this.node.children[0].scaleX = this._horDir * Math.abs(this.node.children[0].scaleX) * this.roleDir);
        },
        enumerable: false,
        configurable: true
    });
    _ctor.prototype.onLoad = function() {
        this.collider || (this.collider = this.getComponent($2FCollider.default));
    };
    _ctor.prototype.onUpdate = function(e) {
        this.isDead && (this._rTime += e) > this.removeTime && this.removeEntityToUpdate();
    };
    _ctor.prototype.behit = function() {
        return null;
    };
    _ctor.prototype.schedule = function(t) {
        var o = [];
        for (var i = 1; i < arguments.length; i++) {
            o[i - 1] = arguments[i];
        }
        for (var n = 0; n < o.length; n++) {
            o[n] /= this.game.gameSpeed;
        }
        e.prototype.schedule.apply(this, cc__spreadArrays([t], o));
    };
    _ctor.prototype.delayByGame = function(e, t, o) {
        undefined === t && (t = 0);
        undefined === o && (o = 1);
        return this.game.timeDelay.delay(t, e, null, this, o);
    };
    _ctor.prototype.cleanEvent = function() {
        var e;
        this._isActive = false;
        cc.Tween.stopAllByTarget(this.node);
        null === (e = this.collider) || undefined === e || e.setActive(false);
        this.unscheduleAllCallbacks();
    };
    _ctor.prototype.onDestroy = function() {
        this.cleanEvent();
    };
    _ctor.nextVaildID = 1;
    return o = cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_BaseEntity;