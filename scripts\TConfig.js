Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TConfig = undefined;
var exp_TConfig = function () {
  function _ctor() {
    this._name = "";
    this._keyMap = null;
  }
  _ctor.prototype.initByMap = function (e) {
    this._map;
    this._map = e;
  };
  _ctor.prototype.initByArray = function (e) {
    var t = this;
    if (null == this._map) {
      this._map = {};
      e.forEach(function (e) {
        t._map[e.id] = e;
      });
    }
  };
  _ctor.prototype.tryGet = function (e) {
    if (null != this._map) {
      var t = this._map[e];
      return [null != t, t];
    }
  };
  _ctor.prototype.get = function (e) {
    if (null != this._map) {
      var t = this._map[e];
      null == t && cc.warn("索引无效 cfg id", e);
      return t;
    }
  };
  _ctor.prototype.find = function (e) {
    if (null != this._map) {
      for (var t in this._map) {
        var o = this._map[t];
        var i = true;
        for (var n in e) {
          if (e[n] !== o[n]) {
            i = false;
            break;
          }
        }
        if (i) {
          return o;
        }
      }
      return null;
    }
  };
  _ctor.prototype.findBy = function (e) {
    for (var t in this._map) {
      var o = this._map[t];
      if (e(o)) {
        return o;
      }
    }
  };
  _ctor.prototype.filter = function (e) {
    if (null != this._map) {
      var t = [];
      for (var o in this._map) {
        var i = this._map[o];
        var n = true;
        for (var r in e) {
          if (e[r] !== i[r]) {
            n = false;
            break;
          }
        }
        n && t.push(i);
      }
      t.length;
      return t;
    }
  };
  _ctor.prototype.sort = function (e, t) {
    e.sort(function (e, o) {
      for (var i in t) {
        var n = t[i];
        var r = e[i];
        var a = o[i];
        if (null != r && null != a) {
          return n * (r - a);
        }
      }
      return 0;
    });
  };
  _ctor.prototype.forEach = function (e, t) {
    if (null != this._map) {
      for (var o in this._map) {
        var i = this._map[o];
        e.call(t, i);
      }
    }
  };
  _ctor.prototype.keyMap = function (e, t, o) {
    null == this._keyMap && (this._keyMap = {});
    var i = this._keyMap[e];
    if (null == i) {
      i = {};
      this._keyMap[e] = i;
    }
    var n = i[t];
    if (null == n) {
      n = {};
      i[t] = n;
      for (var r in this._map) {
        var a = this._map[r];
        var s = a[e];
        var c = a[t];
        null != s && null != c && (n[s] = c);
      }
    }
    return n[o];
  };
  _ctor.prototype.getAll = function () {
    if (null != this._map) {
      return this._map;
    }
  };
  _ctor.prototype.getArray = function () {
    var e = [];
    this.forEach(function (t) {
      return e.push(t);
    });
    return e;
  };
  _ctor.prototype.addItems = function (e, t) {
    this.get(e) && cc.warn("id add repeat", e);
    this._map[e] = t;
  };
  return _ctor;
}();
exports.TConfig = exp_TConfig;