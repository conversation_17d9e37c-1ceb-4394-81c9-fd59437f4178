var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LoadingController = undefined;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2LoadingModel = require("LoadingModel");
var exp_LoadingController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2LoadingModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "LoadingController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2NotifyID.NotifyID.Game_LoadingView, this.setLoadingViewVisible, this);
    $2Notifier.Notifier.changeListener(e, $2NotifyID.NotifyID.Game_BanClick, this.setBanClickVisible, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_StartLoadMap, this.setLoadMap, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_EndLoadMap, this.closeLoading, this);
  };
  _ctor.prototype.setLoadMap = function (e, t, o) {
    $2UIManager.UIManager.Open("ui/loading/LoadingMapView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setParam({
      desc: e,
      openCb: t,
      closeCb: o
    }));
  };
  _ctor.prototype.closeLoading = function () {
    var e = $2UIManager.UIManager.getView("ui/loading/LoadingMapView");
    if (e) {
      if (e.isShowFinish) {
        e.hide();
      } else {
        e.setWaitToHideCall(true);
      }
    }
  };
  _ctor.prototype.setLoadingViewVisible = function (e) {
    $2UIManager.UIManager.Open("ui/loading/LoadingView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setParam(e));
  };
  _ctor.prototype.setBanClickVisible = function (e) {
    undefined === e && (e = 1);
    $2UIManager.UIManager.Open("ui/loading/BanClickView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setParam(e));
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.LoadingController = exp_LoadingController;