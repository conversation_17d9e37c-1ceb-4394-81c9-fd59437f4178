var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SoundCfgReader = exports.SoundDefine = undefined;
var $2TConfig = require("TConfig");
(function (e) {
  e[e.bgm_bulletrebound = 99] = "bgm_bulletrebound";
  e[e.bgm_dragon = 100] = "bgm_dragon";
  e[e.bgm_lobby = 101] = "bgm_lobby";
  e[e.bgm_battle = 102] = "bgm_battle";
  e[e.bgm_boss = 103] = "bgm_boss";
  e[e.button_click = 104] = "button_click";
  e[e.reward = 105] = "reward";
  e[e.skill_zj = 106] = "skill_zj";
  e[e.skill_sdl = 107] = "skill_sdl";
  e[e.skill_lj = 108] = "skill_lj";
  e[e.skill_ys = 109] = "skill_ys";
  e[e.skill_jf = 110] = "skill_jf";
  e[e.skill_bb = 111] = "skill_bb";
  e[e.skill_hbz = 112] = "skill_hbz";
  e[e.skill_dcj = 113] = "skill_dcj";
  e[e.skill_bjsf = 114] = "skill_bjsf";
  e[e.boss_appear = 115] = "boss_appear";
  e[e.lvup = 116] = "lvup";
  e[e.box_drop = 117] = "box_drop";
  e[e.passskill_select21 = 118] = "passskill_select21";
  e[e.passskill_select52 = 119] = "passskill_select52";
  e[e.roletry = 120] = "roletry";
  e[e.pettry = 121] = "pettry";
  e[e.gamewin = 122] = "gamewin";
  e[e.gamerevive = 123] = "gamerevive";
  e[e.gamefail = 124] = "gamefail";
  e[e.screw_in = 125] = "screw_in";
  e[e.screw_out = 126] = "screw_out";
  e[e.win = 127] = "win";
  e[e.weather_sunny = 128] = "weather_sunny";
  e[e.weahter_rain = 129] = "weahter_rain";
  e[e.weather_thunder = 130] = "weather_thunder";
  e[e.weather_tinnywinter = 131] = "weather_tinnywinter";
  e[e.weather_winter = 132] = "weather_winter";
  e[e.weather_darknight = 133] = "weather_darknight";
  e[e.sfx_skill_dcj2 = 134] = "sfx_skill_dcj2";
  e[e.sfx_skill_blast = 135] = "sfx_skill_blast";
  e[e.skill_hq = 136] = "skill_hq";
  e[e.skill_hqhit = 137] = "skill_hqhit";
  e[e.sfx_arrow_shoot = 1e3] = "sfx_arrow_shoot";
  e[e.sfx_fire_boom = 1001] = "sfx_fire_boom";
  e[e.sfx_fire_shot = 1002] = "sfx_fire_shot";
  e[e.sfx_laser_shoot = 1003] = "sfx_laser_shoot";
  e[e.sfx_hxb_shoot = 1004] = "sfx_hxb_shoot";
  e[e.sfx_ft_shoot = 1005] = "sfx_ft_shoot";
  e[e.sfx_slj_shoot = 1006] = "sfx_slj_shoot";
  e[e.sfx_ljf = 1007] = "sfx_ljf";
  e[e.sfx_tachi = 1008] = "sfx_tachi";
  e[e.sfx_bulletreound = 1009] = "sfx_bulletreound";
  e[e.sfx_knifereound = 1010] = "sfx_knifereound";
  e[e.sfx_pop_1 = 1011] = "sfx_pop_1";
  e[e.sfx_pop_2 = 1012] = "sfx_pop_2";
  e[e.sfx_dragonappear = 1013] = "sfx_dragonappear";
  e[e.sfx_dragonhit = 1014] = "sfx_dragonhit";
  e[e.sfx_qjcj_bgm = 1015] = "sfx_qjcj_bgm";
  e[e.sfx_qjcj_pro = 1016] = "sfx_qjcj_pro";
  e[e.ui_collectsilverCoin = 2e3] = "ui_collectsilverCoin";
  e[e.ui_purchase_success = 2001] = "ui_purchase_success";
  e[e.ui_receive_reward = 2002] = "ui_receive_reward";
  e[e.ui_equip = 2003] = "ui_equip";
  e[e.ui_mergelv2 = 2004] = "ui_mergelv2";
  e[e.ui_mergelv3 = 2005] = "ui_mergelv3";
  e[e.ui_mergelv4 = 2006] = "ui_mergelv4";
  e[e.ui_addgem = 2007] = "ui_addgem";
  e[e.sfx_skill_laser = 3001] = "sfx_skill_laser";
  e[e.sfx_skill_thunderbolt = 3002] = "sfx_skill_thunderbolt";
  e[e.sfx_skill_stone = 3003] = "sfx_skill_stone";
  e[e.sfx_skill_hose = 3004] = "sfx_skill_hose";
  e[e.sfx_skill_flaming = 3005] = "sfx_skill_flaming";
  e[e.sfx_skill_loong = 3006] = "sfx_skill_loong";
})(exports.SoundDefine || (exports.SoundDefine = {}));
var exp_SoundCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "Sound";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($2TConfig.TConfig);
exports.SoundCfgReader = exp_SoundCfgReader;