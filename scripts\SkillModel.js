var i;
var cc__extends = __extends;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var def_SkillModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o._cutMaxSkillNum = 0;
    o._cutMaxSkillID = 0;
    null == _ctor._instance && (_ctor._instance = o);
    o.changeListener(true);
    return o;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor, "getInstance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.reset = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_AddSkill, this.addSkill, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this, $2Notifier.PriorLowest);
  };
  _ctor.prototype.onOpenGame = function () {
    this.config = $2Game.ModeCfg.Skill.getAll();
  };
  _ctor.prototype.addSkill = function (e) {
    e == this._cutMaxSkillID && this._cutMaxSkillNum++;
  };
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return $2Game.Game.mgr.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cutLevelSkill", {
    get: function () {
      return this._cutLevelSkill;
    },
    set: function (e) {
      this._cutLevelSkill = e;
      this._cutMaxSkillNum = 0;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getSkillPool = function (e) {
    var t = this;
    var o = this.config;
    this.role.skillMgr.skills.length;
    var i;
    var n = [];
    i = [];
    t.role.skillMgr.skills.forEach(function (e) {
      i.push(e.id);
    });
    var a = i;
    var s = function () {
      var o = [];
      if (t.role.skillMgr.isPoolFull) {
        return o;
      } else {
        return e.forEach(function (e) {
          for (var i in a) {
            if (t.getMianID(a[i]) == e) {
              return;
            }
          }
          o.push(e);
        }), o;
      }
    }();
    var c = cc__spreadArrays(s);
    var l = [];
    var u = function (e, i, n, r) {
      undefined === i && (i = []);
      undefined === n && (n = []);
      undefined === r && (r = []);
      var a = e + 1;
      if (o[a]) {
        if (o[a].isAdGet) {
          n.push(a);
        } else {
          i.push(a);
        }
        var s = t.getMaxLvID(e);
        a != s && o[s].isAdGet && r.push(s);
      }
    };
    a.forEach(function (e) {
      return u(e, c, l, n);
    });
    return {
      free: c,
      video: l,
      max: n
    };
  };
  _ctor.prototype.randomSkill = function (e, t, o) {
    var i = this;
    undefined === t && (t = 3);
    undefined === o && (o = 40);
    var n;
    var a = Math.floor(t / 2);
    var s = cc__spreadArrays(e.free);
    var c = t - a;
    var p = cc__spreadArrays(e.video);
    var f = cc__spreadArrays(e.max);
    f.length > 0 && this._cutMaxSkillNum < this._cutLevelSkill.maxLimit && $2Game.Game.random(1, 100) < o && (n = $2GameUtil.GameUtil.getRandomInArray(f, 1)[0]);
    this._cutMaxSkillID = n;
    var h = function () {
      var e = [];
      n && e.push(n);
      for (var t = cc__spreadArrays(p); e.length < c && t.length > 0;) {
        var o = $2Game.Game.random(0, t.length - 1);
        if (i.checkMainIDInArr(t[o], e)) {
          t.splice(o, 1);
        } else {
          e.push(t.splice(o, 1)[0]);
        }
      }
      return e;
    }();
    var d = function () {
      for (var e = []; e.length + h.length < t && s.length > 0;) {
        e.push(s.splice($2Game.Game.random(0, s.length - 1), 1)[0]);
      }
      return e;
    }();
    var g = function () {
      var e = [];
      if (0 != d.length) {
        return e;
      }
      for (var o = i.getLvSort(p); e.length + h.length < t && o.length > 0;) {
        var n = $2GameUtil.GameUtil.weightGetValue(o);
        i.checkMainIDInArr(n.id, h) || e.push(n.id);
        o.splice(o.indexOf(n), 1);
      }
      return e;
    }();
    if (0 == d.length && 0 == g.length && h.length >= 2) {
      var y = this.getLvSort(h);
      g.push(h.splice(h.indexOf(y[0].id), 1)[0]);
    }
    var m = [];
    d.forEach(function (e) {
      return m.push($2Game.ModeCfg.Skill.get(e));
    });
    g.forEach(function (e) {
      var t = $2Game.ModeCfg.Skill.get(e);
      t.isAdGet = 0;
      m.push(t);
    });
    h.forEach(function (e) {
      return m.push($2Game.ModeCfg.Skill.get(e));
    });
    return m.splice(0, t);
  };
  _ctor.prototype.getLvSort = function (e) {
    var t = this;
    var o = [];
    e.forEach(function (e) {
      var i = t.getSkillLv(e);
      o.push({
        id: e,
        lv: i,
        w: 100 * (40 - i * i)
      });
    });
    o.sort(function (e, t) {
      return e.lv - t.lv;
    });
    return o;
  };
  _ctor.prototype.checkMainIDInArr = function (e, t) {
    for (var o = 0; o < t.length; o++) {
      if (this.getMianID(t[o]) == this.getMianID(e)) {
        return true;
      }
    }
    return false;
  };
  _ctor.prototype.getMianID = function (e) {
    return 20 * Math.floor(e / 20);
  };
  _ctor.prototype.getSkillLv = function (e) {
    return e % 20 + 1;
  };
  _ctor.prototype.getMaxLvID = function (e) {
    for (var t = this.getMianID(e) + 19; !this.config[t];) {
      t--;
    }
    return t;
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_SkillModel;