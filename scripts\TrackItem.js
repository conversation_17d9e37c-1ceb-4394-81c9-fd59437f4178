var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2TrackManger = require("TrackManger");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var h = cc.Vec2.ZERO;
var def_TrackItem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.label = null;
    t.icon = null;
    t.bg = null;
    t.arrow = null;
    t.targetList = [];
    t._letf = 0;
    t._searchTime = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    var e = this.node.parent.getContentSize();
    this.winSize = {
      w: .4 * e.width,
      h: .5 * e.height
    };
    this.lockSize = {
      w: .6 * e.width,
      h: .5 * e.height
    };
    this.pointer = this.node.getChildByName("arrow");
  };
  _ctor.prototype.addTarget = function (e) {
    this.targetList.push(e);
    this.searchTarget();
  };
  _ctor.prototype.searchTarget = function () {
    var e;
    var t;
    var o = this;
    this.role = null === (e = $2Game.Game.Mgr.instance.mainRole) || undefined === e ? undefined : e.node;
    this.node.active = this.targetList.length > 0;
    if (this.node.active) {
      for (var i = this.targetList.length - 1; i >= 0; i--) {
        var n = this.targetList[i];
        if (null === (t = n.node) || undefined === t ? undefined : t.isValid) {
          n.d = $2GameUtil.GameUtil.getDistance(this.role.position, n.node.position);
        } else {
          this.targetList.splice(i, 1);
        }
      }
      this.targetList.sort(function (e, t) {
        return e.d - t.d;
      });
      this.cutTarget = this.targetList[0];
      this.targetNode = this.cutTarget.node;
      $2Manager.Manager.loader.loadSpriteAsync(this.cutTarget.icon).then(function (e) {
        o.icon && (o.icon.spriteFrame = e);
      });
      $2Manager.Manager.loader.loadSpriteAsync($2TrackManger.TrackManger.MsgList[this.cutTarget.trackType].bg).then(function (e) {
        o.bg && (o.bg.spriteFrame = e);
      });
      $2Manager.Manager.loader.loadSpriteAsync($2TrackManger.TrackManger.MsgList[this.cutTarget.trackType].arrow).then(function (e) {
        o.arrow && (o.arrow.spriteFrame = e);
      });
    }
  };
  _ctor.prototype.cleanTarget = function (e) {
    var t = this.targetList.indexOf(e);
    this.targetList.splice(t, 1);
  };
  _ctor.prototype.inCamera = function (e) {
    var t = Math.abs(this.role.x - e.x);
    var o = Math.abs(this.role.y - e.y);
    return !(t > this.lockSize.w || o > this.lockSize.h);
  };
  _ctor.prototype.update = function (e) {
    if ((this._searchTime += e) > 1) {
      this._searchTime = 0;
      this.searchTarget();
    }
    this.onPointTo();
  };
  _ctor.prototype.onPointTo = function () {
    if (this.targetNode && this.targetNode.parent) {
      if (this.inCamera(this.targetNode.position)) {
        this.node.opacity = 0;
      } else {
        h = this.targetNode.position.sub(this.role.position);
        var e = $2GameUtil.GameUtil.getDistance(this.targetNode.position, this.role.position);
        var t = $2GameUtil.GameUtil.GetAngle(cc.Vec2.ZERO, h) + 90;
        this.pointer.angle = t;
        h.x = this.getMinMax(-this.winSize.w, h.x, this.winSize.w);
        h.y = this.getMinMax(180 - this.winSize.h, h.y, this.winSize.h - 200);
        this.node.setPosition(h);
        this.node.opacity = 255;
        this.label.string = $2GameUtil.GameUtil.ToWord(e) + "m";
      }
    } else {
      this.cleanTarget(this.cutTarget);
    }
  };
  _ctor.prototype.getMinMax = function (e, t, o) {
    if (t > o) {
      return o;
    } else {
      if (t < e) {
        return e;
      } else {
        return t;
      }
    }
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "label", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "icon", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "bg", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "arrow", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_TrackItem;