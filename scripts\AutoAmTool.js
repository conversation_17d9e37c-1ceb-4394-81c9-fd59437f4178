var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var a;
var $2GameSeting = require("GameSeting");
(function (e) {
  e[e.NOT = 0] = "NOT";
  e[e.FadeIn = 1] = "FadeIn";
  e[e.ScaleIn = 2] = "ScaleIn";
  e[e.Float = 3] = "Float";
  e[e.Breathing = 4] = "Breathing";
  e[e.FadeBreathing = 5] = "FadeBreathing";
  e[e.Rotate = 6] = "Rotate";
  e[e.BigIn = 7] = "BigIn";
  e[e.FadeOut = 8] = "FadeOut";
  e[e.Wobble = 9] = "Wobble";
  e[e.BigOutLoop = 10] = "BigOutLoop";
})(a || (a = {}));
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;
var def_AutoAmTool = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.AmType = a.NOT;
    t.toValue = [];
    t.amTime = .3;
    t.delayTime = 0;
    t.type = $2GameSeting.GameSeting.TweenType.Not;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    this.scheduleOnce(this.loopAm, this.delayTime);
  };
  _ctor.prototype.loopAm = function () {
    switch (this.AmType) {
      case a.Float:
        this.tween(this.node).sequence(cc.tween().by(this.amTime, {
          position: cc.v2(+this.toValue[0] || 0, +this.toValue[1] || 50)
        }, {
          easing: "sineInOut"
        }), cc.tween().by(this.amTime, {
          position: cc.v2(-+this.toValue[0] || 0, -this.toValue[1] || -50)
        }, {
          easing: "sineInOut"
        })).repeatForever().start();
        break;
      case a.Breathing:
        this.tween(this.node).sequence(cc.tween().to(this.amTime, {
          scale: +this.toValue[0] || 1
        }, {
          easing: "sineInOut"
        }), cc.tween().to(this.amTime, {
          scale: +this.toValue[1] || 1.1
        }, {
          easing: "sineInOut"
        })).repeatForever().start();
        break;
      case a.FadeBreathing:
        this.tween(this.node).sequence(cc.tween().to(this.amTime, {
          opacity: 0
        }, {
          easing: "sineInOut"
        }), cc.tween().to(this.amTime, {
          opacity: 255
        }, {
          easing: "sineInOut"
        })).repeatForever().start();
        break;
      case a.Wobble:
        this.tween(this.node).sequence(cc.tween().by(this.amTime, {
          angle: 30
        }, {
          easing: "sineInOut"
        }), cc.tween().by(this.amTime, {
          angle: -30
        }, {
          easing: "sineInOut"
        })).repeatForever().start();
        break;
      case a.Rotate:
        this.tween(this.node).by(this.amTime, {
          angle: -30
        }).repeatForever().start();
        break;
      case a.BigOutLoop:
        this.tween(this.node).sequence(cc.tween().parallel(cc.tween().set({
          scale: 1
        }), cc.tween().to(.3, {
          opacity: 255
        }, {
          easing: "sineInOut"
        })), cc.tween().parallel(cc.tween().to(this.amTime + .2, {
          scale: 3
        }, {
          easing: "sineInOut"
        }), cc.tween().to(this.amTime, {
          opacity: 0
        }, {
          easing: "sineInOut"
        }))).repeatForever().start();
    }
  };
  _ctor.prototype.resetType = function (e) {
    this.AmType = e;
    cc.Tween.stopAllByTarget(this.node);
    this.loopAm();
    this.show();
    return this;
  };
  _ctor.prototype.show = function () {
    switch (this.AmType) {
      case a.FadeIn:
        this.node.opacity = 0;
        this.tween(this.node).delay(this.delayTime).to(this.amTime, {
          opacity: +this.toValue[0] || 255
        }).start();
        break;
      case a.FadeOut:
        this.node.opacity = 255;
        this.tween(this.node).delay(this.delayTime).to(this.amTime, {
          opacity: +this.toValue[0] || 0
        }).start();
        break;
      case a.ScaleIn:
        this.node.scale = 0;
        this.tween(this.node).delay(this.delayTime).to(this.amTime / 2, {
          scale: +this.toValue[0] || 1.2
        }).to(this.amTime / 3, {
          scale: +this.toValue[1] || 1
        }).start();
        break;
      case a.BigIn:
        this.node.scale = 4;
        this.node.opacity = 0;
        this.tween(this.node).delay(this.delayTime).parallel(cc.tween().to(.2, {
          opacity: 255
        }), cc.tween().to(.3, {
          scale: 1
        })).start();
    }
  };
  _ctor.prototype.onEnable = function () {
    this.show();
  };
  _ctor.prototype.tween = function (e) {
    return cc.tween(e).tag(this.type);
  };
  cc__decorate([ccp_property({
    type: cc.Enum(a),
    displayName: "动画类型"
  })], _ctor.prototype, "AmType", undefined);
  cc__decorate([ccp_property({
    type: [cc.Float],
    displayName: "到达指定值"
  })], _ctor.prototype, "toValue", undefined);
  cc__decorate([ccp_property({
    type: cc.Float,
    displayName: "动画时间"
  })], _ctor.prototype, "amTime", undefined);
  cc__decorate([ccp_property({
    type: cc.Float,
    displayName: "等待时间"
  })], _ctor.prototype, "delayTime", undefined);
  cc__decorate([ccp_property({
    type: cc.Enum($2GameSeting.GameSeting.TweenType)
  })], _ctor.prototype, "type", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/AutoAmTool")], _ctor);
}(cc.Component);
exports.default = def_AutoAmTool;