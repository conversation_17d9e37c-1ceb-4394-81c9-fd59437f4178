var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_BuffCardItem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.eventScenc = "";
    t.skillname = null;
    t.desc = null;
    t.tag = null;
    t.icon = null;
    t.isSelectMask = null;
    t.cardBtn = null;
    t.numLabel = null;
    t.videoList = [];
    t.isCanClick = false;
    t.isSelect = false;
    t._onClickCall = function () {};
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "rarity", {
    get: function () {
      return this.config.rarity;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function (e, t, o) {
    var i;
    undefined === o && (o = 0);
    this.config = e;
    this.isADunlock = t;
    this.isCanClick = false;
    var n = this.node.getComByChild(cc.Sprite, "bg");
    var r = this.node.getComByChild(cc.Sprite, "frame");
    var a = $2GameSeting.GameSeting.getRarity(e.rarity);
    if (t) {
      $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/img_buff_xcd", n);
    } else {
      $2Manager.Manager.loader.loadSpriteToSprit(a.buffImg, n);
    }
    $2Manager.Manager.loader.loadSpriteToSprit(a.blockImg, r);
    // $2Manager.Manager.loader.loadSpriteToSprit(a.tagImg, this.tag);
    $2Manager.Manager.loader.loadSpriteToSprit(e.icon, this.icon.getComponent(cc.Sprite));
    this.skillname.node.color = a.color;
    var s = e.skillId && $2Cfg.Cfg.EquipMergeLv.getArray().find(function (t) {
      var o;
      if (null === (o = t.skill) || undefined === o) {
        return undefined;
      } else {
        return o.includes(e.skillId[0]);
      }
    });
    this.node.getChildByName("isExclusive").setActive(!!s && !!s.spine);
    n.node.destroyAllChildren();
    this.cardBtn.getComByChild(cc.Label).string = 1 == t ? "体验" : "免费获得";
    this.skillname.string = this.config.name;
    this.desc.text = e.desc;
    this.SkillRoll(o);
    this.videoList.forEach(function (e) {
      return e.setActive(1 == t);
    });
    // null === (i = this.numLabel) || undefined === i || i.setAttribute({
    //   string: o + 1
    // }).node.setActive(wonderSdk.isWebDev);
  };
  _ctor.prototype.showInfo = function () {
    var e = this;
    this.videoList.forEach(function (t) {
      return t.setActive(1 == e.isADunlock);
    });
    this.isCanClick = true;
  };
  _ctor.prototype.setClick = function (e) {
    if (this.isValid) {
      this.node.getComponent(cc.Button).enabled = true;
      this._onClickCall = e;
    }
  };
  _ctor.prototype.onClick = function () {
    var e = this;
    if (this.isCanClick && !this.isSelect) {
      var t = function () {
        e.isSelect = true;
        cc.tween(e.node).by(.1, {
          y: 30
        }).start();
        cc.tween(e.cardBtn).to(.3, {
          y: 0,
          opacity: 0
        }).start();
        e._onClickCall(e.config);
      };
      if (1 == this.isADunlock) {
        this.sendEvent("click");
        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (o) {
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
          if (o == wonderSdk.VideoAdCode.COMPLETE) {
            e.sendEvent("success");
            e.isCanClick && t();
          }
        });
      } else {
        t();
      }
    }
  };
  _ctor.prototype.SkillRoll = function (e) {
    var t = this;
    cc.tween(this.node).delay(.2 * e).to(.3, {
      scale: 1
    }, {
      easing: cc.easing.backOut
    }).call(function () {
      t.showInfo();
    }).start();
  };
  _ctor.prototype.sendEvent = function (e) {
    $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", cc__assign({
      Type: e,
      Scene: this.eventScenc + "_BuffCard",
      ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
    }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "skillname", undefined);
  cc__decorate([ccp_property(cc.RichText)], _ctor.prototype, "desc", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "tag", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "icon", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "isSelectMask", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "cardBtn", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "numLabel", undefined);
  cc__decorate([ccp_property([cc.Node])], _ctor.prototype, "videoList", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_BuffCardItem;