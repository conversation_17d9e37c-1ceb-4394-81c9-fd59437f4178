var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BulletVoPool = require("BulletVoPool");
var $2Game = require("Game");
var $2GameEffect = require("GameEffect");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_EffectSkeleton = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.endAnimation = "";
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    var e = this;
    this.mySkeleton = this.node.getComByChild(sp.Skeleton) || this.node.getComponent(sp.Skeleton);
    this.mySkeleton.setEventListener(function (t, o) {
      if (o.data.name.includes("attack") && e.myBulletVo) {
        var i = $2BulletVoPool.BulletVoPool.spawn().setAttribute(e.myBulletVo);
        $2Game.Game.Mgr.instance.spawnBullet("entity/fight/Bullet/Bullet_Round", i);
      }
    });
    this.mySkeleton.setCompleteListener(function () {
      var t;
      (null === (t = e.mySkillCfg) || undefined === t ? undefined : t.dur) || e.setDead();
    });
  };
  _ctor.prototype.set = function (e) {
    var t = this;
    this.myBulletVo = e;
    if (this.deadTime > 0) {
      this.endAnimation && this.delayByGame(function () {
        t.mySkeleton.setAnimation(0, t.endAnimation, false);
      }, this.deadTime - .5);
      this.myBulletVo.setAttribute({
        lifeTime: this.deadTime
      });
      $2Game.Game.Mgr.instance.spawnBullet("entity/fight/Bullet/Bullet_Round", this.myBulletVo);
    }
  };
  _ctor.prototype.onEnable = function () {
    this.mySkeleton.setAnimation(0, this.mySkeleton.defaultAnimation, this.mySkeleton.loop);
  };
  _ctor.prototype.unuse = function () {
    this.node.opacity = 0;
    e.prototype.unuse.call(this);
  };
  cc__decorate([ccp_property()], _ctor.prototype, "endAnimation", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameEffect/EffectSkeleton")], _ctor);
}($2GameEffect.default);
exports.default = def_EffectSkeleton;