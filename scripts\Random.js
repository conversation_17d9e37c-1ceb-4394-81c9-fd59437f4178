Object.defineProperty(exports, "__esModule", {
  value: true
});
var def_Random = function () {
  function _ctor() {
    this.iRandomMax = 2e11;
    this._seed = 0;
  }
  Object.defineProperty(_ctor.prototype, "seed", {
    get: function () {
      return this._seed;
    },
    set: function (e) {
      this._seed = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.random = function () {
    this._seed = (9301 * this._seed + 49297) % 233280;
    return this._seed / 233280;
  };
  _ctor.prototype.randomInt = function (e, t) {
    if (undefined === t) {
      t = e;
      e = 0;
    }
    var o = e + this.random() * (t - e);
    return Math.round(o);
  };
  _ctor.prototype.randomDouble = function (e, t) {
    if (undefined === t) {
      t = e;
      e = 0;
    }
    return e + this.random() * (t - e);
  };
  _ctor.prototype.randomRange = function (e) {
    return this.randomInt(0, this.iRandomMax) % e;
  };
  _ctor.prototype.randomOdds = function (e, t) {
    if (this.randomRange(e) < t) {
      return 1;
    } else {
      return 0;
    }
  };
  _ctor.prototype.getRandomSDiffInArray = function (e, t) {
    var o;
    var i = e.length;
    if (e.length < t) {
      return e;
    }
    if (1 == e.length) {
      return e;
    }
    for (var n = 0; n < t; n++) {
      var r = this.randomInt(n, i - 1);
      o = [e[r], e[n]];
      e[n] = o[0];
      e[r] = o[1];
    }
    e.length = t;
  };
  return _ctor;
}();
exports.default = def_Random;