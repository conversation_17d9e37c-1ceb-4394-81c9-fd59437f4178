var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSkeleton = require("GameSkeleton");
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_ContinuousBullet = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    var o;
    e.prototype.setBulletVo.call(this, t);
    1440 == this.vo.belongSkill.skillMainID && (null === (o = this.getComponentInChildren($2GameSkeleton.default)) || undefined === o || o.setAnimation(0, "ready" + [1, 1, 1, 2, 2, 2, 3, 3][this.vo.belongSkill.lv], true));
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/ContinuousBullet")], _ctor);
}($2Bullet.default);
exports.default = def_ContinuousBullet;