var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BuffController = undefined;
var $2MVC = require("MVC");
var $2Game = require("Game");
var $2BuffModel = require("BuffModel");
var exp_BuffController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2BuffModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2BuffModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "BuffController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.Mgr.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function () {};
  return _ctor;
}($2MVC.MVC.MController);
exports.BuffController = exp_BuffController;