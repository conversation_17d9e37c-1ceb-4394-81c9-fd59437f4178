Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AngleSlerp = exports.Intersection = undefined;
var i = cc.v2();
var n = cc.v2();
var r = cc.v2();
var a = cc.v2();
function s(e, t) {
  var o;
  var i;
  o = e.min < t.min ? e.min : t.min;
  i = e.max > t.max ? e.max : t.max;
  return e.max - e.min + (t.max - t.min) < i - o;
}
var exp_Intersection = function () {
  function _ctor() {}
  _ctor.polygonCircle = function (t, o) {
    var i = o.worldPosition;
    if (_ctor.pointInPolygon(i, t)) {
      return true;
    }
    var n = o.worldRadius * o.worldRadius;
    var r = 0;
    for (var a = t.length; r < a; r++) {
      var s = 0 === r ? t[t.length - 1] : t[r - 1];
      var c = t[r];
      if (_ctor.pointLineDistanceSqr(i, s, c, true) < n) {
        return true;
      }
    }
    return false;
  };
  _ctor.pointLineDistanceSqr = function (e, t, o, n) {
    var r;
    var a = o.x - t.x;
    var s = o.y - t.y;
    var c = a * a + s * s;
    var l = ((e.x - t.x) * a + (e.y - t.y) * s) / c;
    r = n ? c ? l < 0 ? t : l > 1 ? o : cc.Vec2.set(i, t.x + l * a, t.y + l * s) : t : cc.Vec2.set(i, t.x + l * a, t.y + l * s);
    return (a = e.x - r.x) * a + (s = e.y - r.y) * s;
  };
  _ctor.circleCircle = function (e, t) {
    cc.Vec2.subtract(a, e.worldPosition, t.worldPosition);
    return a.magSqr() < Math.pow(e.worldRadius + t.worldRadius, 2);
  };
  _ctor.lineLine = function (e, t, o, i) {
    var n = (i.x - o.x) * (e.y - o.y) - (i.y - o.y) * (e.x - o.x);
    var r = (t.x - e.x) * (e.y - o.y) - (t.y - e.y) * (e.x - o.x);
    var a = (i.y - o.y) * (t.x - e.x) - (i.x - o.x) * (t.y - e.y);
    if (0 !== a) {
      var s = n / a;
      var c = r / a;
      if (0 <= s && s <= 1 && 0 <= c && c <= 1) {
        return true;
      }
    }
    return false;
  };
  _ctor.lineRect = function (t, o, s) {
    cc.Vec2.set(i, s.x, s.y);
    cc.Vec2.set(n, s.x, s.yMax);
    cc.Vec2.set(r, s.xMax, s.yMax);
    cc.Vec2.set(a, s.xMax, s.y);
    return !!(_ctor.lineLine(t, o, i, n) || _ctor.lineLine(t, o, n, r) || _ctor.lineLine(t, o, r, a) || _ctor.lineLine(t, o, a, i));
  };
  _ctor.linePolygon = function (t, o, i) {
    var n = i.length;
    for (var r = 0; r < n; ++r) {
      var a = i[r];
      var s = i[(r + 1) % n];
      if (_ctor.lineLine(t, o, a, s)) {
        return true;
      }
    }
    return false;
  };
  _ctor.rectRect = function (e, t) {
    var o = e.x;
    var i = e.y;
    var n = e.x + e.width;
    var r = e.y + e.height;
    var a = t.x;
    var s = t.y;
    var c = t.x + t.width;
    var l = t.y + t.height;
    return o <= c && n >= a && i <= l && r >= s;
  };
  _ctor.rectPolygon = function (t, o) {
    cc.Vec2.set(i, t.x, t.y);
    cc.Vec2.set(n, t.x, t.yMax);
    cc.Vec2.set(r, t.xMax, t.yMax);
    cc.Vec2.set(a, t.xMax, t.y);
    if (_ctor.linePolygon(i, n, o)) {
      return true;
    }
    if (_ctor.linePolygon(n, r, o)) {
      return true;
    }
    if (_ctor.linePolygon(r, a, o)) {
      return true;
    }
    if (_ctor.linePolygon(a, i, o)) {
      return true;
    }
    var s = 0;
    for (var c = o.length; s < c; ++s) {
      if (t.contains(o[s])) {
        return true;
      }
    }
    return !!(_ctor.pointInPolygon(i, o) || _ctor.pointInPolygon(n, o) || _ctor.pointInPolygon(r, o) || _ctor.pointInPolygon(a, o));
  };
  _ctor.pointInPolygon = function (e, t) {
    var o = false;
    var i = e.x;
    var n = e.y;
    var r = t.length;
    var a = 0;
    for (var s = r - 1; a < r; s = a++) {
      var c = t[a].x;
      var l = t[a].y;
      var u = t[s].x;
      var p = t[s].y;
      l > n != p > n && i < (u - c) * (n - l) / (p - l) + c && (o = !o);
    }
    return o;
  };
  _ctor.getNearestPoint = function (e, t) {
    var o = t[0];
    var i = cc.Vec2.squaredDistance(e, o);
    var n = 1;
    for (var r = t.length; n < r; n++) {
      var a = t[n];
      var s = cc.Vec2.squaredDistance(e, a);
      if (s < i) {
        i = s;
        o = a;
      }
    }
    return o;
  };
  _ctor.satPolygonPolygon = function (t, o, n, r) {
    var a = 0;
    for (var c = n.length; a < c; a++) {
      cc.Vec2.set(i, n[a].y, -n[a].x);
      if (s(_ctor.getProjectionPolygon(t, i), _ctor.getProjectionPolygon(o, i))) {
        return false;
      }
    }
    a = 0;
    for (var l = r.length; a < l; a++) {
      cc.Vec2.set(i, r[a].y, -r[a].x);
      if (s(_ctor.getProjectionPolygon(t, i), _ctor.getProjectionPolygon(o, i))) {
        return false;
      }
    }
    return true;
  };
  _ctor.getProjectionPolygon = function (e, t) {
    var o = Number.MAX_SAFE_INTEGER;
    var i = -Number.MAX_SAFE_INTEGER;
    var n = 0;
    for (var r = e.length; n < r; n++) {
      var a = e[n].dot(t);
      o = Math.min(o, a);
      i = Math.max(i, a);
    }
    return {
      min: o,
      max: i
    };
  };
  _ctor.polygonPolygon = function (t, o) {
    var i;
    var n;
    i = 0;
    for (n = t.length; i < n; ++i) {
      var r = t[i];
      var a = t[(i + 1) % n];
      if (_ctor.linePolygon(r, a, o)) {
        return true;
      }
    }
    i = 0;
    for (n = o.length; i < n; ++i) {
      if (_ctor.pointInPolygon(o[i], t)) {
        return true;
      }
    }
    i = 0;
    for (n = t.length; i < n; ++i) {
      if (_ctor.pointInPolygon(t[i], o)) {
        return true;
      }
    }
    return false;
  };
  _ctor.getPoint = function (e, t, o, i, n) {
    n = n || cc.v2();
    var r = e.x;
    var a = e.y;
    var s = t.x;
    var c = t.y;
    var l = o.x;
    var u = o.y;
    var p = i.x;
    var f = i.y;
    var h = ((s - r) * (u - a) - (l - r) * (c - a)) / ((s - r) * (u - f) - (l - p) * (c - a));
    return cc.Vec2.set(n, l + h * (p - l), u + h * (f - u));
  };
  _ctor.getLineSegmentIntersection = function (e, t, o, i) {
    var n = t.x - e.x;
    var r = t.y - e.y;
    var a = i.x - o.x;
    var s = i.y - o.y;
    var c = o.x - e.x;
    var l = o.y - e.y;
    var u = n * s - r * a;
    if (0 === u) {
      return null;
    }
    var p = (c * s - l * a) / u;
    var f = (c * r - l * n) / u;
    if (p < 0 || p > 1 || f < 0 || f > 1) {
      return null;
    } else {
      return cc.v2(e.x + p * n, e.y + p * r);
    }
  };
  _ctor.catmullRomSpline = function (e, t) {
    t = Math.max(0, Math.min(1, t));
    var o = Math.floor(t * (e.length - 1));
    t = t * (e.length - 1) - o;
    var i = e[(o - 1 + e.length) % e.length] || e[0];
    var n = e[o % e.length];
    var r = e[(o + 1) % e.length];
    var a = e[(o + 2) % e.length];
    return {
      x: .5 * (2 * n.x + (-i.x + r.x) * t + (2 * i.x - 5 * n.x + 4 * r.x - a.x) * t * t + (-i.x + 3 * n.x - 3 * r.x + a.x) * t * t * t),
      y: .5 * (2 * n.y + (-i.y + r.y) * t + (2 * i.y - 5 * n.y + 4 * r.y - a.y) * t * t + (-i.y + 3 * n.y - 3 * r.y + a.y) * t * t * t)
    };
  };
  _ctor.distance = function (e, t) {
    return Math.sqrt(Math.pow(e.x - t.x, 2) + Math.pow(e.y - t.y, 2));
  };
  _ctor.insertPointsAtFixedDistance = function (e, t) {
    var o = [];
    var i = 0;
    for (var n = 0; n < e.length - 1; n++) {
      var r = e[n];
      var a = e[n + 1];
      for (var s = this.distance(r, a); i + t <= s;) {
        var c = (i + t) / s;
        var l = r.x + c * (a.x - r.x);
        var u = r.y + c * (a.y - r.y);
        o.push({
          x: +l.toFixed(0),
          y: +u.toFixed(0)
        });
        i += t;
      }
      i -= s;
    }
    o.push(e[e.length - 1]);
    return o;
  };
  _ctor.isConcavePolygon = function (e) {
    var t = [];
    var o = 0;
    for (var i = e.length; o < i; ++o) {
      var n = e[o];
      var r = e[(o + 1) % i];
      var a = cc.v2();
      cc.Vec2.subtract(a, n, r);
      t.push(a);
    }
    var s;
    var c;
    var l = t[0].cross(t[1]) >= 0 ? 1 : -1;
    var u = t.length;
    for (o = 1; o < t.length; o++) {
      s = t[o];
      c = t[(o + 1) % u];
      var p = s.cross(c) >= 0 ? 1 : -1;
      if (l != p) {
        return true;
      }
      l = p;
    }
    return false;
  };
  _ctor.generateMosquitoCoilPath = function (e, t, o, i) {
    undefined === i && (i = true);
    var n = [];
    var r = Math.PI / 180 * (i ? 1 : -1);
    var a = 0;
    for (var s = 0; s < 360 * t; s++) {
      var c = e + Math.floor(s / 360) * o + s % 360 / 360 * o;
      var l = c * Math.cos(a);
      var u = c * Math.sin(a);
      n.push({
        x: l,
        y: u
      });
      a += r;
    }
    return n;
  };
  _ctor.findClosestIndex = function (e, t) {
    var o = Infinity;
    var i = -1;
    e.forEach(function (e, n) {
      var r = Math.sqrt(Math.pow(e.x - t.x, 2) + Math.pow(e.y - t.y, 2));
      if (r < o) {
        o = r;
        i = n;
      }
    });
    return i;
  };
  _ctor.isLineIntersectingCircle = function (e, t, o, i, n, r, a) {
    var s = r - i;
    var c = a - n;
    var l = i - e;
    var u = n - t;
    var p = s * l + c * u;
    return l * l + u * u - p * p / (s * s + c * c) <= o * o;
  };
  return _ctor;
}();
exports.Intersection = exp_Intersection;
var exp_AngleSlerp = function () {
  function e() {}
  e.slerp = function (e, t, o) {
    var i = this.degToRad(e);
    for (var n = this.degToRad(t) - i; n > Math.PI;) {
      n -= 2 * Math.PI;
    }
    for (; n < -Math.PI;) {
      n += 2 * Math.PI;
    }
    var r = i + n * o;
    return this.radToDeg(r) % 360;
  };
  e.degToRad = function (e) {
    return e * (Math.PI / 180);
  };
  e.radToDeg = function (e) {
    return e * (180 / Math.PI);
  };
  return e;
}();
exports.AngleSlerp = exp_AngleSlerp;