var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GameatrCfgReader = exports.GameatrDefine = undefined;
var $2TConfig = require("TConfig");
(function (e) {
  e[e.hp = 1] = "hp";
  e[e.recover = 2] = "recover";
  e[e.roleatk = 3] = "roleatk";
  e[e.cirtdam = 4] = "cirtdam";
  e[e.cirtrate = 5] = "cirtrate";
  e[e.hitrate = 6] = "hitrate";
  e[e.dodge = 7] = "dodge";
  e[e.movespeed = 8] = "movespeed";
  e[e.exp = 9] = "exp";
  e[e.itemarea = 10] = "itemarea";
  e[e.roledef = 11] = "roledef";
  e[e.invincible = 12] = "invincible";
  e[e.modeScale = 13] = "modeScale";
  e[e.monsterOffset = 14] = "monsterOffset";
  e[e.wokermovespeed = 15] = "wokermovespeed";
  e[e.spirit = 16] = "spirit";
  e[e.skilldam = 100] = "skilldam";
  e[e.barrageNum = 101] = "barrageNum";
  e[e.scale = 102] = "scale";
  e[e.cd = 103] = "cd";
  e[e.dur = 104] = "dur";
  e[e.damcd = 105] = "damcd";
  e[e.freeze = 106] = "freeze";
  e[e.rotate = 107] = "rotate";
  e[e.combo = 108] = "combo";
  e[e.buffEffect = 109] = "buffEffect";
  e[e.attackarea = 110] = "attackarea";
  e[e.slayingDamage_30 = 111] = "slayingDamage_30";
  e[e.camera = 112] = "camera";
  e[e.damadd = 113] = "damadd";
  e[e.damdis = 114] = "damdis";
  e[e.beheaded = 115] = "beheaded";
  e[e.dropWood = 116] = "dropWood";
  e[e.getCoinRate = 117] = "getCoinRate";
  e[e.woodChange = 118] = "woodChange";
  e[e.repel = 119] = "repel";
  e[e.penNum = 120] = "penNum";
  e[e.shieldPer = 121] = "shieldPer";
  e[e.buffSubSkill = 122] = "buffSubSkill";
  e[e.roundReward = 123] = "roundReward";
  e[e.buffDam = 124] = "buffDam";
  e[e.hpPer = 125] = "hpPer";
  e[e.shieldBlock = 126] = "shieldBlock";
  e[e.deepHurt = 127] = "deepHurt";
  e[e.bulletTime = 128] = "bulletTime";
  e[e.buffOverlay = 129] = "buffOverlay";
  e[e.ReboundDamPer = 130] = "ReboundDamPer";
  e[e.ReboundDamVal = 131] = "ReboundDamVal";
  e[e.addshieldper = 132] = "addshieldper";
  e[e.barrageSpeed = 133] = "barrageSpeed";
  e[e.repeled = 134] = "repeled";
  e[e.replaceBullet = 135] = "replaceBullet";
  e[e.vampireEffect = 136] = "vampireEffect";
  e[e.sheildLimit = 137] = "sheildLimit";
  e[e.buffWeight = 138] = "buffWeight";
  e[e.replaceSkill = 139] = "replaceSkill";
  e[e.replaceBuff = 140] = "replaceBuff";
  e[e.useSkill = 141] = "useSkill";
  e[e.addObjectBuff = 142] = "addObjectBuff";
  e[e.addSkill = 143] = "addSkill";
  e[e.areaDam = 144] = "areaDam";
  e[e.buffDur = 145] = "buffDur";
  e[e.resistDam = 146] = "resistDam";
  e[e.buffEffectAdd = 147] = "buffEffectAdd";
  e[e.skillCrit = 148] = "skillCrit";
  e[e.skillCritNum = 149] = "skillCritNum";
  e[e.otherValueAdd = 150] = "otherValueAdd";
  e[e.replaceRole = 151] = "replaceRole";
  e[e.buffWeightChange = 152] = "buffWeightChange";
  e[e.subSkillTrigger = 153] = "subSkillTrigger";
  e[e.normal = 200] = "normal";
  e[e.fire = 201] = "fire";
  e[e.ice = 202] = "ice";
  e[e.electricity = 203] = "electricity";
  e[e.wind = 204] = "wind";
  e[e.monsterSplit = 300] = "monsterSplit";
  e[e.monsterAppear = 301] = "monsterAppear";
  e[e.addMana = 400] = "addMana";
  e[e.hpval = 401] = "hpval";
  e[e.recoverval = 402] = "recoverval";
  e[e.roleatkval = 403] = "roleatkval";
  e[e.cirtdamval = 404] = "cirtdamval";
  e[e.ouputManaval = 406] = "ouputManaval";
  e[e.skillcdval = 407] = "skillcdval";
  e[e.shieldval = 408] = "shieldval";
  e[e.reflexCount = 500] = "reflexCount";
  e[e.lastAttackCrit = 501] = "lastAttackCrit";
  e[e.disarm = 502] = "disarm";
  e[e.bagGridCd = 1e3] = "bagGridCd";
  e[e.bagGridDam = 1001] = "bagGridDam";
  e[e.bagGridAppear = 1002] = "bagGridAppear";
  e[e.bagGridCritRate = 1003] = "bagGridCritRate";
  e[e.bagGridCritDam = 1004] = "bagGridCritDam";
  e[e.targetequip = 2e3] = "targetequip";
  e[e.cdequip = 2001] = "cdequip";
  e[e.atkequip = 2002] = "atkequip";
  e[e.hpequip = 2003] = "hpequip";
  e[e.rangeequip = 2004] = "rangeequip";
  e[e.healequip = 2005] = "healequip";
  e[e.shieldequip = 2006] = "shieldequip";
  e[e.silvercoinequip = 2007] = "silvercoinequip";
  e[e.healeratequip = 2008] = "healeratequip";
  e[e.alldam = 3e3] = "alldam";
  e[e.allhp = 3001] = "allhp";
})(exports.GameatrDefine || (exports.GameatrDefine = {}));
var exp_GameatrCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "Gameatr";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($2TConfig.TConfig);
exports.GameatrCfgReader = exp_GameatrCfgReader;