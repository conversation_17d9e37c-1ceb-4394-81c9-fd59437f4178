var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Skill_MCDragonFollowPath = exports.Skill_FollowPath = exports.Skill_Magazine = exports.Skill_Flower = exports.Skill_PowerStorage = exports.Skill_StampSweep = exports.Skill_PosExcute = exports.Skill_ColdAir = exports.Skill_EffectSkill = exports.Skill_SwordSword = exports.Skill_GoldenCudgel = exports.Skill_Continuous = exports.Skill_Tornado = exports.Skill_Whirlwind = exports.Skill_BounceThrowingKnife = exports.Skill_Icicle = exports.Skill_Venom = exports.Skill_Arrows = exports.Skill_LaserRadiationGuard = exports.Skill_Ligature = exports.Skill_LaserAnacampsis = exports.Skill_LaserRadiation = exports.Skill_RangeBomb = exports.Skill_RingFireballs = exports.Skill_Meteorite = exports.Skill_StruckLightning = exports.Skill_MultiBullet = exports.Skill_Sniper = exports.Skill_ParallelFire = exports.Skill_FireOffset = exports.Skill_Multishot = exports.Skill_MagicBall = exports.Skill_NormalChop = exports.Skill_AtkDefault = exports.Skill_Default = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2AutoFollow = require("AutoFollow");
var $2GameatrCfg = require("GameatrCfg");
var $2SoundCfg = require("SoundCfg");
var $2Notifier = require("Notifier");
var $2StateMachine = require("StateMachine");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Intersection = require("Intersection");
var $2MChains = require("MChains");
var $2Game = require("Game");
var $2SkillModule = require("SkillModule");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var b = cc.v2();
var C = [];
var exp_Skill_Default = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.excute = function (t) {
    e.prototype.excute.call(this, t);
  };
  return cc__decorate([ccp_ccclass("Skill_Default")], _ctor);
}($2SkillModule.BaseSkill);
exports.Skill_Default = exp_Skill_Default;
var exp_Skill_AtkDefault = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this, t);
    t.forEach(function (e, t) {
      o.owner.delayByGame(function () {
        o.fire({
          pos: e.position,
          shootDir: o.launchPoint.sub(b).normalizeSelf()
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      startPos: e.pos,
      shootDir: e.shootDir
    });
    this.game.spawnBullet(t.bulletPath, t);
  };
  return cc__decorate([ccp_ccclass("Skill_AtkDefault")], t);
}($2SkillModule.BaseSkill);
exports.Skill_AtkDefault = exp_Skill_AtkDefault;
var exp_Skill_NormalChop = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_zj;
    return t;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    this.radioScattering().forEach(function (e, o) {
      t.owner.delayByGame(function () {
        (b = t._owner.position.add(e.mul(100))).y += t._owner.node.height / 2;
        var o = t.getBulletVo({
          lifeTime: .2,
          speed: 100,
          startPos: b,
          shootDir: e
        });
        $2Game.Game.mgr.spawnBullet(o.bulletPath, o);
      }, t.cutVo.barrageCd * o);
    });
    this.isStartSkill && this.setAm();
  };
  t.prototype.setAm = function () {
    var e;
    var t;
    var o = null === (t = null === (e = this._owner) || undefined === e ? undefined : e.mySkeleton) || undefined === t ? undefined : t.setAnimation(1, this._owner.curStateTag == $2StateMachine.State.Type.IDLE ? "attack01" : "attack02", false);
    o.timeScale = 1 / this.cutVo.cd * 2;
    this.owner.delayByGame(function () {
      o.alpha = .5;
    }, o.animationEnd * o.timeScale * .5);
  };
  return cc__decorate([ccp_ccclass("Skill_NormalChop")], t);
}($2SkillModule.BaseSkill);
exports.Skill_NormalChop = exp_Skill_NormalChop;
var exp_Skill_MagicBall = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      var i = e.position.clone();
      o.owner.delayByGame(function () {
        b = o.launchPoint;
        var e = o.getBulletVo({
          lifeTime: 4,
          speed: o.cutVo.barrangeSpeed,
          startPos: b
        });
        e.shootDir = i.sub(b).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(e.bulletPath, e, {
          opacity: 0
        }).then(function (e) {
          $2Game.Game.tween(e.node).set({
            opacity: 0
          }).to(.1, {
            opacity: 255
          }).start();
        });
      }, o.cutVo.barrageCd * t);
    });
    this.isStartSkill && this.setAm();
  };
  t.prototype.setAm = function () {
    var e;
    var t;
    var o = null === (t = null === (e = this._owner) || undefined === e ? undefined : e.mySkeleton) || undefined === t ? undefined : t.setAnimation(1, this._owner.curStateTag == $2StateMachine.State.Type.IDLE ? "attack01" : "attack02", false);
    if (o) {
      o.timeScale = 1 / this.cutVo.cd * 2;
      o.alpha = 1;
      $2Game.Game.tween(o).to(o.animationEnd * o.timeScale * 2, {
        alpha: 0
      }).start();
    }
  };
  return cc__decorate([ccp_ccclass("Skill_MagicBall")], t);
}($2SkillModule.BaseSkill);
exports.Skill_MagicBall = exp_Skill_MagicBall;
var exp_Skill_Multishot = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this, t);
    t.forEach(function (e, t) {
      var i = e.position.clone();
      o.owner.delayByGame(function () {
        b.set(o.launchPoint);
        var e = o.getBulletVo({
          startPos: b,
          targetPos: i,
          shootDir: i.sub(b).normalizeSelf()
        });
        o.game.spawnBullet(e.bulletPath, e);
      }, o.cutVo.barrageCd * t);
    });
  };
  t.prototype.fire = function (e) {
    var t = this;
    this.game.LatticeElementMap.seekByPos({
      pos: e.pos,
      radius: this.dis,
      targetCamp: [this._owner.atkCamp],
      ignoreID: e.ignoreID
    }).slice(0, this.cutVo.barrageNum).forEach(function (o) {
      var i = t.getBulletVo({
        startPos: e.pos,
        ignore: e.ignoreID
      });
      i.shootDir = o.position.sub(e.pos).normalizeSelf();
      t.game.spawnBullet(i.bulletPath, i);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Multishot")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Multishot = exp_Skill_Multishot;
var exp_Skill_FireOffset = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this, t);
    var i = this.game.scenceSize;
    t.forEach(function (e, t) {
      var n = e.position.clone();
      o.owner.delayByGame(function () {
        b.set(o.launchPoint);
        var e = o.getBulletVo({
          startPos: b.add(cc.v2($2Game.Game.random(i[0], i[1]), 0)),
          shootDir: n.sub(b).normalizeSelf()
        });
        o.game.spawnBullet(e.bulletPath, e).then(function (e) {
          126e3 == o.id && $2Game.Game.tween(e.vo.shootDir).by($2Game.Game.random(5, 10) / 10, {
            x: $2Game.Game.random(-3, 3) / 10
          }).by($2Game.Game.random(5, 10) / 10, {
            x: $2Game.Game.random(-3, 3) / 10
          }).union().repeat(20).start();
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_FireOffset")], t);
}($2SkillModule.BaseSkill);
exports.Skill_FireOffset = exp_Skill_FireOffset;
var exp_Skill_ParallelFire = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.lotId = 1;
    return t;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this, t);
    var i = this.cutVo.offSet * (this.cutVo.barrageNum - 1);
    t.forEach(function (e, t) {
      var n = e.position.sub(o.launchPoint).normalizeSelf();
      o.owner.delayByGame(function () {
        b.set(o.launchPoint);
        o.fire({
          pos: b.add(cc.v2(-i / 2 + o.cutVo.offSet * t, 0)),
          shootDir: n
        });
      }, o.cutVo.barrageCd * t);
    });
    this.lotId++;
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      startPos: e.pos,
      shootDir: e.shootDir
    });
    t.lotId = this.lotId;
    this.game.spawnBullet(t.bulletPath, t);
  };
  return cc__decorate([ccp_ccclass("Skill_ParallelFire")], t);
}($2SkillModule.BaseSkill);
exports.Skill_ParallelFire = exp_Skill_ParallelFire;
var exp_Skill_Sniper = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_dcj;
    t.tempTarget = [];
    t.line = null;
    return t;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    this.line && (this.line.active = false);
    if (!(this.tempTarget.length > 0) || this.tempTarget[0].isValid) {
      if (this.tempTarget.length) {
        return this.excute(this.tempTarget[0]);
      } else {
        return void this.excuteEnd();
      }
    }
    this.excuteEnd();
  };
  t.prototype.load = function () {
    var t = this;
    e.prototype.load.call(this);
    $2Manager.Manager.loader.loadPrefab("entity/fight/Bullet/sniperLine").then(function (e) {
      if (e) {
        t.line = e;
        t.line.active = false;
        t.line.setParent(t._owner.node);
        t.line.setPosition(30, 50);
      }
    });
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Tower_FightRoundTickStart, this.tickChange, this);
  };
  t.prototype.tickChange = function (e) {
    if (this.line) {
      this.line.active = !e;
      e && (this.line.height = 0);
    }
  };
  t.prototype.excuteEnd = function () {
    e.prototype.excuteEnd.call(this);
    this.tempTarget = [];
  };
  t.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (!this.isReady || 0 == this.tempTarget.length) {
      this.tempTarget.length > 0 && this.tempTarget[0].isDead && (this.tempTarget = []);
      if (0 == this.tempTarget.length) {
        this.line && (this.line.active = true);
        this.tempTarget = $2Game.Game.mgr.getTarget({
          target: this._owner,
          radius: this.dis
        });
      } else if (this.line) {
        var o = cc.Vec2.squaredDistance(this._owner.position, this.tempTarget[0].position);
        this.line.height = 4 * Math.sqrt(o);
        var i = this._owner.position.sub(this.tempTarget[0].position);
        this.line.angle = $2GameUtil.GameUtil.GetAngle(i.normalizeSelf()) + 90;
      }
    }
  };
  t.prototype.unload = function () {
    e.prototype.unload.call(this);
    $2Notifier.Notifier.changeListener(false, $2ListenID.ListenID.Tower_FightRoundTickStart, this.tickChange, this);
  };
  t.prototype.excute = function (t) {
    e.prototype.excute.call(this);
    b = this.launchPoint;
    var o = this.getBulletVo({
      lifeTime: 999,
      speed: this.cutVo.barrangeSpeed,
      startPos: b
    });
    o.shootDir = t.position.sub(b).normalizeSelf();
    $2Game.Game.mgr.spawnBullet(o.bulletPath, o);
  };
  t.prototype.fire = function (e) {
    var t = this;
    $2Game.Game.mgr.LatticeElementMap.seekByPos({
      pos: e.pos,
      radius: this.dis,
      targetCamp: [this._owner.atkCamp],
      minRadius: 100
    }).slice(0, this.cutVo.barrageNum).forEach(function (o) {
      b = e.pos;
      var i = t.getBulletVo({
        lifeTime: 999,
        speed: t.cutVo.barrangeSpeed,
        startPos: b
      });
      i.shootDir = o.position.sub(b).normalizeSelf();
      $2Game.Game.mgr.spawnBullet(i.bulletPath, i);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Sniper")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Sniper = exp_Skill_Sniper;
var exp_Skill_MultiBullet = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_dcj;
    return t;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    e.prototype.excute.call(this);
    this.fire({
      pos: t[0].position
    });
  };
  t.prototype.fire = function (e) {
    var t = this;
    this.radioScattering(e.pos).forEach(function (e) {
      var o = t.getBulletVo({
        startPos: t.launchPoint
      });
      o.shootDir = e;
      $2Game.Game.mgr.spawnBullet(o.bulletPath, o);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_MultiBullet")], t);
}($2SkillModule.BaseSkill);
exports.Skill_MultiBullet = exp_Skill_MultiBullet;
var exp_Skill_StruckLightning = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var t = this;
    var o = $2GameUtil.GameUtil.getRandomInArray($2Game.Game.mgr.getTarget({
      target: this._owner,
      radius: this.dis
    }), this.cutVo.barrageNum);
    if (o.length) {
      e.prototype.excute.call(this);
      o.forEach(function (e, o) {
        var i = e.position.clone();
        t.owner.delayByGame(function () {
          var e = t.getBulletVo({
            startPos: i
          });
          $2Game.Game.mgr.spawnBullet(e.bulletPath, e);
        }, t.cutVo.barrageCd * o);
      });
    }
  };
  t.prototype.fire = function (e) {
    this.cutVo.tartype == $2GameSeting.GameSeting.SelectType.RandomPos && e.pos.addSelf($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), $2Game.Game.random(10, this.cutVo.dis)));
    var t = this.getBulletVo({
      startPos: e.pos
    });
    $2Game.Game.mgr.spawnBullet(t.bulletPath, t);
  };
  return cc__decorate([ccp_ccclass("Skill_StruckLightning")], t);
}($2SkillModule.BaseSkill);
exports.Skill_StruckLightning = exp_Skill_StruckLightning;
var exp_Skill_Meteorite = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_ys;
    return t;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      var i = e.position.clone();
      o.owner.delayByGame(function () {
        o.fire({
          pos: i
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      startPos: e.pos
    });
    $2Game.Game.mgr.showEffectByType(t.bulletPath, e.pos, true, -1, {
      scale: this.cutVo.scale
    }).then(function (e) {
      e.set(t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Meteorite")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Meteorite = exp_Skill_Meteorite;
var exp_Skill_RingFireballs = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this, t);
    var i = this.cutVo.barrangeSpeed * (1 + this._owner.buffMgr.getAttr($2GameatrCfg.GameatrDefine.rotate));
    this.bisectionAngle.forEach(function (e) {
      var t = o.getBulletVo({
        speed: i
      });
      $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
        opacity: 0
      }).then(function (t) {
        t.node.setParent(o.showTier);
        t.offset = cc.v2(0, 50);
        t.cutAngle = e;
        $2Game.Game.tween(t.node).to(.1, {
          opacity: 255
        }).start();
        o.cutBullet.push(t);
      });
    });
  };
  return cc__decorate([ccp_ccclass("Skill_RingFireballs")], t);
}($2SkillModule.BaseSkill);
exports.Skill_RingFireballs = exp_Skill_RingFireballs;
var exp_Skill_RangeBomb = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_bb;
    return t;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    e.prototype.excute.call(this);
    this.fire({
      pos: this._owner.position
    });
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      speed: 0,
      startPos: e.pos
    });
    $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
      parent: this.showTier
    });
  };
  return cc__decorate([ccp_ccclass("Skill_RangeBomb")], t);
}($2SkillModule.BaseSkill);
exports.Skill_RangeBomb = exp_Skill_RangeBomb;
var exp_Skill_LaserRadiation = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = this.targetList;
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      var i = o.getBulletVo({});
      o.owner.delayByGame(function () {
        var t = o.launchPoint;
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i).then(function (i) {
          i.node.angle = $2GameUtil.GameUtil.GetAngle(t, e.position) + 90;
          i.atkTaget = e;
          i._maxWidth = 120;
          i.offset.set(o.mgr.launchPoint);
          i.set(o._owner);
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_LaserRadiation")], t);
}($2SkillModule.BaseSkill);
exports.Skill_LaserRadiation = exp_Skill_LaserRadiation;
var exp_Skill_LaserAnacampsis = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = $2Game.Game.mgr.LatticeElementMap.seek(this._owner, this.dis);
    e.sort(function (e, t) {
      return t.curHp - e.curHp;
    });
    if (e.length) {
      return this.excute(e.splice(0, 1));
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      var i = o.getBulletVo({
        lifeTime: o.cutVo.dur
      });
      o.owner.delayByGame(function () {
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i, {
          opacity: 0
        }).then(function (t) {
          t.set({
            startTarget: o._owner,
            endTarget: e,
            firstTarget: e,
            canAnsNum: o.cutVo.barrageNum - 1
          });
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_LaserAnacampsis")], t);
}($2SkillModule.BaseSkill);
exports.Skill_LaserAnacampsis = exp_Skill_LaserAnacampsis;
var exp_Skill_Ligature = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      o.owner.delayByGame(function () {
        o.fire({
          start: o._owner,
          end: e
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      lifeTime: this.cutVo.dur
    });
    if (!e.end) {
      var o = this.game.LatticeElementMap.seekByPos({
        pos: e.start.position,
        targetCamp: t.atkCamp,
        radius: this.dis
      });
      e.end = $2GameUtil.GameUtil.randomArr(o);
    }
    if (e.end) {
      var i = $2GameUtil.GameUtil.GetAngle(e.start.position, e.end.position) + 90;
      $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
        opacity: 0,
        angle: i
      }).then(function (t) {
        t.setTarget(e.start, e.end);
      });
    }
  };
  return cc__decorate([ccp_ccclass("Skill_Ligature")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Ligature = exp_Skill_Ligature;
var exp_Skill_LaserRadiationGuard = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    b = this._owner.position;
    var o = .4 * cc.winSize.width;
    var i = .4 * cc.winSize.height;
    var n = [b.x - o, b.x + o];
    var r = [i - 200, i];
    $2GameUtil.GameUtil.getRandomInArray($2Game.Game.mgr.getTarget({
      target: this._owner,
      radius: this.dis
    }), this.cutVo.barrageNum).forEach(function (e, o) {
      var i = cc.v2($2Game.Game.random(n[0], n[1]), b.y + $2Game.Game.random(r[0], r[1]) * $2GameUtil.GameUtil.getRandomInArray([1, -1], 1)[0]);
      var a = t.getBulletVo({
        lifeTime: t.cutVo.dur,
        speed: 0,
        startPos: i
      });
      t.owner.delayByGame(function () {
        $2Game.Game.mgr.spawnBullet(a.bulletPath, a).then(function (t) {
          t.node.angle = $2GameUtil.GameUtil.GetAngle(a.startPos, e.position) + 90;
          t.isMove = false;
          t._maxWidth = 50;
          t.set(null);
        });
      }, t.cutVo.barrageCd * o);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_LaserRadiationGuard")], t);
}($2SkillModule.BaseSkill);
exports.Skill_LaserRadiationGuard = exp_Skill_LaserRadiationGuard;
var exp_Skill_Arrows = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = $2Game.Game.mgr.getTarget({
      target: this._owner,
      radius: this.dis,
      maxNum: this.cutVo.barrageNum
    });
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e) {
      var t = o.cutVo.dur / o.cutVo.barrageCd;
      var i = e.position;
      $2Game.Game.mgr.showSkillHerald("entity/fight/effect/Effect_Herald", i, 2 * o.cutVo.area + 50, o.cutVo.dur + 1);
      var n = function (e) {
        var t = i.add($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), $2Game.Game.random(0, o.cutVo.area)));
        o.owner.delayByGame(function () {
          $2Game.Game.mgr.showEffectByType("entity/fight/effect/Effect_" + o.skillMainID, t, true, 1).then(function (e) {
            e.set(o.getBulletVo({
              startPos: t
            }));
          });
        }, o.cutVo.barrageCd * e);
      };
      for (var r = 0; r < t; r++) {
        n(r);
      }
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Arrows")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Arrows = exp_Skill_Arrows;
var exp_Skill_Venom = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    e.prototype.excute.call(this);
    this.fire({
      pos: this._owner.position
    });
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      speed: 0,
      startPos: e.pos
    });
    $2Game.Game.mgr.spawnBullet(t.bulletPath, t, {
      parent: this.showTier
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Venom")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Venom = exp_Skill_Venom;
var exp_Skill_Icicle = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = this.targetList;
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    var i = this.launchPoint.clone();
    t.forEach(function (e, t) {
      o.owner.delayByGame(function () {
        var t = o.getBulletVo({
          lifeTime: 2,
          speed: o.cutVo.barrangeSpeed,
          startPos: i
        });
        t.shootDir = e.position.sub(i).normalizeSelf();
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t).then(function (e) {
          $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.skill_hbz);
          e.onCollisionCall = function () {};
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Icicle")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Icicle = exp_Skill_Icicle;
var exp_Skill_BounceThrowingKnife = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = $2Game.Game.mgr.getTarget({
      target: this._owner,
      radius: this.dis,
      maxNum: this.cutVo.barrageNum
    });
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      o.owner.delayByGame(function () {
        (b = o.launchPoint).y += o._owner.node.height / 2;
        var t = o.getBulletVo({
          lifeTime: 5,
          speed: o.cutVo.barrangeSpeed,
          startPos: b
        });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t).then(function (t) {
          t.setTarget(e, o._param[0]);
          cc.Tween.stopAllByTarget(t.node.children[0]);
        });
      }, o.cutVo.barrageCd * t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_BounceThrowingKnife")], t);
}($2SkillModule.BaseSkill);
exports.Skill_BounceThrowingKnife = exp_Skill_BounceThrowingKnife;
var exp_Skill_Whirlwind = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    var o = this.cutVo.barrangeSpeed;
    this.bisectionAngle.forEach(function () {
      var e = t.getBulletVo({
        speed: o,
        lifeTime: t.cutVo.dur
      });
      $2Game.Game.mgr.spawnBullet(e.bulletPath, e).then(function (e) {
        e.offset = cc.v2(0, 50);
      });
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Whirlwind")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Whirlwind = exp_Skill_Whirlwind;
var exp_Skill_Tornado = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_jf;
    return t;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = $2Game.Game.mgr.getTarget({
      target: this._owner,
      radius: this.dis,
      maxNum: this.cutVo.barrageNum
    });
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      o.owner.delayByGame(function () {
        b = e.position.sub(o.launchPoint).normalizeSelf();
        var t = o.getBulletVo({
          shootDir: b
        });
        $2Game.Game.mgr.spawnBullet(t.bulletPath, t).then(function () {});
      }, o.cutVo.barrageCd * t);
    });
  };
  t.prototype.fire = function (e) {
    b.set($2GameUtil.GameUtil.AngleAndLenToPos($2GameUtil.GameUtil.randomArr(this.radioAngle)));
    var t = this.getBulletVo({
      startPos: e.pos,
      shootDir: b
    });
    $2Game.Game.mgr.spawnBullet(t.bulletPath, t);
  };
  return cc__decorate([ccp_ccclass("Skill_Tornado")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Tornado = exp_Skill_Tornado;
var exp_Skill_Continuous = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    e.prototype.excute.call(this);
    this.fire({
      pos: this.launchPoint
    });
  };
  t.prototype.fire = function (e) {
    var t = this;
    var o = this.getBulletVo({
      lifeTime: this.cutVo.dur,
      startPos: e.pos
    });
    $2Game.Game.mgr.spawnBullet(o.bulletPath, o, {
      parent: this.showTier
    }).then(function (e) {
      [1440].includes(t.skillMainID) && e.node.getORaddComponent($2AutoFollow.default).offset.set(t.mgr.launchPoint);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_Continuous")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Continuous = exp_Skill_Continuous;
var exp_Skill_GoldenCudgel = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.audioID = $2SoundCfg.SoundDefine.skill_bjsf;
    return t;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    var o = this.getBulletVo({
      lifeTime: this.cutVo.dur,
      startPos: this._owner.position
    });
    $2Game.Game.mgr.spawnBullet(o.bulletPath, o).then(function (e) {
      cc.Tween.stopAllByTarget(e.node);
      $2Game.Game.tween(e.node).by(.3, {
        angle: t.cutVo.barrangeSpeed
      }).repeatForever().start();
      e.offset.set(t.mgr.launchPoint);
    });
    this.isStartSkill && this.setAm();
  };
  t.prototype.setAm = function () {
    var e;
    var t;
    var o = null === (t = null === (e = this._owner) || undefined === e ? undefined : e.mySkeleton) || undefined === t ? undefined : t.setAnimation(1, this._owner.curStateTag == $2StateMachine.State.Type.IDLE ? "attack01" : "attack02", false);
    o.alpha = 1;
    cc.Tween.stopAllByTarget(o);
    $2Game.Game.tween(o).delay(this.cutVo.dur).to(.3, {
      alpha: 0
    }).start();
  };
  return cc__decorate([ccp_ccclass("Skill_GoldenCudgel")], t);
}($2SkillModule.BaseSkill);
exports.Skill_GoldenCudgel = exp_Skill_GoldenCudgel;
var exp_Skill_SwordSword = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    e.prototype.excute.call(this);
    this.fire();
  };
  t.prototype.fire = function () {
    var e = this;
    var t = this.launchPoint;
    this.radioScattering().forEach(function (o, i) {
      e.owner.delayByGame(function () {
        b = t.add(o.mul(100));
        var i = e.getBulletVo({
          lifeTime: 1,
          speed: 700,
          startPos: b
        });
        i.shootDir = o.clone();
        $2Game.Game.mgr.spawnBullet(i.bulletPath, i);
      }, e.cutVo.barrageCd * i);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_SwordSword")], t);
}($2SkillModule.BaseSkill);
exports.Skill_SwordSword = exp_Skill_SwordSword;
var exp_Skill_EffectSkill = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = this.targetList;
    if (e.length) {
      return this.excute(e.map(function (e) {
        return e.position;
      }));
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      var i = e.clone();
      o.owner.delayByGame(function () {
        o.fire({
          pos: i
        });
      }, o.cutVo.barrageCd * t);
    });
    this.isStartSkill && this.setAm();
  };
  t.prototype.setAm = function () {
    var e;
    var t;
    var o = null === (t = null === (e = this._owner) || undefined === e ? undefined : e.mySkeleton) || undefined === t ? undefined : t.setAnimation(1, "attack02", false);
    o.timeScale = 1 / this.cutVo.cd * 2;
    this.owner.delayByGame(function () {
      o.alpha = .5;
    }, o.animationEnd * o.timeScale * .5);
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      startPos: e.pos,
      size: this.cutVo.area * this.cutVo.scale
    });
    $2Game.Game.mgr.showEffectByType(t.bulletPath, e.pos, true, this.cutVo.dur, {
      parent: this.showTier
    }).then(function (e) {
      e.set(t);
    });
  };
  return cc__decorate([ccp_ccclass("Skill_EffectSkill")], t);
}($2SkillModule.BaseSkill);
exports.Skill_EffectSkill = exp_Skill_EffectSkill;
var exp_Skill_ColdAir = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    this.game.elementMap.forEach(function (e) {
      e.isValid && !e.isDead && (e.isInvincible || (e.campType, t._owner.atkCamp));
    });
  };
  return cc__decorate([ccp_ccclass("Skill_ColdAir")], t);
}($2SkillModule.BaseSkill);
exports.Skill_ColdAir = exp_Skill_ColdAir;
var exp_Skill_PosExcute = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.fireNum = 0;
    return t;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "rangeRadio", {
    get: function () {
      var e = [];
      var t = [this.game.scenceSize[0], this.game.scenceSize[1], this._owner.position.y + this._owner.radius + 100, this._owner.position.y + this.dis];
      for (var o = 0; o < this.cutVo.barrageNum; o++) {
        e.push(cc.v2($2Game.Game.random(t[0], t[1]), [t[3], (t[2] + t[3]) / 2, t[2]][this.fireNum % 3]));
      }
      return e;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.load = function () {
    e.prototype.load.call(this);
    this.fireNum = 0;
  };
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    this.bulletNum > 100 || this.rangeRadio.forEach(function (e, o) {
      t.owner.delayByGame(function () {
        e.y += $2Game.Game.random(-10, 250);
        t.fire({
          pos: e
        });
      }, t.cutVo.barrageCd * o);
    });
  };
  t.prototype.fire = function (e) {
    var t = this;
    var o = this.getBulletVo({
      startPos: this.launchPoint
    });
    $2Game.Game.mgr.spawnBullet(o.bulletPath, o, {
      parent: this.game.botEffectNode,
      active: false
    }).then(function (o) {
      var i = cc.Vec2.distance(e.pos, t.launchPoint) / t.cutVo.barrangeSpeed;
      $2Game.Game.tween(o.node).set({
        active: true
      }).bezierTo(i, t.launchPoint, e.pos.add(cc.v2(0, 50)).multiply(cc.v2(.9, 1)), e.pos).call(function () {
        var e;
        null === (e = o.collider) || undefined === e || e.setActive(true);
        126 == o.vo.bulletId && t.checkLigature(o);
      }).start();
    });
    this.fireNum++;
  };
  t.prototype.checkLigature = function (e) {
    C.length = 0;
    this.game.bulletList.forEach(function (t) {
      if (t.vo.bulletId == e.vo.bulletId) {
        if (!t.isActive) {
          return;
        }
        if (t == e) {
          return;
        }
        var o = cc.Vec2.squaredDistance(e.position, t.position);
        C.push({
          target: t,
          d: o
        });
      }
    });
    C.sort(function (e, t) {
      return e.d - t.d;
    });
    (C = C.splice(0, 3)).forEach(function (t) {
      e.connect.add(t.target);
      e.vo.belongSkill.checkSubSkill($2GameSeting.GameSeting.Release.SetActive, {
        start: e,
        end: t.target
      });
    });
  };
  return cc__decorate([ccp_ccclass("Skill_PosExcute")], t);
}($2SkillModule.BaseSkill);
exports.Skill_PosExcute = exp_Skill_PosExcute;
var exp_Skill_StampSweep = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = this.targetList;
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this);
    t.forEach(function (e, t) {
      var i = e.position.clone();
      o.owner.delayByGame(function () {
        o.fire({
          pos: i
        }, t);
      }, o.cutVo.barrageCd * t);
    });
  };
  t.prototype.fire = function (e, t) {
    var o;
    var i = this;
    if ((null === (o = this._owner.buffMgr) || undefined === o ? undefined : o.isHasID([12600])) || [12620, 6200].includes(this.skillMainID)) {
      var n = this.getBulletVo({
        lifeTime: 2,
        startPos: this.launchPoint
      });
      var r = t % 2 == 0 ? 1 : -1;
      $2Game.Game.mgr.spawnBullet(n.bulletPath, n, {
        opacity: 1
      }).then(function (e) {
        $2Game.Game.tween(e.node).stopLast().set({
          opacity: 0,
          angle: 90 * r,
          scaleX: r * i.cutVo.scale
        }).to(.05, {
          opacity: 255
        }).call(function () {
          var e = "bones/skill/fx_bigsword";
          [12620].includes(i.skillMainID) && (e = "bones/skill/fx_sword5");
          $2Manager.Manager.loader.loadSpineNode(e, {
            nodeAttr: {
              parent: i.game._bulletNode,
              position: i.launchPoint,
              scaleX: r * i.cutVo.scale,
              scaleY: i.cutVo.scale
            },
            spAttr: {
              animation: "animation",
              type: $2GameSeting.GameSeting.TweenType.Game,
              loop: false,
              timeScale: 1.2 / i.game.gameSpeed
            },
            delayRemove: 1
          }, i.game.gameNode);
        }).parallel(cc.tween().to(.2, {
          opacity: 255
        }), cc.tween().by(.5, {
          angle: 180 * (t % 2 == 0 ? -1 : 1)
        }, {
          easing: cc.easing.sineOut
        })).call(function () {
          e.vo.lifeTime = .1;
        }).start();
      });
    } else {
      var s = $2GameUtil.GameUtil.GetAngle(this.launchPoint, e.pos) + 90;
      var c = this.launchPoint.add($2GameUtil.GameUtil.AngleAndLenToPos(s, 200)).clone();
      n = this.getBulletVo({
        lifeTime: 2,
        startPos: this.launchPoint
      });
      $2Game.Game.mgr.spawnBullet(n.bulletPath, n, {
        angle: s,
        opacity: 100,
        scale: .1
      }).then(function (e) {
        $2Game.Game.tween(e.node).set({
          scale: .1,
          angle: s
        }).parallel(cc.tween().to(.2, {
          scale: i.cutVo.scale
        }), cc.tween().to(.2, {
          position: c,
          opacity: 255
        }, {
          easing: cc.easing.sineOut
        })).delay(.1).to(.3, {
          position: i.launchPoint,
          opacity: 0
        }, {
          easing: cc.easing.sineOut
        }).call(function () {
          e.vo.lifeTime = 0;
        }).start();
      });
    }
  };
  return cc__decorate([ccp_ccclass("Skill_StampSweep")], t);
}($2SkillModule.BaseSkill);
exports.Skill_StampSweep = exp_Skill_StampSweep;
var exp_Skill_PowerStorage = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.checkTarget = function () {
    var e = this.targetList;
    if (e.length) {
      return this.excute(e);
    }
    this.excuteEnd();
  };
  t.prototype.excute = function () {
    var t = this;
    e.prototype.excute.call(this);
    this.game.showEffectByPath("bones/skill/fx_arch_addpower", {
      nodeAttr: {
        parent: this.game._bulletNode,
        position: this._owner.bodyPosition
      },
      spAttr: {
        loop: false,
        defaultAnim: "animation",
        isPlayerOnLoad: true
      }
    }).then(function (e) {
      e.setCompleteListener(function () {
        t.targetList.forEach(function (e, o) {
          var i = e.position.clone();
          t.owner.delayByGame(function () {
            b.set(t.launchPoint);
            var e = t.getBulletVo({
              startPos: b,
              targetPos: i
            });
            e.shootDir = i.sub(b).normalizeSelf();
            $2Game.Game.mgr.spawnBullet(e.bulletPath, e);
          }, t.cutVo.barrageCd * o);
        });
        e.node.destroy();
      });
    });
  };
  return cc__decorate([ccp_ccclass("Skill_PowerStorage")], t);
}($2SkillModule.BaseSkill);
exports.Skill_PowerStorage = exp_Skill_PowerStorage;
var exp_Skill_Flower = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.curAngle = 0;
    return t;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    e.prototype.excute.call(this, t);
    this.fire({
      pos: this.owner.bodyPosition
    });
  };
  t.prototype.fire = function (e) {
    var t = this;
    var o = function (o) {
      i.curAngle = (i.curAngle + i.cutVo.barrageAngle) % 360;
      var n = i.curAngle;
      i.owner.delayByGame(function () {
        var o = t.getBulletVo({
          startPos: e.pos,
          shootDir: $2GameUtil.GameUtil.AngleAndLenToPos(n)
        });
        t.game.spawnBullet(o.bulletPath, o);
      }, i.cutVo.barrageCd * o);
    };
    var i = this;
    for (var n = 0; n < this.cutVo.barrageNum; n++) {
      o(n);
    }
  };
  return cc__decorate([ccp_ccclass("Skill_Flower")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Flower = exp_Skill_Flower;
var exp_Skill_Magazine = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function (t) {
    var o = this;
    e.prototype.excute.call(this, t);
    t.forEach(function (e, t) {
      o.owner.delayByGame(function () {
        b.set(o.launchPoint);
        o.fire({
          pos: b,
          shootDir: o.owner.forwardDirection
        });
      }, o.cutVo.barrageCd * t + o.magazineReloadTime);
    });
  };
  t.prototype.fire = function (e) {
    var t = this.getBulletVo({
      startPos: e.pos,
      shootDir: e.shootDir
    });
    this.game.spawnBullet(t.bulletPath, t);
    this.cutMagazineNum--;
  };
  t.prototype.load = function () {
    e.prototype.load.call(this);
    this.loadBulletMagazine();
  };
  t.prototype.onBuff = function (t) {
    var o;
    e.prototype.onBuff.call(this, t);
    null === (o = this.magazineUI) || undefined === o || o.resetState(this.cutMagazineNum, this.totalMagazineNum);
    this.cutVo.cd = Math.max(this.magazineReloadTime, this.cutVo.cd);
  };
  return cc__decorate([ccp_ccclass("Skill_Magazine")], t);
}($2SkillModule.BaseSkill);
exports.Skill_Magazine = exp_Skill_Magazine;
var exp_Skill_FollowPath = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.fire = function () {};
  return cc__decorate([ccp_ccclass("Skill_FollowPath")], t);
}(exp_Skill_AtkDefault);
exports.Skill_FollowPath = exp_Skill_FollowPath;
var exp_Skill_MCDragonFollowPath = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.fire = function (e) {
    var t;
    var o = this;
    if (this.game instanceof $2MChains.MChains.Mgr) {
      var i = this.game.chainsList && (null === (t = this.game.chainsList[0]) || undefined === t ? undefined : t.pathList);
      if (i) {
        var n = $2GameUtil.GameUtil.deepCopy(i).reverse();
        var r = this.getBulletVo({
          startPos: this.launchPoint
        });
        this.game.spawnBullet(r.bulletPath, r).then(function (t) {
          b.set(o.launchPoint).addSelf(e.pos).divSelf(2).addSelf(cc.v2(0, 200));
          var r = $2Intersection.Intersection.findClosestIndex(n, e.pos);
          var a = n.slice(r, i.length);
          var s = cc.Vec2.distance(o.launchPoint, e.pos) / 800;
          $2Game.Game.tween(t.node).bezierTo(s, o.launchPoint, b, e.pos).call(function () {
            t.set(a);
          }).by(.3, {
            y: -50
          }, {
            easing: cc.easing.bounceOut
          }).start();
        });
      }
    }
  };
  return cc__decorate([ccp_ccclass("Skill_MCDragonFollowPath")], t);
}(exp_Skill_AtkDefault);
exports.Skill_MCDragonFollowPath = exp_Skill_MCDragonFollowPath;