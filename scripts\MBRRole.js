var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Buff = require("Buff");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2MBRebound = require("MBRebound");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MBRRole = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._myData = null;
    t.roleId = 30200;
    t.touchPos = cc.v2();
    t.bulletIndex = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
        return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.uiSpine, this.mySkeleton.node).then(function (e) {
          o.mySkeleton.reset(e);
          o.setAnimation("idle", true);
          o.scheduleOnce(function () {
            o.onNewSize(o.roleNode.getContentSize());
          });
        })));
      }
      this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onNewSize = function (t) {
    var o;
    t.mulSelf(.7);
    this.node.setContentSize(t.width, t.height);
    this.collider.size = t;
    this.collider.offset = cc.v2(0, t.height / 2);
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale / 2);
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this.entityType = $2BaseEntity.EntityType.Role;
    this.campType = $2BaseEntity.CampType.One;
    this.bulletIndex = 0;
    this.knifeController = null;
    this.knifeController = new $2MBRebound.MBRebound.KnifeController().setAttribute({
      ower: this
    });
  };
  _ctor.prototype.setRole = function () {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    var e = $2Cfg.Cfg.Role.find({
      roleId: this.roleId
    });
    this.property.set(e);
    this.updateProperty();
    this.skillMgr.add(this.myData.startSkill, false);
    this.initHp();
  };
  _ctor.prototype.changeRole = function (e) {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
  };
  _ctor.prototype.updateProperty = function () {
    this.property && e.prototype.updateProperty.call(this);
  };
  _ctor.prototype.behit = function (e) {
    if (!this.isDead && this.hurtMgr.checkHurt(e)) {
      this.curHp -= e.val;
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      this.materialTwinkle();
      this.mySkeleton.playQueue(["hit", "idle"], true);
      this.game.showDamageDisplay(e, this.haedPosition);
      if (this.curHp <= 0) {
        this.toDead();
        wonderSdk.vibrate(0);
      }
      return e;
    }
  };
  _ctor.prototype.materialTwinkle = function () {};
  _ctor.prototype.toDead = function () {
    if (!this.isDead) {
      this.isDead = true;
      this.game.sendEvent("BatchFail");
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
    }
  };
  Object.defineProperty(_ctor.prototype, "bulletID", {
    get: function () {
      return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;
    },
    enumerable: false,
    configurable: true
  });
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBulletsRebound/MBRRole")], _ctor);
}($2OrganismBase.default);
exports.default = def_MBRRole;