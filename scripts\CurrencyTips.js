var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_CurrencyTips = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.label = null;
    t.spriteframes = [];
    t.currency = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.start = function () {};
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "label", undefined);
  cc__decorate([ccp_property([cc.SpriteFrame])], _ctor.prototype, "spriteframes", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "currency", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_CurrencyTips;