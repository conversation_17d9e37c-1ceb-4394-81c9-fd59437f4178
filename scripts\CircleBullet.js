var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();
var f = cc.v2();
var def_CircleBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.cutAngle = 0;
    t.offset = cc.Vec2.ZERO;
    t._deltaTime = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setBulletVo = function (t) {
    e.prototype.setBulletVo.call(this, t);
    this._deltaTime = 0;
  };
  _ctor.prototype.onUpdate = function (t) {
    if (null != this.cutAngle) {
      this._deltaTime += this.maxSpeed / 400 * t;
      f.set(this.vo.ower.bodyPosition);
      f.addSelf(this.offset);
      p.set($2GameUtil.GameUtil.AngleAndLenToPos(this.cutAngle + 100 * this._deltaTime, this.vo.skillCfg.area));
      p.addSelf(f);
      cc.Vec2.lerp(p, this.node.position, p, this.maxSpeed / 400 * t);
      0 == this.isRotate && (this.node.angle = $2GameUtil.GameUtil.GetAngle(this.node, f) + 15);
      this.setPosition(p);
      e.prototype.onUpdate.call(this, t);
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/CirCleBullet")], _ctor);
}($2BulletBase.default);
exports.default = def_CircleBullet;