var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GameCamera = undefined;
var $2GameSeting = require("GameSeting");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2Game = require("Game");
var exp_GameCamera = function (e) {
  function _ctor(t, o, i) {
    var n = e.call(this) || this;
    n.targetPos = cc.Vec2.ZERO;
    n.offset = 0;
    n.tempOffset = 0;
    n.zoomScaleX = 0;
    n.zoomScaleY = 0;
    n.targetZoom = .7;
    n.radio = 0;
    n._startShake = false;
    n._game = null;
    n._designWidth = 0;
    n._designHeight = 0;
    n.cameraBox = null;
    n.tempBox = cc.rect();
    n.lookPos = cc.v2();
    n._offset = cc.v2(0, 0);
    n.dir = 0;
    n.cutZoomRatio = .9;
    n.startzoom = false;
    n.cameraTween = null;
    n.cameraBox = new cc.Rect();
    n.camera = t;
    n._targetNode = o;
    n._game = i;
    n.tempOffset = n.offset;
    var r = $2Manager.Manager.vo.designSize;
    n._designHeight = r.height;
    n._designWidth = r.width;
    n.cameraBox.width = r.width;
    n.cameraBox.height = r.height;
    return n;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "position", {
    get: function () {
      return this.camera.node.position;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setTargetNode = function (e) {
    this._targetNode = e;
    this.cutZoomRatio = this.camera.zoomRatio;
  };
  _ctor.prototype.lateUpdate = function () {
    this._targetNode && this.lookPos.set(this._targetNode.position);
    this.targetPos.x = cc.misc.lerp(this.targetPos.x, this.lookPos.x, $2Time.Time.deltaTime + .06);
    this.targetPos.y = cc.misc.lerp(this.targetPos.y, this.lookPos.y, $2Time.Time.deltaTime + .06);
    this.moveLimtX && (this.targetPos.x = cc.misc.clampf(this.targetPos.x, this.moveLimtX[0], this.moveLimtX[1]));
    this.moveLimtY && (this.targetPos.y = cc.misc.clampf(this.targetPos.y, this.moveLimtY[0], this.moveLimtY[1]));
    this._startShake && this.targetPos.addSelf(this._offset);
    this.camera.node.position = this.targetPos;
    this.cameraBox.x = this.targetPos.x - .5 * this.cameraBox.width;
    this.cameraBox.y = this.targetPos.y - .5 * this.cameraBox.height;
  };
  _ctor.prototype.setZoomRatio = function (e, t) {
    undefined === t && (t = .3);
    if (!(Math.abs(e - this.cutZoomRatio) < .01)) {
      cc.tween(this.camera).stopLast().to(t, {
        zoomRatio: e
      }).start();
      this.cutZoomRatio = e;
    }
  };
  _ctor.prototype.setMoveLimt = function (e, t) {
    this.moveLimtX = e;
    this.moveLimtY = t;
  };
  _ctor.prototype.isInCamera = function (e, t, o, i) {
    this.tempBox.width = o - e;
    this.tempBox.height = i - t;
    this.tempBox.x = e;
    this.tempBox.y = t;
    return this.cameraBox.intersects(this.tempBox);
  };
  _ctor.prototype.startShake = function (e, t) {
    var o = this;
    undefined === e && (e = 1);
    undefined === t && (t = 1);
    this._startShake = true;
    this._offset = cc.Vec2.ZERO;
    this.dir;
    var i = e;
    cc.Tween.stopAllByTarget(this.camera.node.parent);
    this.cameraTween = $2Game.Game.tween(this.camera.node.parent);
    this.cameraTween.to(.03, {
      position: cc.v2(0 + 8 * i, 0 + 2 * i)
    }).to(.03, {
      position: cc.v2(0 - 3 * i, 0 - 8 * i)
    }).to(.03, {
      position: cc.v2(0 + 2 * i, 0 + 3 * i)
    }).union().repeat(t);
    this.cameraTween.to(.03, {
      x: 0,
      y: 0
    }).call(function () {
      o._startShake = false;
    }).start();
  };
  _ctor.prototype.setPosition = function (e) {
    this.camera.node.position.set(e);
  };
  return _ctor;
}($2GameSeting.GameSeting.CompBase);
exports.GameCamera = exp_GameCamera;