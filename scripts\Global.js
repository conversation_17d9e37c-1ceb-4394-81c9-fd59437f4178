window.isNullOrEmpty = function (e) {
  if (null == e) {
    return true;
  }
  if ("" == e) {
    return true;
  }
  var t = typeof e;
  "string" != t && "number" != t && cc.error("isNullOrEmpty error type", e);
  return false;
};
window.copy = function (e) {
  if (null === e || "object" != typeof e) {
    return e;
  }
  var t = Array.isArray(e) ? [] : {};
  Object.keys(e).forEach(function (o) {
    t[o] = e[o];
  });
  return t;
};
window.toArray = function (e) {
  var t = [];
  if (null === e || "object" != typeof e) {
    return t;
  }
  for (var o in e) {
    var i = e[o];
    t.push(i);
  }
  return t;
};
window.GameDeepCopy = function (e) {
  var t = [];
  return function e(o) {
    if ("object" != typeof o || !o) {
      return o;
    }
    for (var i = 0; i < t.length; i++) {
      if (t[i].target === o) {
        return t[i].copyTarget;
      }
    }
    var n = {};
    Array.isArray(o) && (n = []);
    t.push({
      target: o,
      copyTarget: n
    });
    Object.keys(o).forEach(function (t) {
      n[t] || (n[t] = e(o[t]));
    });
    return n;
  }(e);
};