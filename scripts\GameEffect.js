var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BaseEntity = require("BaseEntity");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_GameEffect = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.deadTime = .3;
    t._isNotDead = false;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.entityType = $2BaseEntity.EntityType.Effect;
    this.deadTime = .3;
  };
  _ctor.prototype.setDead = function () {
    this.isDead || (this.isDead = true);
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this._isNotDead || !this.isDead && this.deadTime > 0 && (this.deadTime -= t, this.deadTime <= 0 && (this.isDead = true));
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2BaseEntity.default);
exports.default = def_GameEffect;