var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Buff = require("Buff");
var $2BaseEntity = require("BaseEntity");
var $2DragonBody = require("DragonBody");
var $2Role = require("Role");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2MCBoss = require("MCBoss");
var $2MChains = require("MChains");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MCRole = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._myData = null;
    t.reliveNum = $2Manager.Manager.vo.switchVo.dragonRevive;
    // t.attackAmList = ["attack", "attack2", "attack3"];
    t.attackAmList = ["gongji"];
    t.damagePanel = new $2GameSeting.TMap();
    t._bossHurtDt = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "settingScale", {
    get: function () {
      return this.property.base.Scale;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "canFire", {
    get: function () {
      return this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.BATTLE;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      // if (e.spine) {
      //   this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
      //   return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
      //     o.mySkeleton.reset(e);
      //     // o.attackAmList = o.mySkeleton.animations.filter(function (e) {
      //     //   return e.includes("attack");
      //     // });
      //     o.delayByGame(function () {
      //       o.onNewSize(o.roleNode.getContentSize());
      //     });
      //     o.firstSkill && (o.firstSkill.mySkeleton = o.mySkeleton);
      //     // o.setAnimation("idle", true);
      //     o.setAnimation("daiji", true);
      //     // if (o.game.passType == $2MChains.MChains.PassType.D360) {
      //     //   o.forwardDirection.setVal(0, -1);
      //     //   o.mySkeleton.setSkin("f");
      //     // } else {
      //     //   o.mySkeleton.setSkin("b");
      //     // }
      //   })));
      // }
      this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    this.node.changeListener(t, $2ListenID.ListenID.Fight_SpawnHurt, this.onSpawnHurt, this);
  };
  _ctor.prototype.onNewSize = function (t) {
    var o;
    this.node.setContentSize(t.width, t.height);
    this.collider.size = t;
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale / 2);
    this.collider.offset = this._bodyPosition;
    if (this.game.passType == $2MChains.MChains.PassType.D360) {
      this._haedPosition.setVal(0, t.height * this.scale * .7);
      this._bodyPosition.setVal(0, 0);
    }
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    this.game.lineNode.setPosition(this.position);
    this.botEffectBox.setAttribute({
      position: this._bodyPosition
    });
    this.topEffectBox.setAttribute({
      position: this._bodyPosition
    });
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this.entityType = $2BaseEntity.EntityType.Role;
    this.campType = $2BaseEntity.CampType.One;
    this.skillMgr.poolLimt = 99;
  };
  _ctor.prototype.setRole = function () {
    var e = this;
    this.roleId = this.game.pathData.roleId || 30300;
    this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    this.property.set($2Cfg.Cfg.Role.find({
      roleId: this.roleId
    }));
    this.game.passParam.isHighDiff && (this.property.base.hp = this.property.cut.hp = .2 * this.property.base.hp);
    this.updateProperty();
    this.myData.startSkill && this.addSkill(this.myData.startSkill, true, true);
    this.myData.startBuff && this.buffMgr.add(this.myData.startBuff);
    this.forwardDirection.setVal(0, 1);
    this.initHp();
    this.game.createLifeBar(this, {}).then(function (t) {
      return t.isPermanent = e.game.passType == $2MChains.MChains.PassType.Move;
    });
  };
  _ctor.prototype.behit = function (e) {
    if (!this.isDead && this.hurtMgr.checkHurt(e)) {
      this.curHp -= e.val;
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      this.materialTwinkle();
      this.game.showDamageDisplay(e, this.haedPosition);
      if (this.curHp <= 0) {
        this.toDead();
        wonderSdk.vibrate(0);
      }
      return e;
    }
  };
  _ctor.prototype.materialTwinkle = function () {
    var e = this;
    if (this._mals) {
      this._mals.setProperty("setwhite", .6);
      this.delayByGame(function () {
        e._mals.setProperty("setwhite", 0);
      }, .1);
    }
  };
  Object.defineProperty(_ctor.prototype, "isCanRelive", {
    get: function () {
      return !this.game.passParam.isHighDiff && this.reliveNum > 0;
    },
    set: function (e) {
      this._isCanRelive = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setJoyStickPos = function (e) {
    this.isDead || this.toMove(e);
  };
  _ctor.prototype.toDead = function () {
    if (!this.isDead) {
      this.node.emit($2ListenID.ListenID.Fight_Dead);
      this.isDead = true;
      this.game.sendEvent("BatchFail");
      if (this.isCanRelive) {
        this.reliveNum--, $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OpenReliveView);
      } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
      }
    }
  };
  _ctor.prototype.relive = function () {
    this.game.clearAllBullet();
    this.buffMgr.clearDebuff();
    this.isDead = false;
    this.initHp();
    this.addBuff(9994);
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
  };
  Object.defineProperty(_ctor.prototype, "attackAm", {
    get: function () {
      if (this.game.passType == $2MChains.MChains.PassType.ForwardMoveExtend) {
        // return "attack";
        return "gongji";
      } else {
        return $2GameUtil.GameUtil.randomArr(this.attackAmList);
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onSkill = function (e) {
    if (e.mySkeleton) {
      // e.mySkeleton.playQueue([this.attackAm, "idle"], true);
      e.mySkeleton.playQueue([this.attackAm, "daiji"], true);
    } else {
      // this.mySkeleton.playQueue([this.attackAm, "idle"], true);
      this.mySkeleton.playQueue([this.attackAm, "daiji"], true);
    }
  };
  _ctor.prototype.addSkill = function (e, t, o) {
    var i = $2GameUtil.GameUtil.deepCopy($2Game.ModeCfg.Skill.get(e));
    var n = $2Cfg.Cfg.EquipLv.getArray().filter(function (t) {
      return t.skill.includes(e);
    });
    if (n.length > 0) {
      var r = $2ModeBackpackHeroModel.default.instance.userEquipPack.getItem(n[0].equipId);
      var s = n.find(function (e) {
        return e.lv == r.lv;
      });
      i.dam = s.atk;
    }
    return this.addSkillByData(i, t, o);
  };
  _ctor.prototype.addSkillByData = function (e, t, o) {
    var i;
    var n;
    var r = this.skillMgr.addByData(e, t, o);
    var a = {
      9100: "buildjg",
      9060: "build03e",
      9040: "build05e",
      91200: "kpbl",
      91220: "kpbl",
      91240: "kpbl",
      91260: "kpbl",
      91600: "sdkpbl",
      91620: "sdkpbl",
      91640: "sdkpbl",
      91660: "sdkpbl"
    };
    a[r.skillCfg.id] && $2Manager.Manager.loader.loadSpineNode("bones/role/" + a[r.skillCfg.id], {
      nodeAttr: {
        parent: this.node,
        x: (null === (i = r.skillCfg.releasePos) || undefined === i ? undefined : i[0]) || 0,
        y: (null === (n = r.skillCfg.releasePos) || undefined === n ? undefined : n[1]) || 0
      },
      spAttr: {
        isPlayerOnLoad: true,
        animation: "idle",
        loop: true
      }
    }).then(function (e) {
      r.mySkeleton = e;
    });
    this.damagePanel.add(r.skillCfg.id, 0);
    return r;
  };
  _ctor.prototype.onSpawnHurt = function (e) {
    e.ownerSkill && this.damagePanel.add(e.ownerSkill.belongSkillID || e.ownerSkill.id, e.val);
  };
  _ctor.prototype.setPosition = function (e) {
    if (this.limitSize) {
      e.x = cc.misc.clampf(e.x, this.limitSize[0], this.limitSize[1]);
      e.y = cc.misc.clampf(e.y, this.limitSize[2], this.limitSize[3]);
    }
    this.node.setPosition(e);
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    var t = e.comp;
    if (t instanceof $2DragonBody.default && t.curIndex) {
      var o = t.owerChains.getHurt();
      this.behit(o);
    } else {
      t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
        hurCd: 1,
        hid: t.ID
      }));
    }
  };
  _ctor.prototype.onCollisionStay = function (e) {
    var t = e.comp;
    t instanceof $2MCBoss.MCBoss && this.behit(t.getHurt().setAttribute({
      hurCd: 1,
      hid: t.ID
    }));
  };
  Object.defineProperty(_ctor.prototype, "horDir", {
    get: function () {
      return this._horDir;
    },
    set: function (e) {
      this._horDir = e;
    },
    enumerable: false,
    configurable: true
  });
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeChains/MCRole")], _ctor);
}($2Role.default);
exports.default = def_MCRole;