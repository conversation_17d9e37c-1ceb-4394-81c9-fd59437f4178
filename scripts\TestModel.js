var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Pool = require("Pool");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2FColliderManager = require("FColliderManager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2AlertManager = require("AlertManager");
var $2GoodsUIItem = require("GoodsUIItem");
var $2RBadgeModel = require("RBadgeModel");
var $2Game = require("Game");
var $2CompManager = require("CompManager");
var $2PropertyVo = require("PropertyVo");
var $2NodePool = require("NodePool");
var $2SettingModel = require("SettingModel");
var $2TaskModel = require("TaskModel");
var def_TestModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    null == _ctor._instance && (_ctor._instance = o);
    window.W = o;
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return $2Game.Game.Mgr.instance.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "entityNode", {
    get: function () {
      var e;
      if (null === (e = $2Game.Game.Mgr.instance) || undefined === e) {
        return undefined;
      } else {
        return e._entityNode;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Boss", {
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetBoss);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Notifier", {
    get: function () {
      return $2Notifier.Notifier;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "GameUtil", {
    get: function () {
      return $2GameUtil.GameUtil;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "SwitchVo", {
    get: function () {
      return $2Manager.Manager.vo.switchVo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "CompManager", {
    get: function () {
      return $2CompManager.default.Instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "vo", {
    get: function () {
      return $2Manager.Manager.vo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "NodePool", {
    get: function () {
      return $2NodePool.NodePool;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Audio", {
    get: function () {
      return $2Manager.Manager.audio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "PoolManager", {
    get: function () {
      return $2Pool.PoolManager;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "ModeBackpackHeroModel", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "UIManager", {
    get: function () {
      return $2UIManager.UIManager.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "AlertManager", {
    get: function () {
      return $2AlertManager.AlertManager;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Cfg", {
    get: function () {
      return $2Cfg.Cfg;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Time", {
    get: function () {
      return $2Time.Time;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "GoodsUI", {
    get: function () {
      return $2GoodsUIItem.default.List;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Manager", {
    get: function () {
      return $2Manager.Manager;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Storage", {
    get: function () {
      return $2Manager.Manager.storage;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Scenc", {
    get: function () {
      return cc.director.getScene();
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "SettingModel", {
    get: function () {
      return $2SettingModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Hurt", {
    get: function () {
      return $2PropertyVo.Hurt;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "FColliderManager", {
    get: function () {
      return $2FColliderManager.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "TaskModel", {
    get: function () {
      return $2TaskModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "MBack", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "RBadgeModel", {
    get: function () {
      return $2RBadgeModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_TestModel;