var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Hurt = exports.Property = undefined;
var r;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2GameatrCfg = require("GameatrCfg");
var $2ObjectPool = require("ObjectPool");
var $2Manager = require("Manager");
var $2Game = require("Game");
cc._decorator.ccclass;
(function (e) {
  var t = new $2GameSeting.TMap();
  e.DefaultData = {
    hp: 0,
    def: 0,
    speed: 0,
    atk: 1,
    atkArea: 100,
    atkSpeed: 1,
    atkInterval: 1,
    MissRatio: 0,
    crit: .01,
    CritNum: 2,
    Picking: 100,
    ExpUp: 1,
    Scale: 1
  };
  var o = function () {
    function o(t, o) {
      undefined === o && (o = e.DefaultData);
      if (o) {
        this.ower = t;
        this.base = Object.assign({}, e.DefaultData);
        this.cut = Object.assign({}, e.DefaultData);
        this.extra = Object.assign({}, e.DefaultData);
        for (var i in this.extra) {
          this.extra[i] = 0;
        }
        this.set(o);
        t.hurtMgr = new r.Mgr(t);
      }
    }
    o.prototype.set = function (t) {
      undefined === t && (t = e.DefaultData);
      if (!t) {
        return this;
      }
      for (var o in t) {
        if (null != this.base[o]) {
          this.base[o] = t[o];
          this.cut[o] = t[o];
        }
      }
      return this;
    };
    o.prototype.updateVo = function () {
      var e;
      var o = (null === (e = this.ower.buffMgr) || undefined === e ? undefined : e.attrMapAll) || t;
      this.cut.speed = Math.max((this.base.speed + this.extra.speed) * (1 + o.getor($2GameatrCfg.GameatrDefine.movespeed, 0)), 0);
      this.cut.hp = (this.base.hp + this.extra.hp) * (1 + o.getor($2GameatrCfg.GameatrDefine.hp, 0));
      this.cut.atk = (this.base.atk + this.extra.atk) * (1 + o.getor($2GameatrCfg.GameatrDefine.roleatk, 0));
      this.cut.atk = (this.base.atk + this.extra.atk + o.getor($2GameatrCfg.GameatrDefine.roleatkval, 0)) * (1 + o.getor($2GameatrCfg.GameatrDefine.roleatk, 0));
      this.cut.crit = this.base.crit + this.extra.crit + o.getor($2GameatrCfg.GameatrDefine.cirtrate, 0);
      this.cut.CritNum = (this.base.CritNum + this.extra.CritNum) * (1 + o.getor($2GameatrCfg.GameatrDefine.cirtdam, 0));
      this.ower.maxSpeed = this.cut.speed;
      this.ower.node.scale = this.ower.settingScale * (1 + o.getor($2GameatrCfg.GameatrDefine.modeScale, 0));
    };
    return o;
  }();
  e.Vo = o;
})(exports.Property || (exports.Property = {}));
(function (e) {
  var t;
  (function (e) {
    e[e.Default = 1] = "Default";
    e[e.Block = 2] = "Block";
    e[e.Miss = 3] = "Miss";
    e[e.Critical = 4] = "Critical";
    e[e.Slash = 5] = "Slash";
  })(t = e.Type || (e.Type = {}));
  e.Pool = new $2ObjectPool.ObjectPool(function () {
    return new o();
  });
  var o = function (e) {
    function o(t) {
      var o = e.call(this) || this;
      o.val = 0;
      o.isCrit = false;
      o.hid = 1;
      o.baseVal = 0;
      o.extraVal = 0;
      o.critValRate = 1;
      o.critRate = 0;
      o.hurCd = 0;
      o.hitBack = 0;
      o.owner = null;
      o.ownerSkill = null;
      o.hasBehitEffect = true;
      o.hitPos = cc.v2();
      t && o.set(t);
      return o;
    }
    cc__extends(o, e);
    o.prototype.unuse = function () {};
    o.prototype.reuse = function () {
      var e = [];
      for (var t = 0; t < arguments.length; t++) {
        e[t] = arguments[t];
      }
    };
    o.prototype.destroy = function () {};
    o.prototype.set = function (e) {
      this.critRate = 0;
      this.hurCd = 0;
      this.hasBehitEffect = true;
      for (var i in e) {
        this[i] = e[i];
      }
      this.val = e.baseVal;
      this.hid = o.HurtBaseID++;
      this.type = e.type || t.Default;
      o.HurtBaseID > 5e10 && (o.HurtBaseID = 1);
      return this;
    };
    o.prototype.clone = function (e) {
      for (var t = 0; t < o.CloneType.length; t++) {
        this[o.CloneType[t]] = e[o.CloneType[t]];
      }
    };
    Object.defineProperty(o.prototype, "isSlash", {
      get: function () {
        return this.type == t.Slash;
      },
      enumerable: false,
      configurable: true
    });
    o.HurtBaseID = 0;
    o.CloneType = ["hid", "baseVal", "critVal", "critRate", "critValRate", "hurCd", "hitBack", "owner", "ownerSkill"];
    return o;
  }($2GameSeting.GameSeting.CompBase);
  e.Data = o;
  var i = cc.Vec2.ZERO;
  var r = function () {
    function e(e) {
      this.list = {};
      this.owner = null;
      this.owner = e;
    }
    e.prototype.checkHurt = function (e) {
      if (this.owner.isInvincible) {
        return null;
      } else {
        if (0 == e.hurCd) {
          return this.onHurt(e);
        } else {
          if (this.list[e.hid]) {
            return null;
          } else {
            return this.list[e.hid] = e.hurCd, this.onHurt(e);
          }
        }
      }
    };
    e.prototype.onHurt = function (e) {
      var o;
      var n;
      var r;
      var a = this;
      var l = 0;
      e.extraVal = 0;
      e.isCrit = $2Game.Game.weightFloat(e.critRate);
      e.isCrit && (e.type = t.Critical);
      if (e.owner.isActive && e.owner.buffMgr) {
        this.owner.curHpProgress <= .3 && (l += e.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.slayingDamage_30, 0));
        cc.Vec2.subtract(i, e.owner.position, this.owner.position);
        var f = i.magSqr();
        e.ownerSkill && e.owner.buffMgr.attrBuffMap.getor($2GameatrCfg.GameatrDefine.damdis, []).forEach(function (t) {
          t.isSpecificSkill && !t.isSpecificSkill.includes(e.ownerSkill.skillMainID) || f >= Math.pow(e.ownerSkill.dis, 2) * t.otherValue[0] && (l += t.attrMap.getor($2GameatrCfg.GameatrDefine.damdis, 0));
        });
        var h = null === (o = e.ownerSkill) || undefined === o ? undefined : o.skillBuffItem.filter(function (e) {
          return e.attrMap.get($2GameatrCfg.GameatrDefine.beheaded);
        });
        null == h || h.forEach(function (t) {
          var o = t.specialMap.find(function (e) {
            return e.type == $2GameatrCfg.GameatrDefine.beheaded;
          }).data;
          a.owner._curHp - (e.baseVal + e.baseVal * l) <= a.owner._curHp * o[0] && t.cutVo.weight >= Math.random() && a.owner.addBuff(o[1]).setCaster(e.owner);
        });
      }
      if (this.owner.buffMgr) {
        if (this.owner.buffMgr.isHasAttr($2GameatrCfg.GameatrDefine.shieldBlock) && $2Game.Game.weightFloat(this.owner.buffMgr.getAttr($2GameatrCfg.GameatrDefine.shieldBlock, 0))) {
          e.val = 0;
          e.type = t.Block;
        }
        l += this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.deepHurt, 0);
        var d = null === (n = this.owner.buffMgr.attrBuffMap.get($2GameatrCfg.GameatrDefine.resistDam)) || undefined === n ? undefined : n[0];
        if (d) {
          d.unuseLayer();
          return null;
        }
      }
      e.extraVal += e.baseVal * l;
      var g = (e.isCrit ? e.critValRate : 1) * (e.baseVal + e.extraVal);
      e.val = Math.ceil(g);
      e.owner.isActive && e.owner.node.emit($2ListenID.ListenID.Fight_SpawnHurt, e, this.owner);
      (null === (r = e.ownerSkill) || undefined === r ? undefined : r.cutVo.soundhitId) && $2Manager.Manager.audio.playAudio(e.ownerSkill.cutVo.soundhitId);
      return {
        type: e.type
      };
    };
    e.prototype.onUpdate = function (e) {
      for (var t in this.list) {
        this.list[t] -= e;
        this.list[t] <= 0 && delete this.list[t];
      }
    };
    return e;
  }();
  e.Mgr = r;
})(r = exports.Hurt || (exports.Hurt = {}));