Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ShareType = exports.VideoAdCode = exports.BaseSdk = undefined;
cc.sys.localStorage;
var exp_BaseSdk = function () {
  function _ctor() {
    this._shareList = [];
    this._appId = "";
    this._openId = "";
    this.bmsVo = null;
    this.isIpEnable = 1;
    this.ip = 0;
  }
  _ctor.prototype.setSuperProperties = function () {};
  _ctor.prototype.setUserProperties = function () {};
  _ctor.prototype.init = function (e) {
    cc.game.on("exit_game", this.exitGame, this);
    e && this.setAppId(e);
  };
  _ctor.prototype.exitGame = function () {
    cc.game.end();
  };
  _ctor.prototype.showBanner = function (e, t) {
    this.showBannerWithStyle(e, {}, t);
  };
  _ctor.prototype.showFullVideoAD = function () {};
  _ctor.prototype.showInsertAd = function () {};
  _ctor.prototype.showSplashAd = function () {};
  _ctor.prototype.showFeedAd = function () {};
  _ctor.prototype.hideFeedAd = function () {};
  _ctor.prototype.showPrivacy = function (e) {
    e && e(true);
  };
  _ctor.prototype.getUserInfo = function (e) {
    return new Promise(function (t) {
      t(null);
      e && e(null);
    });
  };
  _ctor.prototype.setShareList = function () {};
  _ctor.prototype.getAppId = function () {
    return this._appId;
  };
  _ctor.prototype.setAppId = function (e) {
    this._appId = e;
    return this;
  };
  _ctor.prototype.getOpenId = function () {
    return this._openId;
  };
  _ctor.prototype.setOpenId = function (e) {
    this._openId = e;
    return this;
  };
  _ctor.prototype.setBmsVo = function (e) {
    this.bmsVo = e;
    e.ip && (this.ip = e.ip);
  };
  _ctor.prototype.setIpEnable = function (e) {
    this.isIpEnable = e;
  };
  _ctor.prototype.vibrate = function (e) {
    undefined === e && (e = 0);
  };
  _ctor.prototype.createAppBox = function () {};
  _ctor.prototype.showAppBox = function () {};
  _ctor.prototype.preLoadRewardVideo = function () {};
  _ctor.prototype.goRate = function () {};
  _ctor.prototype.setLoginFinish = function () {};
  _ctor.prototype.toPay = function () {};
  _ctor.prototype.toRestorePay = function () {};
  _ctor.prototype.getNativeAdInfo = function () {
    return null;
  };
  _ctor.prototype.showNativeFullVideoAD = function () {};
  _ctor.prototype.nativeAdRefresh = function () {};
  _ctor.prototype.toShareFaceBook = function () {};
  _ctor.prototype.notifyCreateRole = function () {};
  _ctor.prototype.notifyRoleEnterGame = function () {};
  _ctor.prototype.notifyRoleLevelUp = function () {};
  _ctor.prototype.checkContent = function () {
    return Promise.resolve(true);
  };
  _ctor.prototype.getStorageItem = function (e) {
    return cc.sys.localStorage.getItem(e);
  };
  _ctor.prototype.setStorageItem = function (e, t) {
    cc.sys.localStorage.setItem(e, t);
  };
  _ctor.prototype.clearStorage = function () {
    cc.sys.localStorage.clear();
  };
  _ctor.prototype.removeStorageItem = function (e) {
    cc.sys.localStorage.removeItem(e);
  };
  return _ctor;
}();
exports.BaseSdk = exp_BaseSdk;
(function (e) {
  e[e.COMPLETE = 0] = "COMPLETE";
  e[e.NOT_SUPPORT = 1] = "NOT_SUPPORT";
  e[e.NOT_READY = 2] = "NOT_READY";
  e[e.UNKNOW_AdId = 3] = "UNKNOW_AdId";
  e[e.NOT_COMPLITE = 4] = "NOT_COMPLITE";
  e[e.AD_ERROR = 5] = "AD_ERROR";
  e[e.SHOW_SUCCESS = 6] = "SHOW_SUCCESS";
})(exports.VideoAdCode || (exports.VideoAdCode = {}));
(function (e) {
  e[e.SHARE_CHALLENGE = 1] = "SHARE_CHALLENGE";
  e[e.SHARE_GROUP = 2] = "SHARE_GROUP";
  e[e.SHARE_NORMAL = 3] = "SHARE_NORMAL";
  e[e.SHARE_REWARD = 4] = "SHARE_REWARD";
  e[e.SHARE_VICTORY = 5] = "SHARE_VICTORY";
  e[e.SHARE_SORCE = 6] = "SHARE_SORCE";
  e[e.SHARE_RANK = 7] = "SHARE_RANK";
  e[e.SHARE_HELP = 8] = "SHARE_HELP";
  e[e.SHARE_OTHER = 9] = "SHARE_OTHER";
})(exports.ShareType || (exports.ShareType = {}));