var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MonsterState = undefined;
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2StateMachine = require("StateMachine");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var u = cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
cc.v2();
(function (e) {
  var t = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.IDLE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.playAction("idle", true);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      if (t.isInAttackRange() >= 0) {
        t.readyToAttack() && this._parentState.changeState($2StateMachine.State.Type.ATTACK);
        u.set(t.position);
        t.enforceNonPeretration(u);
        t.setPosition(u);
      } else {
        this._parentState.changeState($2StateMachine.State.Type.MOVE);
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.IdleState = t;
  var o = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.MOVE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.playAction("move", true);
      var o = t.isInAttackRange();
      t.steering.resetFlag();
      if (1 == o) {
        t.steering.fleeOn();
      } else {
        t.steering.seekOn();
      }
    };
    t.prototype.onUpdate = function (t, o) {
      if (!this._owner.isBanMove) {
        e.prototype.onUpdate.call(this, t, o);
        if (0 != this._owner.maxSpeed) {
          this.onSteerMove(t, o), 0 == t.isInAttackRange() && (t.readyToAttack() ? this._parentState.changeState($2StateMachine.State.Type.ATTACK) : this._parentState.changeState($2StateMachine.State.Type.IDLE));
        }
      }
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.MoveState = o;
  var i = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._curattackDelta = 0;
      t._attackPoint = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.ATTACK;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      this._curattackDelta = 0;
      this._attackPoint = this._owner.lvCfg.atkInterval;
      t.playAction("attack", false);
      this._owner.isBanMove || t.steering.targetAgent1 && (t.normalAtk ? t.skillMgr.use(t.normalAtk) : t.steering.targetAgent1.behit(t.getHurt()));
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      this._curattackDelta += o;
      if (this._curattackDelta >= this._attackPoint) {
        if (t.isInAttackRange() >= 0) {
          this._parentState.changeState($2StateMachine.State.Type.IDLE);
        } else {
          this._parentState.changeState($2StateMachine.State.Type.MOVE);
        }
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
      t.curAttackDelta = 0;
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.AttackState = i;
  var p = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.DEAD;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.node.emit($2ListenID.ListenID.Fight_Dead);
      this._deltaTime = .2;
      t.isActive = false;
      t.game.killMonsterNum++;
      $2Game.Game.Mgr.instance.showEntityDieEffect(2, {
        position: t.position,
        scale: t.monCfg.Scale
      });
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      if (this._deltaTime >= 0 && !t.isDead) {
        this._deltaTime -= o;
        cc.Vec2.set(u, -t.heading.x, -t.heading.y);
        cc.Vec2.multiplyScalar(u, u, 50 * o);
        cc.Vec2.add(u, u, t.position);
        t.setPosition(u);
        if (this._deltaTime <= 0) {
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_Kill, this._owner.lvCfg, this._owner.monCfg.type, this._owner.position), t.droppedItems(), t.isDead = true;
        }
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.DeadState = p;
  var f = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.GLOBAL;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      t.curAttackDelta += o;
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.GlobalState = f;
  var h = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      t._hitBackDis = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.BEHIT;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t, o) {
      undefined === o && (o = 0);
      e.prototype.onEnter.call(this, t);
      this._deltaTime = o ? .3 : .01;
      this._hitBackDis = o;
      t.Slot01 && t.playAction("hit", false);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      if (this._deltaTime > 0) {
        this._deltaTime -= o;
        cc.Vec2.set(u, -t.heading.x, -t.heading.y);
        var i = t.lvCfg;
        var n = this._hitBackDis;
        t && i && i.repelDicPer && i.repelDicPer.length > 1 && t.curHpProgress < i.repelDicPer[0] && (n = this._hitBackDis * i.repelDicPer[1]);
        cc.Vec2.multiplyScalar(u, u, -n * o);
        cc.Vec2.add(u, u, t.position);
        t.setPosition(u);
        this._deltaTime <= 0 && this._parentState.changeState($2StateMachine.State.Type.MOVE);
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.BeHit = h;
  var d = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      t._curattackDelta = 0;
      t._attackPoint = .5;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.SPRINT;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t, o) {
      e.prototype.onEnter.call(this, t);
      this.tagertPos = o;
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
      u = this._owner.steering.seek(this.tagertPos);
      cc.Vec2.multiplyScalar(u, u, o);
      cc.Vec2.add(u, u, t.velocity);
      $2GameUtil.GameUtil.Vec2Truncate(u, t.maxSpeed);
      t.velocity = u;
      cc.Vec2.multiplyScalar(u, t.velocity, o);
      cc.Vec2.add(u, u, t.position);
      t.enforceNonPeretration(u);
      t.setPosition(u);
      this._curattackDelta += o;
      if (this._curattackDelta >= this._attackPoint && t.isInAttackRange() >= 0) {
        t.steering.targetAgent1.behit(t.getHurt());
        t.playAction("attack", true);
        this._curattackDelta = 0;
      } else if ($2GameUtil.GameUtil.getDistance(this.tagertPos, t.position) < 200) {
        this._parentState.changeState($2StateMachine.State.Type.IDLE), t.isDead = true;
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.Sprint = d;
  var g = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.SKILL;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      t.playAction("idle", true);
      e.prototype.onEnter.call(this, t);
    };
    t.prototype.onUpdate = function (t, o) {
      e.prototype.onUpdate.call(this, t, o);
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.Skill = g;
  var y = function (e) {
    function t() {
      var t = null !== e && e.apply(this, arguments) || this;
      t._deltaTime = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "tag", {
      get: function () {
        return $2StateMachine.State.Type.ESCAPE;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.onEnter = function (t) {
      e.prototype.onEnter.call(this, t);
      t.steering.fleeOn();
      t.maxSpeed = 300;
    };
    t.prototype.onUpdate = function (t, o) {
      if (!t.isBanMove) {
        e.prototype.onUpdate.call(this, t, o);
        this._deltaTime += o;
        if (this._deltaTime >= 2) {
          t.isDead = true;
        } else {
          u = t.position.sub(t.steering.targetAgent1.position).normalize().mul(10 * t.maxSpeed), t.velocity = u, cc.Vec2.multiplyScalar(u, t.velocity, o), cc.Vec2.add(u, u, t.position), t.setPosition(u), t.velocity.magSqr() > 1e-5 && (cc.Vec2.normalize(u, t.velocity), t.heading = u), t.updateDir(o);
        }
      }
    };
    t.prototype.onExit = function (t) {
      e.prototype.onExit.call(this, t);
    };
    return t;
  }($2StateMachine.State.BaseModel);
  e.Escape = y;
})(exports.MonsterState || (exports.MonsterState = {}));