var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2LanguageFun = require("LanguageFun");
var $2Game = require("Game");
var $2NodePool = require("NodePool");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_DialogBox = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.rText = null;
    t._maxWidth = 300;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "string", {
    get: function () {
      return this.rText.string;
    },
    set: function (e) {
      var t = this;
      this.node.scale = 0;
      var o = $2LanguageFun.LanguageFun.check(e);
      this.rText.string = "";
      this.rText.string = o;
      this.scheduleOnce(function () {
        t.rText.maxWidth = t.rText.node.width > t._maxWidth ? t._maxWidth : 0;
        t.rText.node.setAttribute({
          x: -(t.rText.node.width - 20) / 2
        });
        t.node.setAttribute({
          width: t.rText.node.width + 40
        });
      });
      ($2Game.Game.getCutMode() > 0 ? $2Game.Game.tween(this.node) : cc.tween(this.node)).stopLast().to(.3, {
        scale: 1
      }, {
        easing: cc.easing.backOut
      }).delay(3).to(.1, {
        scale: 0
      }).call(function () {
        t.remove();
      }).start();
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.remove = function () {
    $2NodePool.NodePool.despawn(this.node.nodeItem);
  };
  cc__decorate([ccp_property(cc.RichText)], _ctor.prototype, "rText", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_DialogBox;