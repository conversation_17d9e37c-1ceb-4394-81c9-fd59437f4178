var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Game = require("Game");
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_BounceBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._targetPos = cc.Vec2.ZERO;
    t._killNum = 0;
    t._killMax = 5;
    t._uuidIgnore = [];
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.setTarget = function (e, t) {
    this._killNum = 0;
    this._killMax = t;
    this._uuidIgnore = [e.ID];
    this._targetPos = e.position.sub(this.node.position).normalize();
  };
  _ctor.prototype.nextTarget = function () {
    var e = $2Game.Game.Mgr.instance.getTarget({
      target: this,
      radius: 1e3,
      maxNum: 1,
      ignoreID: this._uuidIgnore
    })[0];
    if (e) {
      this._uuidIgnore = [e.ID];
      this._targetPos = e.position.sub(this.node.position).normalize();
    }
  };
  _ctor.prototype.setOver = function () {
    this._vo.lifeTime = 0;
    this._killNum = 0;
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this.isDead || this.setPosition(this.node.position.add(this._targetPos.mul(this.maxSpeed * t)));
  };
  _ctor.prototype.onCollisionEnter = function (t, o) {
    if (!this.isDead) {
      if (t.comp && this._lastID != t.comp.ID && e.prototype.onCollisionEnter.call(this, t, o) && (this.nextTarget(), this._killNum++, this._lastID = t.comp.ID, this._killNum >= this._killMax)) {
        return this.setOver();
      } else {
        return undefined;
      }
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/BounceBullet")], _ctor);
}($2Bullet.default);
exports.default = def_BounceBullet;