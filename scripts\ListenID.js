Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ListenID = undefined;
(function (e) {
  e[e._Start = 999] = "_Start";
  e[e.Game_UpdateGold = 1e3] = "Game_UpdateGold";
  e[e.Game_UpdatePower = 1001] = "Game_UpdatePower";
  e[e.Game_SecondDay = 1002] = "Game_SecondDay";
  e[e.Game_StartLoadMap = 1003] = "Game_StartLoadMap";
  e[e.Game_EndLoadMap = 1004] = "Game_EndLoadMap";
  e[e.Game_Load = 1005] = "Game_Load";
  e[e.Game_Replay = 1006] = "Game_Replay";
  e[e.Game_NextLV = 1007] = "Game_NextLV";
  e[e.Game_PauseTime = 1008] = "Game_PauseTime";
  e[e.Cheat_ShowCheatInfo = 1009] = "Cheat_ShowCheatInfo";
  e[e.Cheat_SetStageNum = 1010] = "Cheat_SetStageNum";
  e[e.Cheat_PassGuide = 1011] = "Cheat_PassGuide";
  e[e.Net_InitSocketCallback = 1012] = "Net_InitSocketCallback";
  e[e.Platform_NavigateTo = 1013] = "Platform_NavigateTo";
  e[e.Login_Start = 1014] = "Login_Start";
  e[e.Login_Finish = 1015] = "Login_Finish";
  e[e.Login_SelectServer = 1016] = "Login_SelectServer";
  e[e.Login_ConnectServer = 1017] = "Login_ConnectServer";
  e[e.Login_UpdateServerList = 1018] = "Login_UpdateServerList";
  e[e.Login_SwitchServer = 1019] = "Login_SwitchServer";
  e[e.Login_OnConnectServer = 1020] = "Login_OnConnectServer";
  e[e.Login_OpenSelectServerView = 1021] = "Login_OpenSelectServerView";
  e[e.AgeCanView_Open = 1022] = "AgeCanView_Open";
  e[e.AgeCanView_Close = 1023] = "AgeCanView_Close";
  e[e.Net_OnReady = 1024] = "Net_OnReady";
  e[e.Net_OnDisconnect = 1025] = "Net_OnDisconnect";
  e[e.Net_Disconnect = 1026] = "Net_Disconnect";
  e[e.Net_Reconnect = 1027] = "Net_Reconnect";
  e[e.Net_Error = 1028] = "Net_Error";
  e[e.Ad_ShowBanner = 1029] = "Ad_ShowBanner";
  e[e.Ad_HideBanner = 1030] = "Ad_HideBanner";
  e[e.Ad_ShowVideo = 1031] = "Ad_ShowVideo";
  e[e.Ad_ShowFullVideo = 1032] = "Ad_ShowFullVideo";
  e[e.Ad_BannerCall = 1033] = "Ad_BannerCall";
  e[e.Ad_ShowInsertAd = 1034] = "Ad_ShowInsertAd";
  e[e.Ad_ShowNativeAd = 1035] = "Ad_ShowNativeAd";
  e[e.Ad_CheckShowNativeAd = 1036] = "Ad_CheckShowNativeAd";
  e[e.Ad_HideNativeAd = 1037] = "Ad_HideNativeAd";
  e[e.Ad_ResetAutoFullAdTime = 1038] = "Ad_ResetAutoFullAdTime";
  e[e.Ad_ShowAdDebuggView = 1039] = "Ad_ShowAdDebuggView";
  e[e.Ad_SetAdCounting = 1040] = "Ad_SetAdCounting";
  e[e.Ad_CheatNoAd = 1041] = "Ad_CheatNoAd";
  e[e.Activity_OpenExchangeCode = 1042] = "Activity_OpenExchangeCode";
  e[e.Setting_EnableMusic = 1043] = "Setting_EnableMusic";
  e[e.Setting_EnableAudio = 1044] = "Setting_EnableAudio";
  e[e.Setting_ValueMusic = 1045] = "Setting_ValueMusic";
  e[e.Setting_ValueAudio = 1046] = "Setting_ValueAudio";
  e[e.Setting_EnableDub = 1047] = "Setting_EnableDub";
  e[e.Setting_EnableShake = 1048] = "Setting_EnableShake";
  e[e.Setting_SetSavingMode = 1049] = "Setting_SetSavingMode";
  e[e.Setting_PlayShake = 1050] = "Setting_PlayShake";
  e[e.Setting_OpenView = 1051] = "Setting_OpenView";
  e[e.Setting_OpenUserAndPolicy = 1052] = "Setting_OpenUserAndPolicy";
  e[e.Setting_ShakeCamera = 1053] = "Setting_ShakeCamera";
  e[e.Setting_OpenGameService = 1054] = "Setting_OpenGameService";
  e[e.Setting_SetClipboardData = 1055] = "Setting_SetClipboardData";
  e[e.Common_ShowFuncDesc = 1056] = "Common_ShowFuncDesc";
  e[e.Common_ShowWeekAward = 1057] = "Common_ShowWeekAward";
  e[e.Common_GotoFunc = 1058] = "Common_GotoFunc";
  e[e.Common_ExChangeGoods = 1059] = "Common_ExChangeGoods";
  e[e.Common_OperateTime = 1060] = "Common_OperateTime";
  e[e.Common_ShowDiamondNode = 1061] = "Common_ShowDiamondNode";
  e[e.Common_ShowFlyReward = 1062] = "Common_ShowFlyReward";
  e[e.Conflate_DataUpdate = 1063] = "Conflate_DataUpdate";
  e[e.Conflate_TimeUpdate = 1064] = "Conflate_TimeUpdate";
  e[e.Conflate_GameReset = 1065] = "Conflate_GameReset";
  e[e.Event_SendEvent = 1066] = "Event_SendEvent";
  e[e.Event_SetSuperProperties = 1067] = "Event_SetSuperProperties";
  e[e.Event_SetUserProperty = 1068] = "Event_SetUserProperty";
  e[e.Event_LoginTA = 1069] = "Event_LoginTA";
  e[e.Event_LogOutTA = 1070] = "Event_LogOutTA";
  e[e.Main_OpenView = 1071] = "Main_OpenView";
  e[e.Main_SetMainViewVisible = 1072] = "Main_SetMainViewVisible";
  e[e.Main_UpdateCurPage = 1073] = "Main_UpdateCurPage";
  e[e.Main_SetBtnEnable = 1074] = "Main_SetBtnEnable";
  e[e.Item_GoodsChange = 1075] = "Item_GoodsChange";
  e[e.Item_DiamondChange = 1076] = "Item_DiamondChange";
  e[e.Fight_onRoleMove = 1077] = "Fight_onRoleMove";
  e[e.Fight_onRoleIdle = 1078] = "Fight_onRoleIdle";
  e[e.Fight_SetPause = 1079] = "Fight_SetPause";
  e[e.Fight_BackToMain = 1080] = "Fight_BackToMain";
  e[e.Fight_ExpUpdate = 1081] = "Fight_ExpUpdate";
  e[e.Fight_LevelUp = 1082] = "Fight_LevelUp";
  e[e.Fight_UpdateGameTime = 1083] = "Fight_UpdateGameTime";
  e[e.Fight_ShowEffect = 1084] = "Fight_ShowEffect";
  e[e.Fight_GameRound = 1085] = "Fight_GameRound";
  e[e.Fight_Kill = 1086] = "Fight_Kill";
  e[e.Fight_CountDown = 1087] = "Fight_CountDown";
  e[e.Fight_GameMonsterCome = 1088] = "Fight_GameMonsterCome";
  e[e.Fight_GameBossCome = 1089] = "Fight_GameBossCome";
  e[e.Fight_SetTrack = 1090] = "Fight_SetTrack";
  e[e.Fight_AddSkill = 1091] = "Fight_AddSkill";
  e[e.Fight_AddBuff = 1092] = "Fight_AddBuff";
  e[e.Fight_OnBuff = 1093] = "Fight_OnBuff";
  e[e.Fight_OnBuffChange = 1094] = "Fight_OnBuffChange";
  e[e.Fight_OnSkillChange = 1095] = "Fight_OnSkillChange";
  e[e.Fight_OnGameState = 1096] = "Fight_OnGameState";
  e[e.Fight_onChangeRole = 1097] = "Fight_onChangeRole";
  e[e.Fight_AddPet = 1098] = "Fight_AddPet";
  e[e.Fight_SpawnHurt = 1099] = "Fight_SpawnHurt";
  e[e.Fight_BeHit = 1100] = "Fight_BeHit";
  e[e.Fight_Dead = 1101] = "Fight_Dead";
  e[e.Fight_PetDead = 1102] = "Fight_PetDead";
  e[e.Fight_MonsterHurt = 1103] = "Fight_MonsterHurt";
  e[e.Fight_OnHpRecover = 1104] = "Fight_OnHpRecover";
  e[e.Fight_MonsterEscape = 1105] = "Fight_MonsterEscape";
  e[e.Fight_End = 1106] = "Fight_End";
  e[e.Fight_Win = 1107] = "Fight_Win";
  e[e.Fight_ReliveSuccess = 1108] = "Fight_ReliveSuccess";
  e[e.Fight_OpenView = 1109] = "Fight_OpenView";
  e[e.Fight_OpenPetTryView = 1110] = "Fight_OpenPetTryView";
  e[e.Fight_OpenTowerView = 1111] = "Fight_OpenTowerView";
  e[e.Fight_OpenRoleTryView = 1112] = "Fight_OpenRoleTryView";
  e[e.Fight_OpenGameBitView = 1113] = "Fight_OpenGameBitView";
  e[e.Fight_OpenSelectView = 1114] = "Fight_OpenSelectView";
  e[e.Fight_OpenFightUIView = 1115] = "Fight_OpenFightUIView";
  e[e.Fight_OpenReliveView = 1116] = "Fight_OpenReliveView";
  e[e.Badge_Set = 1117] = "Badge_Set";
  e[e.Badge_Update = 1118] = "Badge_Update";
  e[e.Badge_Add = 1119] = "Badge_Add";
  e[e.Item_ShowNormalTips = 1120] = "Item_ShowNormalTips";
  e[e.Refresh_Item = 1121] = "Refresh_Item";
  e[e.Refresh_List = 1122] = "Refresh_List";
  e[e.BottomBar_OpenView = 1123] = "BottomBar_OpenView";
  e[e.Hero_OpenView = 1124] = "Hero_OpenView";
  e[e.Hero_SetViewVisible = 1125] = "Hero_SetViewVisible";
  e[e.Hero_PropChange = 1126] = "Hero_PropChange";
  e[e.Test_OpenView = 1127] = "Test_OpenView";
  e[e.Guide_OpenView = 1128] = "Guide_OpenView";
  e[e.Skill_OpenStoreView = 1129] = "Skill_OpenStoreView";
  e[e.Skill_OpenSelcectView = 1130] = "Skill_OpenSelcectView";
  e[e.Skill_CloseSelcectView = 1131] = "Skill_CloseSelcectView";
  e[e.Buff_OpenSelcectView = 1132] = "Buff_OpenSelcectView";
  e[e.Buff_CloseSelcectView = 1133] = "Buff_CloseSelcectView";
  e[e.HideBuffInfo = 1134] = "HideBuffInfo";
  e[e.ReduceActiveSkill = 1135] = "ReduceActiveSkill";
  e[e.ReducePassiveSkill = 1136] = "ReducePassiveSkill";
  e[e.Tower_FightRoundTickUpdate = 1137] = "Tower_FightRoundTickUpdate";
  e[e.Tower_SpeedUp = 1138] = "Tower_SpeedUp";
  e[e.Tower_FightRoundTickStart = 1139] = "Tower_FightRoundTickStart";
  e[e.Tower_ShowShop = 1140] = "Tower_ShowShop";
  e[e.CatGame_GetCoin = 1141] = "CatGame_GetCoin";
  e[e.CatGame_BackToReady = 1142] = "CatGame_BackToReady";
  e[e.BottomBar_SelectGame = 1143] = "BottomBar_SelectGame";
  e[e.Pop_ShowTInfo = 1144] = "Pop_ShowTInfo";
  e[e.Main_ResetView = 1145] = "Main_ResetView";
  e[e.Game_LoadFinish = 1146] = "Game_LoadFinish";
  e[e.CatGame_EnableMenu = 1147] = "CatGame_EnableMenu";
  e[e.CatGame_OnTick = 1148] = "CatGame_OnTick";
  e[e.CatGame_CheckWeapon = 1149] = "CatGame_CheckWeapon";
  e[e.CatGame_WorkHandle = 1150] = "CatGame_WorkHandle";
  e[e.CatGame_FightEnd = 1151] = "CatGame_FightEnd";
  e[e.CatGame_FightWin = 1152] = "CatGame_FightWin";
  e[e.Main_LoadAnim = 1153] = "Main_LoadAnim";
  e[e.CatGame_CleanSkill = 1154] = "CatGame_CleanSkill";
  e[e.Main_HandleBox = 1155] = "Main_HandleBox";
  e[e.Fight_OpDialog = 1156] = "Fight_OpDialog";
  e[e.CatGameShowAward = 1157] = "CatGameShowAward";
  e[e.CatGame_QuickToHome = 1158] = "CatGame_QuickToHome";
  e[e.Fight_TeammatesChanges = 1159] = "Fight_TeammatesChanges";
  e[e.CatGame_GetCoin_Onready = 1160] = "CatGame_GetCoin_Onready";
  e[e.Fight_EntityUpdate = 1161] = "Fight_EntityUpdate";
  e[e.CatGame_GuideStart = 1162] = "CatGame_GuideStart";
  e[e.CatGame_GuideUpgrade = 1163] = "CatGame_GuideUpgrade";
  e[e.CatGame_ShowWorkBox = 1164] = "CatGame_ShowWorkBox";
  e[e.CatGame_StarGame = 1165] = "CatGame_StarGame";
  e[e.CatGame_ReadyCall = 1166] = "CatGame_ReadyCall";
  e[e.M3_CloseDlsView = 1167] = "M3_CloseDlsView";
  e[e.M3_ShowDlsFailView = 1168] = "M3_ShowDlsFailView";
  e[e.M3_ShowDlsWinView = 1169] = "M3_ShowDlsWinView";
  e[e.CatGame_StartDls = 1170] = "CatGame_StartDls";
  e[e.M3_Dls_SetTimer = 1171] = "M3_Dls_SetTimer";
  e[e.M3_Dls_SetSkillTips = 1172] = "M3_Dls_SetSkillTips";
  e[e.Fight_WeatherSwitch = 1173] = "Fight_WeatherSwitch";
  e[e.M3_CallStart = 1174] = "M3_CallStart";
  e[e.ScreenMatch_Finish = 1175] = "ScreenMatch_Finish";
  e[e.ModeHide_SetHideBtn = 1176] = "ModeHide_SetHideBtn";
  e[e.ModeHide_RoleHide = 1177] = "ModeHide_RoleHide";
  e[e.Fight_GameRoundType = 1178] = "Fight_GameRoundType";
  e[e.M3_AddTempCoin = 1179] = "M3_AddTempCoin";
  e[e.M3_2menuUnlock = 1180] = "M3_2menuUnlock";
  e[e.M3_ShowMenu = 1181] = "M3_ShowMenu";
  e[e.M3_HideMenu = 1182] = "M3_HideMenu";
  e[e.M3_ShowMenuView = 1183] = "M3_ShowMenuView";
  e[e.M3_1MenuHandle = 1184] = "M3_1MenuHandle";
  e[e.M3_2MenuHandle = 1185] = "M3_2MenuHandle";
  e[e.M3_2menuCheck = 1186] = "M3_2menuCheck";
  e[e.M3_Update2Menu = 1187] = "M3_Update2Menu";
  e[e.Tower_FightRoundTickStart1 = 1188] = "Tower_FightRoundTickStart1";
  e[e.M3_HideMenuView = 1189] = "M3_HideMenuView";
  e[e.Fight_RoundState = 1190] = "Fight_RoundState";
  e[e.M3_ShowGetAmethyst = 1191] = "M3_ShowGetAmethyst";
  e[e.M3_CheckFreeSkill = 1192] = "M3_CheckFreeSkill";
  e[e.M3_CheckHighSkillUnlock = 1193] = "M3_CheckHighSkillUnlock";
  e[e.knasChange = 1194] = "knasChange";
  e[e.ModeBack_PackState = 1195] = "ModeBack_PackState";
  e[e.M20_CheckMenu = 1196] = "M20_CheckMenu";
  e[e.M20_RoungChange = 1197] = "M20_RoungChange";
  e[e.M20_ShowEquipInfo = 1198] = "M20_ShowEquipInfo";
  e[e.M20_EquipUpgrade = 1199] = "M20_EquipUpgrade";
  e[e.M20_CheckPackData = 1200] = "M20_CheckPackData";
  e[e.ResetView = 1201] = "ResetView";
  e[e.Fight_ClickBackHome = 1202] = "Fight_ClickBackHome";
  e[e.Common_Guide_Forcus = 1203] = "Common_Guide_Forcus";
  e[e.Common_Guide_Anim = 1204] = "Common_Guide_Anim";
  e[e.Common_Guide_Close = 1205] = "Common_Guide_Close";
  e[e.Ks_SetGame = 1206] = "Ks_SetGame";
  e[e.Fight_EntityUseSkillEnd = 1207] = "Fight_EntityUseSkillEnd";
  e[e.Fight_HandleButton = 1208] = "Fight_HandleButton";
  e[e.Common_Guide_OnlyDesc = 1209] = "Common_Guide_OnlyDesc";
  e[e.M20_GetRoleFrag = 1210] = "M20_GetRoleFrag";
  e[e.M20_RoleChange = 1211] = "M20_RoleChange";
  e[e.M20_ShowRoleInfo = 1212] = "M20_ShowRoleInfo";
  e[e.M20_RoleListChange = 1213] = "M20_RoleListChange";
  e[e.M20_CheckEnergy = 1214] = "M20_CheckEnergy";
  e[e.Platform_AddtoDestop = 1215] = "Platform_AddtoDestop";
  e[e.M20_GuideHandle = 1216] = "M20_GuideHandle";
  e[e.Fight_GuideChange = 1217] = "Fight_GuideChange";
  e[e.M20_UpdateBoxExp = 1218] = "M20_UpdateBoxExp";
  e[e.Pay_ToPay = 1219] = "Pay_ToPay";
  e[e.Pay_PayRewardProductId = 1220] = "Pay_PayRewardProductId";
  e[e.Pay_LoadingView = 1221] = "Pay_LoadingView";
  e[e.Shop_InfoUpDate = 1222] = "Shop_InfoUpDate";
  e[e.Shop_BuyCharge = 1223] = "Shop_BuyCharge";
  e[e.Shop_ShopItemList = 1224] = "Shop_ShopItemList";
  e[e.ModeBack_RoundUnlock = 1225] = "ModeBack_RoundUnlock";
  e[e.Shop_RecoveryBuy = 1226] = "Shop_RecoveryBuy";
  e[e.Fight_OnSkill = 1227] = "Fight_OnSkill";
  e[e.Task_OpenMainView = 1228] = "Task_OpenMainView";
  e[e.Task_UpdateProgress = 1229] = "Task_UpdateProgress";
  e[e.Task_GetResult = 1230] = "Task_GetResult";
  e[e.Task_Jump = 1231] = "Task_Jump";
  e[e.Task_UpdateAchieveDate = 1232] = "Task_UpdateAchieveDate";
  e[e.Task_ClearData = 1233] = "Task_ClearData";
  e[e.Task_UpdateNextDayView = 1234] = "Task_UpdateNextDayView";
  e[e.JumpOpenMainView = 1235] = "JumpOpenMainView";
  e[e.FightHightRound = 1236] = "FightHightRound";
  e[e.Fight_Vampirism = 1237] = "Fight_Vampirism";
  e[e.Fight_ShowGameTips = 1238] = "Fight_ShowGameTips";
  e[e.Fight_PrePare_Refresh_SweepView = 1239] = "Fight_PrePare_Refresh_SweepView";
  e[e.Item_GetReward = 1240] = "Item_GetReward";
  e[e.Fight_PackView_Full_Block = 1241] = "Fight_PackView_Full_Block";
  e[e.PrePare_Fight_Skip = 1242] = "PrePare_Fight_Skip";
  e[e.M34_GetBuff = 1243] = "M34_GetBuff";
  e[e.M31_Update_Bullet_Num = 1244] = "M31_Update_Bullet_Num";
  e[e.M31_Update_Hand_Style = 1245] = "M31_Update_Hand_Style";
  e[e.MoreGame_Refresh = 1246] = "MoreGame_Refresh";
  e[e.Is_Back_From_Try_Play = 1247] = "Is_Back_From_Try_Play";
  e[e.M36_Launcher_Anim = 1248] = "M36_Launcher_Anim";
  e[e.M36_Update_Progress = 1249] = "M36_Update_Progress";
  e[e.NO_BG_Moving = 1250] = "NO_BG_Moving";
  e[e.M37_DragonWarMove = 1251] = "M37_DragonWarMove";
  e[e.M37_DragonWarPowerChange = 1252] = "M37_DragonWarPowerChange";
  e[e.M37_Wall_Enter = 1253] = "M37_Wall_Enter";
  e[e.M37_DragonCollide = 1254] = "M37_DragonCollide";
  e[e.Platform_ShowTestAD = 1255] = "Platform_ShowTestAD";
  e[e.ByteDance_Check_Gift = 1256] = "ByteDance_Check_Gift";
  e[e.Platform_CheckScene = 1257] = "Platform_CheckScene";
})(exports.ListenID || (exports.ListenID = {}));