var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MonsterTidal = require("MonsterTidal");
cc.v2();
cc.v2();
var s = cc._decorator.ccclass;
var def_MonsterTidalBoss = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.game.createLifeBar(this, {
      scale: 2 * this.monCfg.Scale
    });
  };
  _ctor.prototype.onNewSize = function (t) {
    e.prototype.onNewSize.call(this, t);
    this._haedPosition.y /= .7;
    this._haedPosition.y *= 1.1;
  };
  return cc__decorate([s], _ctor);
}($2MonsterTidal.default);
exports.default = def_MonsterTidalBoss;