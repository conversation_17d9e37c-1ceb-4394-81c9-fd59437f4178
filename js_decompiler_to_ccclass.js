const fs = require('fs');
const path = require('path');

/**
 * JS反编译器 - 将编译后的Cocos Creator JS转换为原始cc.Class格式
 * 专门处理从原始JS编译而来的代码，转换为cc.Class语法
 */
class JSDecompilerToCCClass {
    constructor() {
        this.outputDir = 'js_converted';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    /**
     * 转换单个文件
     */
    convertFile(inputPath) {
        try {
            console.log(`Converting file: ${inputPath}`);
            const content = fs.readFileSync(inputPath, 'utf8');
            const fileName = path.basename(inputPath, '.js');
            const outputPath = path.join(this.outputDir, `${fileName}.js`);

            // console.log(`正在转换: ${inputPath}`);

            // 检查是否为空文件或只包含空白字符
            if (content.trim().length === 0) {
                // console.log(`检测到空文件，直接复制: ${inputPath}`);
                fs.writeFileSync(outputPath, content, 'utf8');
                // console.log(`复制完成: ${outputPath}`);
                return 'copied';
            }

            // 检查是否包含ccclass相关内容
            const isCCClass = this.isCCClassFile(content);
            console.log(`Is CC Class file: ${isCCClass}`);
            // console.log(`File: ${inputPath}, isCCClass: ${isCCClass}`);
            if (!isCCClass) {
                // console.log(`检测到非ccclass文件，直接复制: ${inputPath}`);
                fs.writeFileSync(outputPath, content, 'utf8');
                // console.log(`复制完成: ${outputPath}`);
                return 'copied';
            }

            // 检查是否包含多个cc.Class定义
            const multipleClasses = this.extractMultipleClasses(content);
            if (multipleClasses.length > 1) {
                console.log(`检测到多个cc.Class定义 (${multipleClasses.length}个)，暂时不转换，直接复制: ${inputPath}`);
                fs.writeFileSync(outputPath, content, 'utf8');
                return 'copied_multiple_classes';
            }

            const convertedContent = this.decompileToOriginalJS(content, fileName);

            fs.writeFileSync(outputPath, convertedContent, 'utf8');
            // console.log(`转换完成: ${outputPath}`);

            return 'converted';
        } catch (error) {
            console.error(`转换文件失败 ${inputPath}:`, error.message);
            return 'failed';
        }
    }

    /**
     * 转换目录下所有JS文件
     */
    convertDirectory(inputDir) {
        const files = fs.readdirSync(inputDir);
        let successCount = 0;
        let totalCount = 0;
        const convertedFiles = [];
        const copiedFiles = [];
        const multipleClassFiles = [];
        const failedFiles = [];

        files.forEach(file => {
            if (file.endsWith('.js')) {
                totalCount++;
                const filePath = path.join(inputDir, file);
                const result = this.convertFile(filePath);
                if (result === 'converted') {
                    successCount++;
                    convertedFiles.push(file);
                } else if (result === 'copied') {
                    copiedFiles.push(file);
                } else if (result === 'copied_multiple_classes') {
                    multipleClassFiles.push(file);
                } else {
                    failedFiles.push(file);
                }
            }
        });

        // 先输出文件列表
        if (convertedFiles.length > 0) {
            console.log(`\n转换的文件列表:`);
            convertedFiles.forEach(file => console.log(`  - ${file}`));
        }

        if (multipleClassFiles.length > 0) {
            console.log(`\n包含多个cc.Class的文件列表 (已复制):`);
            multipleClassFiles.forEach(file => console.log(`  - ${file}`));
        }

        if (failedFiles.length > 0) {
            console.log(`\n失败的文件列表:`);
            failedFiles.forEach(file => console.log(`  - ${file}`));
        }

        // 最后输出统计信息
        console.log(`\n=== 转换统计 ===`);
        console.log(`总文件数: ${totalCount}`);
        console.log(`转换文件数: ${convertedFiles.length}`);
        console.log(`复制文件数: ${copiedFiles.length}`);
        console.log(`多个cc.Class文件数: ${multipleClassFiles.length}`);
        console.log(`失败文件数: ${failedFiles.length}`);
    }

    /**
     * 反编译为原始JS格式
     */
    decompileToOriginalJS(content, fileName) {
        console.log(`Starting decompilation for: ${fileName}`);
        // 分析编译后的代码结构
        const analysis = this.analyzeCompiledCode(content, fileName);

        // 计算属性总数
        let totalProperties = 0;
        if (analysis.isMultipleClasses) {
            totalProperties = analysis.classes.reduce((sum, cls) => sum + (cls.properties ? cls.properties.length : 0), 0);
        } else {
            totalProperties = analysis.properties ? analysis.properties.length : 0;
        }
        console.log(`Analysis completed, found ${totalProperties} properties`);

        // 生成原始cc.Class格式代码
        const result = this.generateOriginalCCClass(analysis, fileName);
        console.log(`Code generation completed`);
        return result;
    }

    /**
     * 分析编译后的代码
     */
    analyzeCompiledCode(content, fileName) {
        // 检查是否包含多个class定义
        const multipleClasses = this.extractMultipleClasses(content);

        console.log(`extractMultipleClasses found ${multipleClasses.length} classes`);

        if (multipleClasses.length > 0) {
            // 多个class的情况（包括单个class）
            return {
                isMultipleClasses: true,
                classes: multipleClasses,
                imports: this.extractImports(content),
                originalContent: content
            };
        }

        // 单个class的情况（保持原有逻辑）
        const analysis = {
            className: '',
            baseClass: 'cc.Component',
            properties: [],
            methods: [],
            imports: [],
            decorators: [],
            moduleType: 'component', // component, static, module, enum
            originalContent: content // 保存原始内容用于枚举值提取
        };

        // 检测模块类型
        analysis.moduleType = this.detectModuleType(content);

        // 提取类名
        analysis.className = this.extractClassName(content, fileName);

        // 提取基类（对组件和模块类型）
        if (analysis.moduleType === 'component' || analysis.moduleType === 'module') {
            analysis.baseClass = this.extractBaseClass(content);
        }

        // 对于枚举类型，不需要提取属性和方法
        if (analysis.moduleType !== 'enum') {
            // 提取属性
            analysis.properties = this.extractProperties(content);

            // 提取方法
            analysis.methods = this.extractMethods(content);
        }

        // 提取导入
        analysis.imports = this.extractImports(content);

        return analysis;
    }

    /**
     * 检查是否是ccclass文件
     */
    isCCClassFile(content) {
        // 检查是否包含ccclass相关标识
        return content.includes('ccclass') ||
            content.includes('cc._decorator') ||
            content.includes('@ccclass') ||
            content.includes('__decorate');
    }

    /**
     * 提取多个class定义
     */
    extractMultipleClasses(content) {
        const classes = [];

        // 分别匹配每个类定义的各个部分
        // 1. 先找到所有的var def_ClassName = function或var exp_ClassName = function声明
        // 修改正则表达式以匹配实际的函数结构
        // 匹配: var exp_ClassName = function (e) { ... }($2BuffList.Buff_Excute);
        const varPattern = /var\s+((?:def_|exp_)\w+)\s*=\s*function\s*\([^)]*\)\s*\{[\s\S]*?\}\s*\([^)]*\);/g;

        let varMatch;
        while ((varMatch = varPattern.exec(content)) !== null) {
            const [varDefinition, varName] = varMatch;

            // 2. 查找对应的cc__decorate声明来获取真实类名
            // 修改装饰器模式匹配: return cc__decorate([ccp_ccclass("ClassName")], _ctor);
            const decoratePattern = /return\s+cc__decorate\s*\(\s*\[\s*ccp_ccclass\s*\(\s*["']([^"']+)["']\s*\)\s*\]\s*,\s*\w+\s*\);/;
            const decorateMatch = varDefinition.match(decoratePattern);

            // 3. 查找对应的exports声明
            const exportsPattern = new RegExp(`exports\\.(\\w+)\\s*=\\s*${varName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')};`);
            const exportsMatch = content.match(exportsPattern);

            if (decorateMatch) {
                const className = decorateMatch[1];

                // 提取这个class的基类
                const baseClass = this.extractBaseClassFromClassDefinition(varDefinition);
                console.log(`Extracted baseClass for ${className}: "${baseClass}"`);

                // 提取类的方法和属性
                const methods = this.extractMethodsFromClassDefinition(varDefinition);
                const properties = this.extractPropertiesFromClassDefinition(varDefinition);

                classes.push({
                    className: className,
                    varName: varName,
                    baseClass: baseClass,
                    methods: methods,
                    properties: properties,
                    fullDefinition: varDefinition
                });
            }
        }

        return classes;
    }

    /**
     * 从class定义中提取基类
     */
    extractBaseClassFromClassDefinition(classDefinition) {
        // 查找IIFE模式的继承：}($2Pop.Pop); - 优先检查这个模式
        const iifePattern = /\}\s*\(\s*(\$\d*\w+(?:\.\w+)*)\s*\)\s*;?/;
        const iifeMatch = classDefinition.match(iifePattern);

        if (iifeMatch) {
            let baseClass = iifeMatch[1];
            // 如果包含.default，去掉.default但保留后面的部分
            // if (baseClass && baseClass.includes('.default.')) {
            //     baseClass = baseClass.replace('.default.', '.');
            // } else if (baseClass && baseClass.endsWith('.default')) {
            //     baseClass = baseClass.replace('.default', '');
            // }
            return baseClass;
        }

        // 查找其他模式的继承
        const patterns = [
            /__extends\s*\(\s*\w+\s*,\s*(\$?\w+(?:\.\w+)*)\s*\)/,
            /extends\s+(\w+(?:\.\w+)*)/
        ];

        for (const pattern of patterns) {
            const match = classDefinition.match(pattern);
            if (match && match[1]) {
                let baseClass = match[1];
                if (baseClass === 'e') {
                    return 'cc.Component';
                }
                if (baseClass === 'Component') {
                    return 'cc.Component';
                }
                // 如果包含.default，去掉.default但保留后面的部分
                // if (baseClass.includes('.default.')) {
                //     baseClass = baseClass.replace('.default.', '.');
                // } else if (baseClass.endsWith('.default')) {
                //     baseClass = baseClass.replace('.default', '');
                // }
                return baseClass;
            }
        }

        // 检查是否只有 ccclass 装饰器而没有继承
        // 如果只有 ccp_ccclass 装饰器，说明没有继承
        const onlyCcClassPattern = /cc__decorate\s*\(\s*\[\s*ccp_ccclass\s*\([^)]*\)\s*\]\s*,\s*\w+\s*\)/;
        if (onlyCcClassPattern.test(classDefinition)) {
            return null; // 没有继承
        }

        return 'cc.Component';
    }

    /**
     * 从class定义中提取方法
     */
    extractMethodsFromClassDefinition(classDefinition) {
        const methods = [];

        // 1. 首先查找 _ctor 构造函数
        const ctorPattern = /function\s+_ctor\s*\(\s*\)\s*\{/;
        const ctorMatch = classDefinition.match(ctorPattern);

        if (ctorMatch) {
            const startIndex = classDefinition.indexOf(ctorMatch[0]);
            const functionStart = classDefinition.indexOf('{', startIndex);

            if (functionStart !== -1) {
                // 使用括号计数来找到匹配的结束大括号
                let braceCount = 1;
                let endIndex = functionStart + 1;

                while (endIndex < classDefinition.length && braceCount > 0) {
                    const char = classDefinition[endIndex];
                    if (char === '{') {
                        braceCount++;
                    } else if (char === '}') {
                        braceCount--;
                    }
                    endIndex++;
                }

                // 提取构造函数体
                const body = classDefinition.substring(functionStart + 1, endIndex - 1).trim();

                methods.push({
                    name: 'ctor',
                    params: [],
                    body: body,
                    fullDefinition: classDefinition.substring(startIndex, endIndex),
                    isConstructor: true
                });
            }
        }

        // 2. 然后查找原型方法
        const methodStartPattern = /\w+\.prototype\.(\w+)\s*=\s*function\s*\([^)]*\)\s*\{/g;

        let match;
        while ((match = methodStartPattern.exec(classDefinition)) !== null) {
            const [startMatch, methodName] = match;
            const startIndex = match.index;

            // 找到方法开始的大括号位置
            const functionStart = classDefinition.indexOf('{', startIndex);
            if (functionStart === -1) continue;

            // 使用括号计数来找到匹配的结束大括号
            let braceCount = 1;
            let endIndex = functionStart + 1;

            while (endIndex < classDefinition.length && braceCount > 0) {
                const char = classDefinition[endIndex];
                if (char === '{') {
                    braceCount++;
                } else if (char === '}') {
                    braceCount--;
                }
                endIndex++;
            }

            // 检查是否以分号结尾
            if (endIndex < classDefinition.length && classDefinition[endIndex] === ';') {
                endIndex++;
            }

            const fullMatch = classDefinition.substring(startIndex, endIndex);

            // 提取参数
            const paramMatch = fullMatch.match(/function\s*\(([^)]*)\)/);
            const params = paramMatch && paramMatch[1] ?
                paramMatch[1].split(',').map(p => p.trim()).filter(p => p) : [];

            // 提取方法体
            const bodyMatch = fullMatch.match(/\{([\s\S]*)\}/);
            const body = bodyMatch ? bodyMatch[1].trim() : '';

            methods.push({
                name: methodName,
                params: params,
                body: body,
                fullDefinition: fullMatch
            });

            // 更新正则表达式的lastIndex以避免重复匹配
            methodStartPattern.lastIndex = endIndex;
        }

        return methods;
    }

    /**
     * 精确提取完整的Object.defineProperty定义
     * 使用括号计数来确保匹配完整的定义，避免在嵌套函数中截断
     */
    extractCompletePropertyDefinitions(classDefinition) {
        const properties = [];
        const definePropertyPattern = /Object\.defineProperty\s*\(\s*\w+\.prototype\s*,\s*["']([^"']+)["']\s*,\s*\{/g;

        let match;
        while ((match = definePropertyPattern.exec(classDefinition)) !== null) {
            const propertyName = match[1];
            const startIndex = match.index;
            const openBraceIndex = classDefinition.indexOf('{', startIndex);

            if (openBraceIndex === -1) continue;

            // 使用括号计数来找到匹配的结束大括号
            let braceCount = 1;
            let endIndex = openBraceIndex + 1;

            while (endIndex < classDefinition.length && braceCount > 0) {
                const char = classDefinition[endIndex];
                if (char === '{') {
                    braceCount++;
                } else if (char === '}') {
                    braceCount--;
                }
                endIndex++;
            }

            // 查找结束的分号
            while (endIndex < classDefinition.length && /\s/.test(classDefinition[endIndex])) {
                endIndex++;
            }
            if (endIndex < classDefinition.length && classDefinition[endIndex] === ';') {
                endIndex++;
            }

            const fullMatch = classDefinition.substring(startIndex, endIndex);
            const propertyContent = classDefinition.substring(openBraceIndex + 1, endIndex - (classDefinition[endIndex - 1] === ';' ? 2 : 1));

            properties.push({
                propertyName: propertyName,
                propertyContent: propertyContent,
                fullMatch: fullMatch
            });

            // 更新正则表达式的lastIndex以避免重复匹配
            definePropertyPattern.lastIndex = endIndex;
        }

        return properties;
    }

    /**
     * 从class定义中提取属性
     */
    extractPropertiesFromClassDefinition(classDefinition) {
        console.log(`Extracting properties from class definition, length: ${classDefinition.length}`);
        const properties = [];
        const processedProperties = new Set(); // 跟踪已处理的属性名

        // 匹配Object.defineProperty定义的属性（改进的正则表达式，更好地处理跨行情况和嵌套括号）
        const propertyPattern = /Object\.defineProperty\s*\(\s*\w+\.prototype\s*,\s*["']([^"']+)["']\s*,\s*\{([\s\S]*?)\}\s*\);/g;

        // 使用更精确的方法来匹配完整的Object.defineProperty定义
        const propertyMatches = this.extractCompletePropertyDefinitions(classDefinition);
        console.log(`Found ${propertyMatches.length} property matches in class definition`);

        // 处理精确匹配的属性定义
        propertyMatches.forEach(propMatch => {
            const { propertyName, propertyContent, fullMatch } = propMatch;

            // 如果已经处理过这个属性，跳过
            if (processedProperties.has(propertyName)) {
                return;
            }

            // 检查是否是getter
            const isGetter = /get\s*:\s*function/.test(propertyContent);
            // 检查是否是setter
            const isSetter = /set\s*:\s*function/.test(propertyContent);

            let getterImplementation = '';
            let setterImplementation = '';

            if (isGetter) {
                // 使用更精确的方法提取getter函数体
                const getterStartMatch = propertyContent.match(/get\s*:\s*function\s*\([^)]*\)\s*\{/);
                if (getterStartMatch) {
                    const startIndex = propertyContent.indexOf(getterStartMatch[0]) + getterStartMatch[0].length;

                    // 使用括号计数来找到完整的函数体
                    let braceCount = 1;
                    let endIndex = startIndex;

                    while (endIndex < propertyContent.length && braceCount > 0) {
                        if (propertyContent[endIndex] === '{') {
                            braceCount++;
                        } else if (propertyContent[endIndex] === '}') {
                            braceCount--;
                        }
                        endIndex++;
                    }

                    if (braceCount === 0) {
                        getterImplementation = propertyContent.substring(startIndex, endIndex - 1).trim();
                    }
                }
                // 修复getter函数体的完整性
                getterImplementation = this.fixGetterImplementation(getterImplementation);
            }

            if (isSetter) {
                // 使用更精确的方法提取setter函数体
                const setterStartMatch = propertyContent.match(/set\s*:\s*function\s*\(([^)]*)\)\s*\{/);
                if (setterStartMatch) {
                    const paramName = setterStartMatch[1].trim() || 'value';

                    const startIndex = propertyContent.indexOf(setterStartMatch[0]) + setterStartMatch[0].length;

                    // 使用括号计数来找到完整的函数体
                    let braceCount = 1;
                    let endIndex = startIndex;

                    while (endIndex < propertyContent.length && braceCount > 0) {
                        if (propertyContent[endIndex] === '{') {
                            braceCount++;
                        } else if (propertyContent[endIndex] === '}') {
                            braceCount--;
                        }
                        endIndex++;
                    }

                    if (braceCount === 0) {
                        setterImplementation = propertyContent.substring(startIndex, endIndex - 1).trim();
                        setterImplementation = this.replaceSetterParameter(setterImplementation, paramName);
                    }
                }
            }

            // 如果同时有getter和setter，创建一个包含两者的属性对象
            if (isGetter && isSetter) {
                properties.push({
                    name: propertyName,
                    type: 'getter-setter',
                    getterImplementation: getterImplementation,
                    setterImplementation: setterImplementation,
                    fullDefinition: fullMatch
                });
            } else if (isGetter) {
                properties.push({
                    name: propertyName,
                    type: 'getter',
                    implementation: getterImplementation,
                    fullDefinition: fullMatch
                });
            } else if (isSetter) {
                properties.push({
                    name: propertyName,
                    type: 'setter',
                    implementation: setterImplementation,
                    fullDefinition: fullMatch
                });
            } else {
                properties.push({
                    name: propertyName,
                    type: 'property',
                    implementation: '',
                    fullDefinition: fullMatch
                });
            }

            processedProperties.add(propertyName);
        });

        // 保留原有的正则表达式匹配作为备用
        let match;
        while ((match = propertyPattern.exec(classDefinition)) !== null) {
            const [fullMatch, propertyName, propertyContent] = match;

            // 如果已经处理过这个属性，跳过
            if (processedProperties.has(propertyName)) {
                continue;
            }

            // 检查是否是getter
            const isGetter = /get\s*:\s*function/.test(propertyContent);
            // 检查是否是setter
            const isSetter = /set\s*:\s*function/.test(propertyContent);

            let getterImplementation = '';
            let setterImplementation = '';

            if (isGetter) {
                // 使用更精确的方法提取getter函数体
                const getterStartMatch = propertyContent.match(/get\s*:\s*function\s*\([^)]*\)\s*\{/);
                if (getterStartMatch) {
                    const startIndex = propertyContent.indexOf(getterStartMatch[0]) + getterStartMatch[0].length;

                    // 使用括号计数来找到完整的函数体
                    let braceCount = 1;
                    let endIndex = startIndex;

                    while (endIndex < propertyContent.length && braceCount > 0) {
                        if (propertyContent[endIndex] === '{') {
                            braceCount++;
                        } else if (propertyContent[endIndex] === '}') {
                            braceCount--;
                        }
                        endIndex++;
                    }

                    if (braceCount === 0) {
                        getterImplementation = propertyContent.substring(startIndex, endIndex - 1).trim();
                    }
                }
                // 修复getter函数体的完整性
                getterImplementation = this.fixGetterImplementation(getterImplementation);
            }

            if (isSetter) {
                // 使用更精确的方法提取setter函数体
                const setterStartMatch = propertyContent.match(/set\s*:\s*function\s*\(([^)]*)\)\s*\{/);
                if (setterStartMatch) {
                    const paramName = setterStartMatch[1].trim() || 'value';
                    const startIndex = propertyContent.indexOf(setterStartMatch[0]) + setterStartMatch[0].length;

                    // 使用括号计数来找到完整的函数体
                    let braceCount = 1;
                    let endIndex = startIndex;

                    while (endIndex < propertyContent.length && braceCount > 0) {
                        if (propertyContent[endIndex] === '{') {
                            braceCount++;
                        } else if (propertyContent[endIndex] === '}') {
                            braceCount--;
                        }
                        endIndex++;
                    }

                    if (braceCount === 0) {
                        setterImplementation = propertyContent.substring(startIndex, endIndex - 1).trim();
                        setterImplementation = this.replaceSetterParameter(setterImplementation, paramName);
                    }
                }
            }

            // 如果同时有getter和setter，创建一个包含两者的属性对象
            if (isGetter && isSetter) {
                properties.push({
                    name: propertyName,
                    type: 'getter-setter',
                    getterImplementation: getterImplementation,
                    setterImplementation: setterImplementation,
                    fullDefinition: fullMatch
                });
            } else if (isGetter) {
                properties.push({
                    name: propertyName,
                    type: 'getter',
                    implementation: getterImplementation,
                    fullDefinition: fullMatch
                });
            } else if (isSetter) {
                properties.push({
                    name: propertyName,
                    type: 'setter',
                    implementation: setterImplementation,
                    fullDefinition: fullMatch
                });
            } else {
                properties.push({
                    name: propertyName,
                    type: 'property',
                    implementation: '',
                    fullDefinition: fullMatch
                });
            }

            processedProperties.add(propertyName);
        }

        // 查找 cc__decorate([ccp_property(...)]) 模式的装饰器属性
        const ccDecoratePattern = /cc__decorate\s*\(\s*\[\s*ccp_property\s*\(\s*([^)]+)\s*\)\s*\]\s*,\s*_ctor\.prototype\s*,\s*["'](\w+)["']\s*,\s*undefined\s*\)/g;

        while ((match = ccDecoratePattern.exec(classDefinition)) !== null) {
            const [, typeInfo, propertyName] = match;
            const parsedInfo = this.parseDecoratorInfo(typeInfo);

            properties.push({
                name: propertyName,
                type: parsedInfo.type || 'cc.Node',
                displayName: parsedInfo.displayName,
                fullDefinition: match[0]
            });
        }

        console.log(`Extracted ${properties.length} properties from class definition`);
        properties.forEach((prop, index) => {
            console.log(`  Property ${index}: ${prop.name} (type: ${prop.type})`);
        });
        return properties;
    }

    /**
     * 替换setter中的参数名
     */
    replaceSetterParameter(setterBody, originalParam) {
        if (!setterBody || !originalParam) return setterBody;



        // 移除多余的变量声明
        let result = setterBody.replace(new RegExp(`var\s+${originalParam}\s*;`, 'g'), '');

        // 只替换确实是setter参数的使用，不替换其他变量
        // 特别注意：不要替换局部变量（如 var t, o, i 等）

        // 1. 替换属性访问：e.prop -> value.prop（只替换参数名）
        const regex = new RegExp(`\\b${originalParam}\\.`, 'g');
        console.log(`Using regex: ${regex} to replace "${originalParam}."`);
        const beforeProp = result;
        result = result.replace(regex, 'value.');
        if (result !== beforeProp) {
            console.log(`Property access replacement changed the code`);
            // 查找包含 buffId 的部分
            const buffIdIndex = beforeProp.indexOf('buffId');
            if (buffIdIndex !== -1) {
                const start = Math.max(0, buffIdIndex - 50);
                const end = Math.min(beforeProp.length, buffIdIndex + 100);
                console.log(`Before (buffId area): ${beforeProp.substring(start, end)}`);
                console.log(`After (buffId area): ${result.substring(start, end)}`);
            } else {
                console.log(`Before: ${beforeProp.substring(0, 200)}`);
                console.log(`After: ${result.substring(0, 200)}`);
            }
        }

        // 2. 替换函数调用中的参数：func(e) -> func(value)
        const beforeFunc = result;
        result = result.replace(new RegExp(`\\(\\s*${originalParam}\\s*\\)`, 'g'), '(value)');
        result = result.replace(new RegExp(`\\(\\s*${originalParam}\\s*,`, 'g'), '(value,');
        result = result.replace(new RegExp(`,\\s*${originalParam}\\s*\\)`, 'g'), ', value)');
        result = result.replace(new RegExp(`,\\s*${originalParam}\\s*,`, 'g'), ', value,');
        if (result !== beforeFunc) {
            console.log(`Function call replacement changed the code`);
            // 查找包含 buffId 的部分
            const buffIdIndex = beforeFunc.indexOf('buffId');
            if (buffIdIndex !== -1) {
                const start = Math.max(0, buffIdIndex - 50);
                const end = Math.min(beforeFunc.length, buffIdIndex + 100);
                console.log(`Before func (buffId area): ${beforeFunc.substring(start, end)}`);
                console.log(`After func (buffId area): ${result.substring(start, end)}`);
            }
        }

        // 3. 替换赋值右侧的使用：= e; -> = value;
        const beforeAssign = result;
        result = result.replace(new RegExp(`=\\s*${originalParam}\\s*;`, 'g'), '= value;');
        result = result.replace(new RegExp(`=\\s*${originalParam}\\s*\\)`, 'g'), '= value)');
        if (result !== beforeAssign) {
            console.log(`Assignment replacement changed the code`);
            // 查找包含 buffId 的部分
            const buffIdIndex = beforeAssign.indexOf('buffId');
            if (buffIdIndex !== -1) {
                const start = Math.max(0, buffIdIndex - 50);
                const end = Math.min(beforeAssign.length, buffIdIndex + 100);
                console.log(`Before assign (buffId area): ${beforeAssign.substring(start, end)}`);
                console.log(`After assign (buffId area): ${result.substring(start, end)}`);
            } else {
                console.log(`Before: ${beforeAssign.substring(0, 200)}`);
                console.log(`After: ${result.substring(0, 200)}`);
            }
        }

        return result;
    }

    /**
     * 修复getter函数体的完整性
     * 确保函数有正确的return语句和闭合括号
     */
    fixGetterImplementation(implementation) {
        if (!implementation || !implementation.trim()) {
            return implementation;
        }

        let fixed = implementation.trim();

        // 检查是否有未闭合的括号
        let openBraces = 0;
        let openParens = 0;
        let openBrackets = 0;

        for (let i = 0; i < fixed.length; i++) {
            const char = fixed[i];
            if (char === '{') openBraces++;
            else if (char === '}') openBraces--;
            else if (char === '(') openParens++;
            else if (char === ')') openParens--;
            else if (char === '[') openBrackets++;
            else if (char === ']') openBrackets--;
        }

        // 添加缺失的闭合括号
        while (openBraces > 0) {
            fixed += '\n        }';
            openBraces--;
        }
        while (openParens > 0) {
            fixed += ')';
            openParens--;
        }
        while (openBrackets > 0) {
            fixed += ']';
            openBrackets--;
        }

        // 检查是否有return语句
        const hasReturn = /\breturn\b/.test(fixed);

        // 如果没有return语句，但有变量声明或数组操作，尝试添加return
        if (!hasReturn) {
            // 查找可能需要返回的变量
            const varMatch = fixed.match(/var\s+(\w+)\s*=/);
            const letMatch = fixed.match(/let\s+(\w+)\s*=/);
            const constMatch = fixed.match(/const\s+(\w+)\s*=/);

            let returnVar = null;
            if (varMatch) returnVar = varMatch[1];
            else if (letMatch) returnVar = letMatch[1];
            else if (constMatch) returnVar = constMatch[1];

            // 如果找到了变量声明，添加return语句
            if (returnVar) {
                fixed += `\n        return ${returnVar};`;
            }
            // 如果没有变量声明但有数组操作，可能需要返回数组
            else if (fixed.includes('.push(') && !fixed.includes('return')) {
                // 查找数组变量名
                const arrayMatch = fixed.match(/(\w+)\.push\(/);
                if (arrayMatch) {
                    fixed += `\n        return ${arrayMatch[1]};`;
                }
            }
        }

        return fixed;
    }

    /**
     * 生成多个class的代码
     */
    generateMultipleClasses(analysis, fileName) {
        let code = '';

        // 添加文件头注释
        code += `/**\n * ${fileName}\n * 多个组件类 - 从编译后的JS反编译生成\n */\n\n`;

        // 添加必要的变量定义（简化版本）
        code += 'var cc__extends = __extends;\n';
        code += 'var cc__assign = __assign;\n';
        code += 'var cc__decorate = __decorate;\n\n';

        // 添加导入（如果有）
        if (analysis.imports.length > 0) {
            analysis.imports.forEach(imp => {
                code += `const ${imp.variable} = require('${imp.path}');\n`;
            });
            code += '\n';
        }

        // 提取exports信息
        const allExports = this.extractAllExports(analysis.originalContent || '');

        // 为每个class生成cc.Class定义
        analysis.classes.forEach((classInfo, index) => {
            if (index > 0) {
                code += '\n';
            }

            code += `// ${classInfo.className}\n`;

            // 查找对应的exports
            let matchingExport = allExports.find(exp =>
                exp.name === classInfo.className ||
                exp.value.includes(classInfo.className)
            );

            // 如果没有找到精确匹配，尝试按顺序匹配非枚举、非默认的exports
            if (!matchingExport) {
                const nonDefaultExports = allExports.filter(exp =>
                    exp.name !== 'default' && !exp.isEnum && !exp.isDefault
                );
                if (nonDefaultExports.length > index) {
                    matchingExport = nonDefaultExports[index];
                }
            }

            if (matchingExport && !matchingExport.isEnum) {
                code += `exports.${matchingExport.name} = cc.Class({\n`;
            } else {
                code += 'cc.Class({\n';
            }
            code += `    name: "${classInfo.className}",\n`;
            if (classInfo.baseClass) {
                code += `    extends: ${classInfo.baseClass},\n\n`;
            } else {
                code += '\n';
            }

            // 添加properties（不包括getter/setter）
            code += '    properties: {\n';
            if (classInfo.properties && classInfo.properties.length > 0) {
                classInfo.properties.forEach(prop => {
                    if (prop.type !== 'getter' && prop.type !== 'setter' && prop.type !== 'getter-setter') {
                        // 处理装饰器属性，添加正确的类型定义
                        if (prop.type && prop.type !== 'property') {
                            code += `        ${prop.name}: ${prop.type},\n`;
                        } else {
                            code += `        ${prop.name}: {\n`;
                            code += '            // TODO: 定义属性类型\n';
                            code += '        },\n';
                        }
                    }
                });
            }
            code += '    },\n\n';

            // 添加getter/setter方法
            if (classInfo.properties && classInfo.properties.length > 0) {
                // console.log(`Class ${classInfo.className} has ${classInfo.properties.length} properties`);
                classInfo.properties.forEach(prop => {
                    // console.log(`Property: ${prop.name}, type: ${prop.type}`);

                    // 确保属性名存在且不为空
                    if (!prop.name || !prop.name.trim()) {
                        // console.log(`Warning: Skipping property with empty name, type: ${prop.type}`);
                        return;
                    }

                    if (prop.type === 'getter-setter') {
                        // console.log(`Adding getter-setter for ${prop.name}`);
                        // 添加getter
                        code += `    get ${prop.name}() {\n`;
                        const getterLines = prop.getterImplementation.split('\n');
                        getterLines.forEach(line => {
                            if (line.trim()) {
                                code += '        ' + line.trim() + '\n';
                            }
                        });
                        code += '    },\n\n';

                        // 添加setter
                        code += `    set ${prop.name}(value) {\n`;
                        const setterLines = prop.setterImplementation.split('\n');
                        setterLines.forEach(line => {
                            if (line.trim()) {
                                // 不需要额外的参数替换，因为已经在 replaceSetterParameter 中处理了
                                let processedLine = line.trim();
                                code += '        ' + processedLine + '\n';
                            }
                        });
                        code += '    },\n\n';
                    } else if (prop.type === 'getter') {
                        // console.log(`Adding getter for ${prop.name}`);
                        code += `    get ${prop.name}() {\n`;
                        // 缩进getter实现代码
                        const lines = prop.implementation.split('\n');
                        lines.forEach(line => {
                            if (line.trim()) {
                                code += '        ' + line.trim() + '\n';
                            }
                        });
                        code += '    },\n\n';
                    } else if (prop.type === 'setter') {
                        // console.log(`Adding setter for ${prop.name}`);
                        code += `    set ${prop.name}(value) {\n`;
                        // 缩进setter实现代码
                        const lines = prop.implementation.split('\n');
                        lines.forEach(line => {
                            if (line.trim()) {
                                // 不需要额外的参数替换，因为已经在 replaceSetterParameter 中处理了
                                let processedLine = line.trim();
                                code += '        ' + processedLine + '\n';
                            }
                        });
                        code += '    },\n\n';
                    }
                });
            }

            // 添加方法
            if (classInfo.methods && classInfo.methods.length > 0) {
                classInfo.methods.forEach(method => {
                    const params = method.params.join(', ');

                    // ctor 方法的特殊处理
                    if (method.name === 'ctor') {
                        code += `    ctor() {\n`;
                    } else {
                        code += `    ${method.name}: function (${params}) {\n`;
                    }

                    if (method.body && method.body.trim()) {
                        // 缩进方法体
                        const lines = method.body.split('\n');
                        lines.forEach(line => {
                            if (line.trim()) {
                                code += '        ' + line.trim() + '\n';
                            } else {
                                code += '\n';
                            }
                        });
                    } else {
                        code += '        // TODO: 实现方法逻辑\n';
                    }

                    if (method.name === 'ctor') {
                        code += '    },\n\n';
                    } else {
                        code += '    },\n\n';
                    }
                });
            } else {
                // 如果没有方法，添加基本的生命周期方法
                code += '    // use this for initialization\n';
                code += '    onLoad: function () {\n';
                code += '    },\n\n';

                code += '    // called every frame, uncomment this function to activate update callback\n';
                code += '    // update: function (dt) {\n';
                code += '    // },\n\n';
            }

            code += '});\n';
        });

        return code;
    }

    /**
     * 检测模块类型
     */
    detectModuleType(content) {
        // 检查是否是枚举/常量定义模块
        if (this.isEnumModule(content)) {
            return 'enum';
        }

        // 检查是否是cc.Class组件类 - 主要通过 __decorate 判断
        if (content.includes('__decorate')) {
            return 'component';
        }

        // 检查是否是cc.Class组件类 - 备用判断
        if (content.includes('cc._decorator') || content.includes('@ccclass')) {
            return 'component';
        }

        // 检查是否是数据/枚举导出模块（exports.default = function() { return data; }）
        if (content.includes('exports.default = function ()') && content.includes('return o;')) {
            return 'enum';
        }

        // 检查是否是传统JavaScript模块（使用exports.default导出）
        if (content.includes('exports.default =') &&
            (content.includes('__extends(') || content.includes('function (e)') || content.includes('var $') || content.includes('this._systemName'))) {
            return 'module';
        }

        // 检查是否是静态类（IIFE模式）
        if (content.includes('(function ()') && content.includes('return e;')) {
            return 'static';
        }

        // 检查是否包含extends关键字但没有__decorate的传统模块
        if (content.includes('extends')) {
            return 'module';
        }

        // 默认为模块
        return 'module';
    }

    /**
     * 检测是否是枚举模块
     */
    isEnumModule(content) {
        // 检查TypeScript风格的枚举模式：!(function (e) { ... })(a || (a = {}));
        const tsEnumPattern = /!\(function\s*\(\w+\)\s*\{[\s\S]*?\}\)\(\w+\s*\|\|\s*\(\w+\s*=\s*\{\}\)\)/;
        if (tsEnumPattern.test(content)) {
            // 检查是否包含枚举赋值模式
            const enumAssignPattern = /\w+\[\(\w+\.\w+\s*=\s*[^\]]+\)\]\s*=\s*["'][^"']*["']/;
            if (enumAssignPattern.test(content)) {
                return true;
            }
        }

        // 检查IIFE模式的枚举定义
        const iifePattern = /\(function\s*\(\)\s*\{[\s\S]*?var\s+e\s*=\s*\{[\s\S]*?\}[\s\S]*?return\s+e;?[\s\S]*?\}\)\(\)/;
        if (iifePattern.test(content)) {
            // 进一步检查是否包含枚举特征
            const enumPatterns = [
                /var\s+e\s*=\s*\{[\s\S]*?\}/,  // var e = { ... }
                /e\.[\w]+\s*=\s*\d+/,           // e.property = number
                /e\.[\w]+\s*=\s*["'][^"']*["']/  // e.property = "string"
            ];

            return enumPatterns.some(pattern => pattern.test(content));
        }

        // 检查其他枚举模式
        const otherEnumPatterns = [
            /exports\.default\s*=\s*\{[\s\S]*?\}/,  // exports.default = { ... }
            /module\.exports\s*=\s*\{[\s\S]*?\}/,   // module.exports = { ... }
            /const\s+\w+\s*=\s*\{[\s\S]*?\}/       // const EnumName = { ... }
        ];

        return otherEnumPatterns.some(pattern => pattern.test(content));
    }

    /**
     * 提取类名
     */
    extractClassName(content, fileName) {
        // JavaScript关键字列表，不能作为类名
        const jsKeywords = ['function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch', 'finally', 'throw', 'new', 'this', 'super', 'class', 'extends', 'import', 'export', 'from', 'as'];

        // 尝试从exports.default或其他模式提取类名
        const patterns = [
            /exports\.(\w+)\s*=\s*undefined/, // 匹配 exports.ClassName = undefined
            /exports\.default\s*=\s*(\w+)/,
            /var\s+(\w+)\s*=.*cc\._decorator/,
            /function\s+(\w+)\s*\(/,
            /@ccclass\s*\(\s*["'](\w+)["']\s*\)/
        ];

        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match && match[1] && match[1] !== 'exports' && !jsKeywords.includes(match[1])) {
                // 如果提取到的类名是单字母变量，使用文件名代替
                if (match[1].length === 1 && fileName) {
                    return fileName;
                }
                return match[1];
            }
        }

        return fileName || 'UnknownClass';
    }

    /**
     * 提取基类
     */
    extractBaseClass(content) {
        // 查找IIFE模式的继承：}($2Pop.Pop); - 优先检查这个模式
        const iifePattern = /\}\s*\(\s*(\$\d*\w+(?:\.\w+)*)\s*\)\s*;?/;
        const iifeMatch = content.match(iifePattern);

        if (iifeMatch) {
            let baseClass = iifeMatch[1]; // 直接变量模式
            // 如果包含.default，去掉.default但保留后面的部分
            // if (baseClass && baseClass.includes('.default.')) {
            //     baseClass = baseClass.replace('.default.', '.');
            // } else if (baseClass && baseClass.endsWith('.default')) {
            //     baseClass = baseClass.replace('.default', '');
            // }
            return baseClass;
        }

        // 查找require模式的继承
        const requirePattern = /\}\)\s*\(\s*require\(["']\.\/([^"']+)["']\)\.default\s*\)\s*;?/;
        const requireMatch = content.match(requirePattern);

        if (requireMatch) {
            let baseClass = requireMatch[1];
            return baseClass;
        }

        // 查找其他模式的继承
        const patterns = [
            /extends\s+(\w+(?:\.\w+)*)/,
            /\w+\.prototype\s*=\s*Object\.create\s*\(\s*(\w+(?:\.\w+)*)\.prototype\s*\)/,
            /_super\s*=\s*(\w+(?:\.\w+)*)/,
            /__extends\s*\(\s*\w+\s*,\s*(\$?\w+(?:\.\w+)*)\s*\)/
        ];

        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match && match[1]) {
                // 转换常见的编译后基类名
                let baseClass = match[1];
                // 如果是参数e，需要检查IIFE模式来确定真实的基类
                if (baseClass === 'e') {
                    // 先尝试从IIFE模式获取真实基类
                    const iifePattern = /\}\s*\(\s*(\$\d*\w+(?:\.\w+)*)\s*\)\s*;?/;
                    const iifeMatch = content.match(iifePattern);
                    if (iifeMatch) {
                        let realBaseClass = iifeMatch[1];
                        // 如果包含.default，去掉.default但保留后面的部分
                        // if (realBaseClass && realBaseClass.includes('.default.')) {
                        //     realBaseClass = realBaseClass.replace('.default.', '.');
                        // } else if (realBaseClass && realBaseClass.endsWith('.default')) {
                        //     realBaseClass = realBaseClass.replace('.default', '');
                        // }
                        return realBaseClass;
                    }
                    // 如果没有找到IIFE模式，则返回cc.Component
                    return 'cc.Component';
                }
                if (baseClass === 'Component') {
                    return 'cc.Component';
                }
                // 如果包含.default，去掉.default但保留后面的部分
                // if (baseClass.includes('.default.')) {
                //     baseClass = baseClass.replace('.default.', '.');
                // } else if (baseClass.endsWith('.default')) {
                //     baseClass = baseClass.replace('.default', '');
                // }
                return baseClass;
            }
        }

        // 检查是否只有 ccclass 装饰器而没有继承
        // 如果只有 ccp_ccclass 装饰器，说明没有继承
        const onlyCcClassPattern = /cc__decorate\s*\(\s*\[\s*ccp_ccclass\s*\([^)]*\)\s*\]\s*,\s*\w+\s*\)/;
        if (onlyCcClassPattern.test(content)) {
            return null; // 没有继承
        }

        return 'cc.Component';
    }

    /**
     * 提取属性（只处理有装饰器信息的属性）
     */
    extractProperties(content) {
        const properties = [];
        const decoratedProperties = this.extractDecoratedProperties(content);

        // 处理有装饰器信息的属性
        for (const [name, decoratedInfo] of decoratedProperties) {
            if (!properties.find(p => p.name === name)) {
                const isArray = this.isArrayProperty(name, content);

                let finalType = decoratedInfo.type;
                // 只有在装饰器信息中没有已经处理过数组类型时才添加数组标记
                if (isArray && finalType && finalType !== 'null' && !finalType.startsWith('[')) {
                    finalType = `[${finalType}]`;
                }

                // 尝试从代码中找到默认值
                let defaultValue = 'null';
                const valuePattern = new RegExp(`(?:t\\.|this\\.)${name}\\s*=\\s*([^;\\n]+)`, 'g');
                const valueMatch = valuePattern.exec(content);
                if (valueMatch) {
                    defaultValue = this.parseDefaultValue(valueMatch[1]);
                }

                properties.push({
                    name: name,
                    type: finalType,
                    default: defaultValue,
                    displayName: decoratedInfo.displayName
                });
            }
        }

        // 提取Object.defineProperty定义的getter/setter
        const getterSetterProperties = this.extractGetterSetterProperties(content);
        getterSetterProperties.forEach(prop => {
            if (!properties.find(p => p.name === prop.name)) {
                properties.push(prop);
            }
        });

        return properties;
    }

    /**
     * 提取装饰器属性信息
     */
    extractDecoratedProperties(content) {
        const decoratedProperties = new Map();

        // 首先查找__decorate装饰器，提取类型信息（支持p和d变量）
        const decoratePattern = /__decorate\s*\(\s*\[\s*[pd]\s*\(\s*([^)]+)\s*\)\s*\]\s*,\s*t\.prototype\s*,\s*["'](\w+)["']\s*,\s*void\s+0\s*\)/g;
        let match;

        while ((match = decoratePattern.exec(content)) !== null) {
            const [, typeInfo, name] = match;
            const parsedInfo = this.parseDecoratorInfo(typeInfo);

            // 检查是否为数组类型，但只有在类型还不是数组格式时才添加数组标记
            const isArray = this.isArrayProperty(name, content);
            if (isArray && parsedInfo.type && parsedInfo.type !== 'null' && !parsedInfo.type.startsWith('[')) {
                parsedInfo.type = `[${parsedInfo.type}]`;
            }

            decoratedProperties.set(name, parsedInfo);
        }

        // 查找 cc__decorate([ccp_property(...)]) 模式的装饰器
        const ccDecoratePattern = /cc__decorate\s*\(\s*\[\s*ccp_property\s*\(\s*([^)]+)\s*\)\s*\]\s*,\s*_ctor\.prototype\s*,\s*["'](\w+)["']\s*,\s*undefined\s*\)/g;

        while ((match = ccDecoratePattern.exec(content)) !== null) {
            const [, typeInfo, name] = match;
            const parsedInfo = this.parseDecoratorInfo(typeInfo);

            // 检查是否为数组类型，但只有在类型还不是数组格式时才添加数组标记
            const isArray = this.isArrayProperty(name, content);
            if (isArray && parsedInfo.type && parsedInfo.type !== 'null' && !parsedInfo.type.startsWith('[')) {
                parsedInfo.type = `[${parsedInfo.type}]`;
            }

            decoratedProperties.set(name, parsedInfo);
        }

        return decoratedProperties;
    }

    /**
     * 提取Object.defineProperty定义的getter/setter属性
     */
    extractGetterSetterProperties(content) {
        console.log(`Extracting getter/setter properties from content length: ${content.length}`);
        const properties = [];

        // 使用新的精确匹配方法来处理完整的Object.defineProperty定义
        const propertyMatches = this.extractCompletePropertyDefinitions(content);
        console.log(`Found ${propertyMatches.length} property matches`);

        // 处理精确匹配的属性定义
        for (const propMatch of propertyMatches) {
            const propName = propMatch.name;
            const propContent = propMatch.content;

            if (propName) {
                // 检查是否有getter
                const getterBody = this.extractGetterSetterBody(propContent, 'get');
                // 检查是否有setter
                const setterBody = this.extractGetterSetterBody(propContent, 'set');

                if (getterBody && setterBody) {
                    // 同时有getter和setter
                    console.log(`Processing getter-setter for property: ${propName}`);
                    const paramName = this.extractSetterParameterName(propContent);
                    properties.push({
                        name: propName,
                        type: 'getter-setter',
                        getterBody: this.fixGetterImplementation(getterBody),
                        setterBody: this.replaceSetterParameter(setterBody, paramName),
                        isGetter: true,
                        isSetter: true
                    });
                } else if (getterBody) {
                    // 只有getter
                    console.log(`Processing getter for property: ${propName}`);
                    properties.push({
                        name: propName,
                        type: 'getter',
                        getterBody: this.fixGetterImplementation(getterBody),
                        isGetter: true
                    });
                } else if (setterBody) {
                    // 只有setter
                    console.log(`Processing setter for property: ${propName}`);
                    const paramName = this.extractSetterParameterName(propContent);
                    properties.push({
                        name: propName,
                        type: 'setter',
                        setterBody: this.replaceSetterParameter(setterBody, paramName),
                        isSetter: true
                    });
                }
            }
        }

        // 如果精确匹配没有找到任何属性，回退到原有的正则表达式匹配
        if (properties.length === 0) {
            // 匹配Object.defineProperty定义的getter/setter，支持任意变量名.prototype模式
            const definePropertyRegex = /Object\.defineProperty\(\s*([\w_]+)\.prototype\s*,\s*["'](\w+)["']\s*,\s*\{/gm;
            let match;

            while ((match = definePropertyRegex.exec(content)) !== null) {
                const [fullMatch, varName, propName] = match;

                // 从匹配位置开始，手动提取完整的属性体（处理嵌套大括号）
                const startIndex = match.index + fullMatch.length;
                const propBody = this.extractMethodBody(content, startIndex - 1); // -1 因为我们需要包含开始的 {

                if (!propBody) {
                    // console.log(`Failed to extract property body for ${propName}`);
                    continue;
                }

                // console.log(`Found property: ${propName}, varName: ${varName}`);
                // console.log(`Property body: ${propBody.substring(0, 100)}...`);
                // fs.writeFileSync('debug.log', `Complex regex found: ${propName}, varName: ${varName}\n`, { flag: 'a' });
                // fs.writeFileSync('debug.log', `Property body length: ${propBody.length}\n`, { flag: 'a' });
                // fs.writeFileSync('debug.log', `Property body preview: ${propBody.substring(0, 200)}\n`, { flag: 'a' });

                // 提取getter和setter方法体（处理嵌套大括号）
                // console.log('About to call extractMethodBody for get');
                let getterBody = null;
                let setterBody = null;
                try {
                    getterBody = this.extractGetterSetterBody(propBody, 'get');
                    // console.log('extractMethodBody for get completed');
                } catch (e) {
                    // console.log('Error in extractMethodBody for get:', e.message);
                    // fs.writeFileSync('debug.log', `Error in extractMethodBody for get: ${e.message}\n`, { flag: 'a' });
                }
                try {
                    setterBody = this.extractGetterSetterBody(propBody, 'set');
                    // console.log('extractMethodBody for set completed');
                } catch (e) {
                    // console.log('Error in extractMethodBody for set:', e.message);
                    // fs.writeFileSync('debug.log', `Error in extractMethodBody for set: ${e.message}\n`, { flag: 'a' });
                }
                // console.log('extractMethodBody calls completed');
                // fs.writeFileSync('debug.log', `Getter body: ${getterBody ? 'found' : 'not found'}\n`, { flag: 'a' });
                // fs.writeFileSync('debug.log', `Setter body: ${setterBody ? 'found' : 'not found'}\n`, { flag: 'a' });

                // 确保属性名存在且不为空
                if (propName && propName.trim()) {
                    if (getterBody && setterBody) {
                        // 同时包含getter和setter
                        properties.push({
                            name: propName,
                            type: 'getter_setter',
                            getterBody: getterBody,
                            setterBody: setterBody,
                            isGetter: true,
                            isSetter: true
                        });
                    } else if (getterBody) {
                        // 只有getter
                        properties.push({
                            name: propName,
                            type: 'getter',
                            getterBody: getterBody,
                            isGetter: true
                        });
                    } else if (setterBody) {
                        // 只有setter
                        properties.push({
                            name: propName,
                            type: 'setter',
                            setterBody: setterBody,
                            isSetter: true
                        });
                    }
                } else {
                    // console.log(`Warning: Found getter/setter with empty property name, skipping`);
                    // fs.writeFileSync('debug.log', `Warning: Found getter/setter with empty property name, skipping\n`, { flag: 'a' });
                }
            }
        }

        return properties;
    }

    /**
     * 提取setter参数名
     */
    extractSetterParameterName(propBody) {
        // 匹配 set: function(paramName) 模式
        const setterPattern = /set\s*:\s*function\s*\(\s*([^)]+)\s*\)/;
        const match = propBody.match(setterPattern);
        if (match && match[1]) {
            const paramName = match[1].trim();
            console.log(`Extracted setter parameter name: ${paramName} from: ${propBody.substring(0, 100)}`);
            return paramName;
        }
        console.log(`No setter parameter found, using default 'e'`);
        return 'e'; // 默认参数名
    }

    /**
     * 提取方法体（处理嵌套大括号）
     */
    extractGetterSetterBody(propBody, methodType) {
        // console.log(`\n=== extractMethodBody called for ${methodType} ===`);
        // try {
        //     fs.writeFileSync('debug.log', `\n=== extractMethodBody called for ${methodType} ===\n`, { flag: 'a' });
        // } catch (e) {
        //     console.log('Failed to write debug log:', e.message);
        // }
        // fs.writeFileSync('debug.log', `propBody type: ${typeof propBody}, length: ${propBody ? propBody.length : 'null'}\n`, { flag: 'a' });
        // fs.writeFileSync('debug.log', `methodType: ${methodType}\n`, { flag: 'a' });

        if (!propBody) {
            // fs.writeFileSync('debug.log', `propBody is null or undefined\n`, { flag: 'a' });
            return null;
        }

        // 根据方法类型构建正则表达式
        let pattern;
        if (methodType === 'get') {
            pattern = /get\s*:\s*function\s*\(\s*\)\s*\{/;
        } else if (methodType === 'set') {
            pattern = /set\s*:\s*function\s*\([^)]*\)\s*\{/;
        } else {
            return null;
        }

        const match = propBody.match(pattern);
        fs.writeFileSync('debug.log', `extractMethodBody ${methodType}: pattern=${pattern}, match=${match ? 'found' : 'not found'}\n`, { flag: 'a' });
        if (!match) {
            fs.writeFileSync('debug.log', `propBody sample: ${propBody.substring(0, 200)}\n`, { flag: 'a' });
            // 尝试更宽松的匹配模式
            let loosePattern;
            if (methodType === 'get') {
                loosePattern = /get\s*:\s*function/;
            } else if (methodType === 'set') {
                loosePattern = /set\s*:\s*function/;
            }

            const looseMatch = propBody.match(loosePattern);
            fs.writeFileSync('debug.log', `Trying loose pattern ${loosePattern}: ${looseMatch ? 'found' : 'not found'}\n`, { flag: 'a' });

            if (!looseMatch) {
                return null;
            }

            // 找到 get/set 关键字的位置
            const keywordIndex = propBody.indexOf(looseMatch[0]);
            // 从关键字开始找到第一个 {
            let braceIndex = propBody.indexOf('{', keywordIndex);
            if (braceIndex === -1) {
                return null;
            }

            // 从 { 开始匹配大括号
            let braceCount = 1;
            let i = braceIndex + 1;

            while (i < propBody.length && braceCount > 0) {
                if (propBody[i] === '{') {
                    braceCount++;
                } else if (propBody[i] === '}') {
                    braceCount--;
                }
                i++;
            }

            if (braceCount === 0) {
                const methodBody = propBody.substring(braceIndex + 1, i - 1).trim();
                fs.writeFileSync('debug.log', `Extracted ${methodType} body: ${methodBody.substring(0, 100)}...\n`, { flag: 'a' });
                return methodBody;
            }

            return null;
        }

        const startIndex = propBody.indexOf(match[0]) + match[0].length;
        let braceCount = 1;
        let i = startIndex;

        fs.writeFileSync('debug.log', `Starting brace matching from index ${startIndex}, initial braceCount: ${braceCount}\n`, { flag: 'a' });

        // 匹配大括号，找到方法结束
        while (i < propBody.length && braceCount > 0) {
            if (propBody[i] === '{') {
                braceCount++;
                fs.writeFileSync('debug.log', `Found '{' at ${i}, braceCount: ${braceCount}\n`, { flag: 'a' });
            } else if (propBody[i] === '}') {
                braceCount--;
                fs.writeFileSync('debug.log', `Found '}' at ${i}, braceCount: ${braceCount}\n`, { flag: 'a' });
            }
            i++;
        }

        fs.writeFileSync('debug.log', `Brace matching ended: braceCount=${braceCount}, i=${i}\n`, { flag: 'a' });

        if (braceCount === 0) {
            const methodBody = propBody.substring(startIndex, i - 1).trim();
            fs.writeFileSync('debug.log', `Extracted ${methodType} body: ${methodBody.substring(0, 100)}...\n`, { flag: 'a' });
            return methodBody;
        }

        fs.writeFileSync('debug.log', `Failed to extract ${methodType} body: braceCount=${braceCount}\n`, { flag: 'a' });
        return null;
    }

    /**
     * 提取完整的构造函数代码（处理嵌套大括号）
     */
    extractConstructorFunction(content) {
        const functionStart = content.indexOf('function t()');
        if (functionStart === -1) return null;

        let braceCount = 0;
        let i = functionStart;
        let foundFirstBrace = false;

        // 找到函数开始的大括号
        while (i < content.length) {
            if (content[i] === '{') {
                foundFirstBrace = true;
                braceCount = 1;
                i++;
                break;
            }
            i++;
        }

        if (!foundFirstBrace) return null;

        const start = i;

        // 匹配大括号，找到函数结束
        while (i < content.length && braceCount > 0) {
            if (content[i] === '{') {
                braceCount++;
            } else if (content[i] === '}') {
                braceCount--;
            }
            i++;
        }

        if (braceCount === 0) {
            return 'function t() {' + content.substring(start, i - 1) + '}';
        }

        return null;
    }

    /**
     * 提取构造函数内容
     */
    extractConstructorContent(content) {
        const constructorLines = [];

        // 查找构造函数中的属性定义（t.propertyName = value）
        // 只匹配在构造函数内部的简单赋值
        const constructorMatch = this.extractConstructorFunction(content);
        if (!constructorMatch) return constructorLines;

        const constructorContent = constructorMatch;

        // 获取所有有装饰器的属性
        const decoratedProperties = this.extractDecoratedProperties(content);

        // 更复杂的模式匹配，处理多行数组和对象
        const lines = constructorContent.split('\n');
        let i = 0;

        while (i < lines.length) {
            const line = lines[i].trim();
            const match = line.match(/t\.(\w+)\s*=\s*(.*)/);

            if (match) {
                const [, name, valueStart] = match;

                // 只有没有装饰器的属性才放入构造函数
                if (!decoratedProperties.has(name)) {
                    let value = valueStart;

                    // 如果值以 [ 开始但不以 ] 结束，说明是多行数组
                    if (value.trim().startsWith('[') && !value.trim().endsWith('];') && !value.trim().endsWith(']')) {
                        // 继续读取直到找到结束的 ]
                        let j = i + 1;
                        let bracketCount = 0; // 跟踪括号层级

                        // 计算第一行中的括号数量
                        for (let k = 0; k < value.length; k++) {
                            if (value[k] === '[') bracketCount++;
                            else if (value[k] === ']') bracketCount--;
                        }

                        while (j < lines.length && bracketCount > 0) {
                            const nextLine = lines[j].trim();

                            // 计算当前行的括号数量
                            for (let k = 0; k < nextLine.length; k++) {
                                if (nextLine[k] === '[') bracketCount++;
                                else if (nextLine[k] === ']') bracketCount--;
                            }

                            // 添加当前行
                            if (nextLine === '];' || nextLine === ']') {
                                value += '\n        ' + nextLine;
                            } else {
                                value += '\n            ' + nextLine;
                            }

                            // 如果括号已经平衡，结束循环
                            if (bracketCount === 0) {
                                i = j - 1; // 更新外层循环的索引，减1是因为外层循环会执行i++
                                break;
                            }
                            j++;
                        }
                    }
                    // 如果值以 { 开始但不以 } 结束，说明是多行对象
                    else if (value.trim().startsWith('{') && !value.trim().endsWith('};') && !value.trim().endsWith('}')) {
                        // 继续读取直到找到结束的 }
                        let j = i + 1;
                        while (j < lines.length) {
                            const nextLine = lines[j].trim();
                            // 检查是否是单独的结束符号行或包含结束符号的行
                            if (nextLine === '};' || nextLine === '}' || nextLine.endsWith('};') || nextLine.endsWith('}')) {
                                // 如果是单独的结束符号，直接添加
                                if (nextLine === '};' || nextLine === '}') {
                                    value += '\n        ' + nextLine;
                                } else {
                                    // 如果行包含其他内容但以结束符号结尾，保持原有缩进
                                    value += '\n            ' + nextLine;
                                }
                                i = j - 1; // 更新外层循环的索引，减1是因为外层循环会执行i++
                                break;
                            } else {
                                value += '\n            ' + nextLine;
                            }
                            j++;
                        }
                    }

                    // 清理值，但保留数组和对象的结束符号
                    let cleanValue = value.trim();
                    // 只移除单独的分号，不移除数组结束符号 ]; 或对象结束符号 };
                    if (cleanValue.endsWith(';') && !cleanValue.endsWith('};') && !cleanValue.endsWith('];')) {
                        cleanValue = cleanValue.slice(0, -1);
                    }

                    // 过滤掉明显的条件判断或复杂表达式，只保留简单的赋值
                    if (!cleanValue.includes('==') && !cleanValue.includes('!=') &&
                        !cleanValue.includes('&&') && !cleanValue.includes('||') &&
                        !cleanValue.includes('if') && !cleanValue.includes('for') &&
                        !cleanValue.includes('new cc.SpriteFrame') &&
                        !cleanValue.includes('function') && !cleanValue.includes('(')) {
                        // 如果值已经以分号结尾，就不再添加分号
                        const finalValue = cleanValue.endsWith(';') ? cleanValue : cleanValue + ';';
                        constructorLines.push(`        this.${name} = ${finalValue}`);
                    }
                }
            }
            i++;
        }

        return constructorLines;
    }

    /**
     * 解析装饰器信息
     */
    parseDecoratorInfo(decoratorStr) {
        if (!decoratorStr) return null;

        // 如果是简单类型（如 cc.Node）
        if (!decoratorStr.includes('{')) {
            return {
                type: this.parsePropertyType(decoratorStr.trim()),
                displayName: null
            };
        }

        // 解析对象形式的装饰器信息
        const result = {
            type: null,
            displayName: null
        };

        // 提取type
        const typeMatch = decoratorStr.match(/type\s*:\s*([^,}]+)/);
        if (typeMatch) {
            result.type = this.parsePropertyType(typeMatch[1].trim());
        }

        // 提取displayName
        const displayNameMatch = decoratorStr.match(/displayName\s*:\s*["']([^"']*)["']/);
        if (displayNameMatch) {
            result.displayName = displayNameMatch[1];
        }

        return result;
    }

    /**
     * 解析属性类型
     */
    parsePropertyType(typeStr) {
        if (!typeStr) return null;

        const cleanType = typeStr.trim();

        // 检查是否是数组类型 [Type]
        const arrayMatch = cleanType.match(/^\[(.+)\]$/);
        if (arrayMatch) {
            const innerType = arrayMatch[1].trim();
            const mappedInnerType = this.mapBasicType(innerType);
            return `[${mappedInnerType}]`;
        }

        // 处理普通类型
        return this.mapBasicType(cleanType);
    }

    /**
     * 映射基础类型
     */
    mapBasicType(typeStr) {
        const typeMap = {
            'cc.Node': 'cc.Node',
            'cc.Label': 'cc.Label',
            'cc.Sprite': 'cc.Sprite',
            'cc.Button': 'cc.Button',
            'cc.Prefab': 'cc.Prefab',
            'cc.SpriteFrame': 'cc.SpriteFrame',
            'cc.JsonAsset': 'cc.JsonAsset',
            'cc.ProgressBar': 'cc.ProgressBar',
            'cc.RichText': 'cc.RichText',
            'cc.EditBox': 'cc.EditBox',
            'cc.ScrollView': 'cc.ScrollView',
            'cc.Slider': 'cc.Slider',
            'cc.ToggleContainer': 'cc.ToggleContainer',
            'sp.Skeleton': 'sp.Skeleton',
            'Number': 'cc.Float',
            'String': 'cc.String',
            'Boolean': 'cc.Boolean',
            'any': null
        };

        return typeMap[typeStr] || typeStr;
    }

    /**
     * 检查属性是否为数组类型
     */
    isArrayProperty(name, content) {
        // 检查构造函数中是否初始化为数组
        const patterns = [
            new RegExp(`t\\.${name}\\s*=\\s*\\[\\]`),
            new RegExp(`this\\.${name}\\s*=\\s*\\[\\]`),
            new RegExp(`${name}\\s*=\\s*\\[\\]`)
        ];

        return patterns.some(pattern => pattern.test(content));
    }

    /**
     * 从值推断类型
     */
    inferTypeFromValue(value) {
        if (value === 'null' || value === 'undefined') return null;
        if (value === 'true' || value === 'false' || value === '!0' || value === '!1') return 'cc.Boolean';
        if (/^\d+(\.\d+)?$/.test(value)) return 'cc.Float';
        if (/^["'].*["']$/.test(value)) return 'cc.String';
        return null;
    }

    /**
     * 解析默认值
     */
    parseDefaultValue(value) {
        if (!value) return 'null';

        const cleanValue = value.trim().replace(/[;,]$/, '');

        if (cleanValue === 'null' || cleanValue === 'undefined') return 'null';
        if (cleanValue === 'true' || cleanValue === 'false') return cleanValue;
        if (cleanValue === '!0') return 'true';
        if (cleanValue === '!1') return 'false';
        if (/^\d+(\.\d+)?$/.test(cleanValue)) return cleanValue;
        if (/^["'].*["']$/.test(cleanValue)) return cleanValue;
        if (cleanValue === '[]') return '[]';
        if (cleanValue === '{}') return '{}';

        return 'null';
    }

    /**
     * 提取方法
     */
    extractMethods(content) {
        const methods = [];
        const methodNames = new Set(); // 用于去重
        let match;

        // 首先查找原型方法 - 优先处理，避免被静态方法模式误匹配
        const prototypePattern = /(\w+)\.prototype\.(\w+)\s*=\s*function\s*\(([^)]*)\)\s*\{/g;
        while ((match = prototypePattern.exec(content)) !== null) {
            const [, className, methodName, params] = match;
            if (!methodNames.has(methodName)) {
                const methodStart = match.index + match[0].length;
                const methodBody = this.extractMethodBody(content, methodStart - 1);

                methods.push({
                    name: methodName,
                    params: this.parseParameters(params),
                    body: this.cleanMethodBody(methodBody),
                    isStatic: false  // 明确标记为实例方法
                });
                methodNames.add(methodName);

            }
        }

        // 然后查找真正的静态方法（排除原型方法）
        // 静态方法模式：e.methodName = function，但不是 e.prototype.methodName 或 t.prototype.methodName
        const staticMethodPattern = /e\.(\w+)\s*=\s*function\s*\(([^)]*)\)\s*\{/g;

        while ((match = staticMethodPattern.exec(content)) !== null) {
            const [fullMatch, methodName, params] = match;
            // 检查前面是否有 prototype.
            const beforeMatch = content.substring(Math.max(0, match.index - 15), match.index);
            const isPrototypeMethod = beforeMatch.includes('prototype.');

            if (!isPrototypeMethod && !methodNames.has(methodName)) {
                const methodStart = match.index + match[0].length;
                const methodBody = this.extractMethodBody(content, methodStart - 1);

                methods.push({
                    name: methodName,
                    params: this.parseParameters(params),
                    body: this.cleanMethodBody(methodBody),
                    isStatic: true
                });
                methodNames.add(methodName);

            }
        }

        // 查找其他方法定义模式（排除getter/setter）
        const functionPattern = /(\w+)\s*:\s*function\s*\(([^)]*)\)\s*\{/g;
        while ((match = functionPattern.exec(content)) !== null) {
            const [, methodName, params] = match;
            // 排除getter和setter
            if (methodName !== 'get' && methodName !== 'set' && !methodNames.has(methodName)) {
                const methodStart = match.index + match[0].length;
                const methodBody = this.extractMethodBody(content, methodStart - 1);

                methods.push({
                    name: methodName,
                    params: this.parseParameters(params),
                    body: this.cleanMethodBody(methodBody)
                });
                methodNames.add(methodName);
            }
        }

        return methods;
    }

    /**
     * 解析参数
     */
    parseParameters(paramsStr) {
        if (!paramsStr || !paramsStr.trim()) return [];

        return paramsStr.split(',').map(param => {
            const cleanParam = param.trim();
            return cleanParam || 'param';
        }).filter(param => param && param !== 'param');
    }

    /**
     * 提取方法体（处理嵌套大括号）
     */
    extractMethodBody(content, startIndex) {
        let braceCount = 1;
        let i = startIndex + 1;

        while (i < content.length && braceCount > 0) {
            if (content[i] === '{') {
                braceCount++;
            } else if (content[i] === '}') {
                braceCount--;
            }
            i++;
        }

        return content.substring(startIndex + 1, i - 1);
    }

    /**
     * 清理方法体
     */
    cleanMethodBody(body) {
        if (!body) return '';

        let cleanBody = body.trim();

        // 移除编译器添加的代码
        cleanBody = cleanBody.replace(/var\s+_this\s*=\s*this;?\s*/g, '');
        cleanBody = cleanBody.replace(/return\s+_this;?\s*/g, '');
        cleanBody = cleanBody.replace(/\w+\.__super__\.\w+\.call\(this[^)]*\);?\s*/g, '');

        // 移除多余的空行
        cleanBody = cleanBody.replace(/\n\s*\n\s*\n/g, '\n\n');

        // 保留$前缀的变量名，不进行替换
        // cleanBody = cleanBody.replace(/\$([a-zA-Z]\w*)/g, '$1');

        // 修复var声明中的this赋值错误
        cleanBody = cleanBody.replace(/var\s+this\s*=\s*this;?/g, 'var self = this;');
        cleanBody = cleanBody.replace(/var\s+(\w+)\s*=\s*this;/g, 'var $1 = this;');

        // 修复函数中的this引用
        cleanBody = cleanBody.replace(/\bthis\b(?=\s*\()/g, 'self');

        // 简化常见模式
        cleanBody = cleanBody.replace(/this\.(\w+)\s*&&\s*this\.\1\(\)/g, 'this.$1 && this.$1()');

        // 替换$前缀的变量名为标准变量名
        cleanBody = cleanBody.replace(/\$eventManager/g, 'eventManager');
        cleanBody = cleanBody.replace(/\$gameConfig/g, 'gameConfig');
        cleanBody = cleanBody.replace(/\$globalEnum/g, 'globalEnum');
        cleanBody = cleanBody.replace(/\$gameEventType/g, 'gameEventType');

        // 移除模块调用中的 .default
        // cleanBody = cleanBody.replace(/(\w+)\.default\./g, '$1.');
        // cleanBody = cleanBody.replace(/(\w+)\.default\(/g, '$1(');
        // cleanBody = cleanBody.replace(/(\w+)\.default;/g, '$1;');
        // cleanBody = cleanBody.replace(/(\w+)\.default$/g, '$1');
        // cleanBody = cleanBody.replace(/(\w+)\.default\s*\)/g, '$1)');
        // cleanBody = cleanBody.replace(/(\w+)\.default\s*,/g, '$1,');
        // cleanBody = cleanBody.replace(/(\w+)\.default\s*\]/g, '$1]');
        // cleanBody = cleanBody.replace(/(\w+)\.default\s*\}/g, '$1}');

        // 处理基类调用 - 将 e.prototype.methodName.call(this) 替换为 this._super()
        // 首先处理最常见的模式
        cleanBody = cleanBody.replace(/e\.prototype\._Initialize\.call\(this\)/g, 'this._super()');
        cleanBody = cleanBody.replace(/e\.prototype\.Initialize\.call\(this\)/g, 'this._super()');
        cleanBody = cleanBody.replace(/e\.prototype\.start\.call\(this\)/g, 'this._super()');

        // 处理通用的基类调用模式
        cleanBody = cleanBody.replace(/e\.prototype\.(\w+)\.call\(this(?:,\s*([^)]*))?\)/g, (match, methodName, args) => {
            if (args) {
                return `this._super(${args})`;
            } else {
                return 'this._super()';
            }
        });

        // 处理其他形式的基类调用
        cleanBody = cleanBody.replace(/(\w+)\.prototype\.(\w+)\.call\(this(?:,\s*([^)]*))?\)/g, (match, className, methodName, args) => {
            if (args) {
                return `this._super(${args})`;
            } else {
                return 'this._super()';
            }
        });

        // 修复对象定义不完整的问题
        cleanBody = this.fixIncompleteObjectDefinitions(cleanBody);

        return cleanBody.trim();
    }

    /**
     * 修复对象定义不完整的问题
     */
    fixIncompleteObjectDefinitions(body) {
        if (!body) return body;

        let fixedBody = body;

        // 查找对象定义模式：var o = { ... ishideUI: 1 但后面没有正确的闭合
        // 使用更精确的正则表达式来匹配不完整的对象定义
        const lines = fixedBody.split('\n');
        let inObjectDef = false;
        let objectVarName = '';
        let objectStartIndex = -1;
        let braceCount = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // 检测对象定义开始
            const objectStartMatch = line.match(/var\s+(\w+)\s*=\s*\{/);
            if (objectStartMatch && !inObjectDef) {
                inObjectDef = true;
                objectVarName = objectStartMatch[1];
                objectStartIndex = i;
                braceCount = 1;
                // 检查同一行是否有更多的大括号
                const openBraces = (line.match(/\{/g) || []).length;
                const closeBraces = (line.match(/\}/g) || []).length;
                braceCount = openBraces - closeBraces;
                continue;
            }

            if (inObjectDef) {
                // 计算当前行的大括号数量
                const openBraces = (line.match(/\{/g) || []).length;
                const closeBraces = (line.match(/\}/g) || []).length;
                braceCount += openBraces - closeBraces;

                // 检查是否是 ishideUI: 1 行
                if (line.includes('ishideUI: 1')) {
                    // 检查下一行是否正确闭合对象
                    const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
                    const nextNextLine = i + 2 < lines.length ? lines[i + 2].trim() : '';

                    // 如果下一行是 }, 且再下一行是 }); 这种模式，说明对象定义不完整
                    if (nextLine === '},' && nextNextLine === '});') {
                        // 修复对象定义：将 }, 改为 };
                        lines[i + 1] = lines[i + 1].replace('},', '};');

                        // 查找后续的petList.forEach调用模式
                        let foundPetListCall = false;
                        for (let j = i + 3; j < Math.min(i + 15, lines.length); j++) {
                            if (lines[j].includes('this.role.petList.forEach')) {
                                foundPetListCall = true;
                                break;
                            }
                        }

                        if (foundPetListCall) {
                            // 如果找到petList.forEach调用，添加对应的代码
                            lines[i + 1] += `\n        this.role.petList.forEach(function (e) {\n            return e.addBuffByData(${objectVarName});\n        });`;
                        } else {
                            // 否则使用默认的addBuff调用
                            lines[i + 1] += `\n        this.role.addBuff(${objectVarName});`;
                        }

                        // 删除多余的 }); 行
                        lines.splice(i + 2, 1);

                        inObjectDef = false;
                        objectVarName = '';
                        braceCount = 0;
                        continue;
                    }
                }

                // 如果大括号已经平衡，对象定义结束
                if (braceCount === 0) {
                    inObjectDef = false;
                    objectVarName = '';
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * 提取导入语句和全局变量定义
     */
    extractImports(content) {
        const imports = [];

        // 查找require语句（包括$前缀的变量），但排除注释的
        const requirePattern = /(?:var|const|let)\s+(\$?\w+)\s*=\s*require\s*\(\s*["']([^"']+)["']\s*\)/g;
        let match;

        while ((match = requirePattern.exec(content)) !== null) {
            const [fullMatch, variable, path] = match;

            // 检查这个匹配是否在注释中
            const matchIndex = match.index;
            const lineStart = content.lastIndexOf('\n', matchIndex) + 1;
            const lineContent = content.substring(lineStart, content.indexOf('\n', matchIndex));

            // 如果这一行以 // 开头（忽略空白字符），则跳过
            if (lineContent.trim().startsWith('//')) {
                continue;
            }

            // 保留原始变量名（包括$前缀）
            imports.push({
                variable: variable,
                path: path
            });
        }

        // 查找编译后的模块引用模式
        const modulePattern = /var\s+(\w+)\s*=\s*__webpack_require__\(\d+\)/g;
        while ((match = modulePattern.exec(content)) !== null) {
            const [, variable] = match;
            // 尝试从变量名推断模块路径
            let modulePath = variable;
            if (variable === 'eventManager') {
                modulePath = './EventManager';
            } else if (variable === 'gameConfig') {
                modulePath = './GameConfig';
            } else if (variable === 'globalEnum') {
                modulePath = './GlobalEnum';
            } else if (variable === 'gameEventType') {
                modulePath = './GameEventType';
            }

            imports.push({
                variable: variable,
                path: modulePath
            });
        }

        return imports;
    }

    /**
     * 提取文件开头的变量声明和枚举定义
     */
    extractGlobalDeclarations(content) {
        const declarations = [];
        const usedVars = new Set();

        // 首先提取枚举定义 (function (e) { ... })(a = exports.ColliderType || (exports.ColliderType = {}));
        const enumPattern = /\(function\s*\(\w+\)\s*\{([\s\S]*?)\}\)\((\w+)\s*=\s*exports\.(\w+)\s*\|\|\s*\(exports\.(\w+)\s*=\s*\{\}\)\);/g;
        let match;

        while ((match = enumPattern.exec(content)) !== null) {
            const [fullMatch, enumBody, varName, enumName1, enumName2] = match;
            const enumName = enumName1 || enumName2;

            // 解析枚举体
            const enumEntries = [];
            const entryPattern = /\w+\[\w+\.(\w+)\s*=\s*(\d+)\]\s*=\s*["'](\w+)["']/g;
            let entryMatch;

            while ((entryMatch = entryPattern.exec(enumBody)) !== null) {
                const [, key, value, stringValue] = entryMatch;
                enumEntries.push({ key, value, stringValue });
            }

            declarations.push({
                type: 'enum',
                name: enumName,
                varName: varName,
                entries: enumEntries,
                fullDefinition: fullMatch
            });

            // 记录已使用的变量名
            usedVars.add(varName);
        }

        // 提取直接使用 exports.EnumName 的枚举定义（没有变量赋值）
        const directEnumPattern = /\(function\s*\(\w+\)\s*\{([\s\S]*?)\}\)\(exports\.(\w+)\s*\|\|\s*\(exports\.(\w+)\s*=\s*\{\}\)\);/g;

        while ((match = directEnumPattern.exec(content)) !== null) {
            const [fullMatch, enumBody, enumName1, enumName2] = match;
            const enumName = enumName1 || enumName2;

            // 解析枚举体
            const enumEntries = [];
            const entryPattern = /\w+\[\w+\.(\w+)\s*=\s*(\d+)\]\s*=\s*["'](\w+)["']/g;
            let entryMatch;

            while ((entryMatch = entryPattern.exec(enumBody)) !== null) {
                const [, key, value, stringValue] = entryMatch;
                enumEntries.push({ key, value, stringValue });
            }

            declarations.push({
                type: 'directEnum',
                name: enumName,
                entries: enumEntries,
                fullDefinition: fullMatch
            });
        }

        // 然后提取其他var声明（排除已在枚举中使用的变量）
        // 匹配简单的var声明：var varName;
        const simpleVarPattern = /^var\s+(\w+);$/gm;
        while ((match = simpleVarPattern.exec(content)) !== null) {
            const [, varName] = match;
            if (!usedVars.has(varName)) {
                declarations.push({
                    type: 'variable',
                    name: varName,
                    declaration: `var ${varName};`
                });
                usedVars.add(varName);
            }
        }

        // 匹配带初始化的var声明：var varName = expression;
        // 排除函数定义和复杂的表达式
        const initVarPattern = /^var\s+(\w+)\s*=\s*([^;]+);$/gm;
        while ((match = initVarPattern.exec(content)) !== null) {
            const [fullMatch, varName, expression] = match;
            // 排除函数定义和复杂的对象/函数表达式
            if (!usedVars.has(varName) &&
                !expression.trim().startsWith('function') &&
                !expression.includes('{') &&
                !expression.includes('require(')) {
                declarations.push({
                    type: 'variable',
                    name: varName,
                    declaration: fullMatch
                });
                usedVars.add(varName);
            }
        }

        return declarations;
    }

    /**
     * 生成原始代码格式
     */
    generateOriginalCCClass(analysis, fileName) {
        // console.log(`generateOriginalCCClass: isMultipleClasses=${analysis.isMultipleClasses}`);

        if (analysis.isMultipleClasses) {
            // console.log(`Processing multiple classes: ${analysis.classes.length}`);
            return this.generateMultipleClasses(analysis, fileName);
        }

        // 单个class的情况（保持原有逻辑）
        if (analysis.moduleType === 'enum') {
            return this.generateEnumClass(analysis, fileName);
        } else if (analysis.moduleType === 'static') {
            return this.generateStaticClass(analysis, fileName);
        } else if (analysis.moduleType === 'module') {
            return this.generateModuleClass(analysis, fileName);
        } else {
            // 创建一个对象来存储处理过的导出，避免重复导出
            analysis.processedExports = new Set();
            return this.generateComponentClass(analysis, fileName);
        }
    }

    /**
     * 提取所有exports导出
     */
    extractAllExports(content) {
        const exports = [];

        // 匹配 exports.name = value 模式，但排除一些特殊情况
        const exportsPattern = /exports\.(\w+)\s*=\s*([^;\n]+)/g;
        let match;

        while ((match = exportsPattern.exec(content)) !== null) {
            const [, exportName, exportValue] = match;
            const trimmedValue = exportValue.trim();

            // 跳过枚举导出模式中的exports.name = {}和一些特殊情况
            if (!trimmedValue.startsWith('{}') &&
                !trimmedValue.includes('||') &&
                !trimmedValue.includes('undefined') &&
                !trimmedValue.includes('exports.')) {
                exports.push({
                    name: exportName,
                    value: trimmedValue,
                    isDefault: exportName === 'default'
                });
            }
        }

        // 匹配枚举导出模式: (function(e) { ... })(s = exports.CampType || (exports.CampType = {}));
        const enumExportPattern = /\}\)\(\w+\s*=\s*exports\.(\w+)\s*\|\|\s*\(exports\.\1\s*=\s*\{\}\)\)/g;
        while ((match = enumExportPattern.exec(content)) !== null) {
            const [, enumName] = match;
            if (!exports.find(e => e.name === enumName)) {
                exports.push({
                    name: enumName,
                    value: enumName,
                    isEnum: true
                });
            }
        }

        // 检查是否有exports.default（但不是在赋值语句中）
        if (content.includes('exports.default =') && content.includes('def_')) {
            if (!exports.find(e => e.name === 'default')) {
                exports.push({
                    name: 'default',
                    value: 'cc.Class',
                    isDefault: true
                });
            }
        }

        return exports;
    }

    /**
     * 提取枚举值和静态属性
     */
    extractEnumValues(content) {
        const enumValues = {};
        const staticProperties = {};

        // 提取TypeScript风格的枚举值：e[(e.property = value)] = "property"
        const tsEnumPattern = /(\w+)\[\((\w+)\.(\w+)\s*=\s*([^\)]+)\)\]\s*=\s*["']([^"']*)["']/g;
        let match;
        while ((match = tsEnumPattern.exec(content)) !== null) {
            const [, , , key, value,] = match;
            enumValues[key] = value.trim();
        }

        // 提取静态属性赋值：t.PropertyName = variableName
        const staticPropPattern = /(\w+)\.(\w+)\s*=\s*(\w+);/g;
        while ((match = staticPropPattern.exec(content)) !== null) {
            const [, , propName, varName] = match;
            // 检查变量名是否对应枚举
            if (this.isEnumVariable(content, varName)) {
                staticProperties[propName] = varName;
            }
        }

        // 提取IIFE模式中的枚举值
        const iifeMatch = content.match(/\(function\s*\(\)\s*\{([\s\S]*?)\}\)\(\)/);
        if (iifeMatch) {
            const iifeContent = iifeMatch[1];

            // 查找 var e = { ... } 模式
            const objMatch = iifeContent.match(/var\s+e\s*=\s*\{([\s\S]*?)\}/);
            if (objMatch) {
                const objContent = objMatch[1];
                // 提取对象属性
                const propPattern = /([\w]+)\s*:\s*([^,}]+)/g;
                let match;
                while ((match = propPattern.exec(objContent)) !== null) {
                    const [, key, value] = match;
                    enumValues[key] = value.trim();
                }
            }

            // 查找 e.property = value 模式
            const assignPattern = /e\.(\w+)\s*=\s*([^;]+);/g;
            let match;
            while ((match = assignPattern.exec(iifeContent)) !== null) {
                const [, key, value] = match;
                enumValues[key] = value.trim();
            }
        }

        // 提取其他模式的枚举值
        const directObjPattern = /(?:exports\.default|module\.exports|const\s+\w+)\s*=\s*\{([\s\S]*?)\}/;
        const directMatch = content.match(directObjPattern);
        if (directMatch) {
            const objContent = directMatch[1];
            const propPattern = /([\w]+)\s*:\s*([^,}]+)/g;
            let match;
            while ((match = propPattern.exec(objContent)) !== null) {
                const [, key, value] = match;
                enumValues[key] = value.trim();
            }
        }

        return { enumValues, staticProperties };
    }

    /**
     * 检查变量是否是枚举变量
     */
    isEnumVariable(content, varName) {
        // 检查是否有对应的枚举IIFE定义
        const enumDefPattern = new RegExp(`!\\(function\\s*\\(\\w+\\)\\s*\\{[\\s\\S]*?\\}\\)\\(${varName}\\s*\\|\\|\\s*\\(${varName}\\s*=\\s*\\{\\}\\)\\)`);
        return enumDefPattern.test(content);
    }

    /**
     * 生成枚举类代码
     */
    generateEnumClass(analysis, fileName) {
        let code = '';

        // 添加文件头注释
        code += `/**\n * ${fileName}\n * 枚举/数据模块 - 从编译后的JS反编译生成\n */\n\n`;

        // 添加导入（如果有）
        if (analysis.imports.length > 0) {
            analysis.imports.forEach(imp => {
                code += `const ${imp.variable} = require('${imp.path}');\n`;
            });
            code += '\n';
        }

        // 检查是否是gameMode_Json.js这种特殊格式
        if (analysis.originalContent.includes('exports.GAME_MODE') && analysis.originalContent.includes('exports.default = function ()')) {
            return this.generateGameModeClass(analysis, fileName);
        }

        // 提取原始内容中的枚举值和静态属性
        const { enumValues, staticProperties } = this.extractEnumValues(analysis.originalContent || '');

        // 生成枚举对象
        const enumName = fileName.charAt(0).toUpperCase() + fileName.slice(1);
        code += `const ${enumName} = {\n`;

        const entries = Object.entries(enumValues);
        entries.forEach(([key, value], index) => {
            code += `    ${key}: ${value}`;
            if (index < entries.length - 1) {
                code += ',';
            }
            code += '\n';
        });

        // 如果没有找到枚举值，添加一个示例
        if (entries.length === 0) {
            code += '    // TODO: 添加枚举值\n';
            code += '    // EXAMPLE: 0\n';
        }

        code += '};\n\n';

        // 添加静态属性（如果有）
        if (Object.keys(staticProperties).length > 0) {
            Object.entries(staticProperties).forEach(([propName, enumVar]) => {
                code += `${enumName}.${propName} = ${enumName};\n`;
            });
            code += '\n';
        }

        code += `module.exports = ${enumName};\n`;

        return code;
    }

    /**
     * 生成静态类代码
     */
    generateStaticClass(analysis, fileName) {
        let code = '';

        // 添加文件头注释
        code += `/**\n * ${fileName}\n * 静态类 - 从编译后的JS反编译生成\n */\n\n`;

        // 添加导入（如果有）
        if (analysis.imports.length > 0) {
            analysis.imports.forEach(imp => {
                code += `const ${imp.variable} = require('${imp.path}');\n`;
            });
            code += '\n';
        }

        // 生成静态类结构
        const className = fileName.charAt(0).toUpperCase() + fileName.slice(1);
        code += `const ${className} = {\n`;

        // 收集所有成员（属性和方法）
        const allMembers = [];

        // 添加静态属性（排除与方法同名的）
        const staticProps = analysis.properties.filter(p => p.isStatic);
        const methodNames = analysis.methods.map(m => m.name);

        staticProps.forEach(prop => {
            if (!methodNames.includes(prop.name)) {
                allMembers.push({
                    type: 'property',
                    name: prop.name,
                    value: prop.default
                });
            }
        });

        // 添加方法
        analysis.methods.forEach(method => {
            allMembers.push({
                type: 'method',
                name: method.name,
                params: method.params,
                body: method.body
            });
        });

        // 生成所有成员
        allMembers.forEach((member, index) => {
            if (member.type === 'property') {
                code += `    ${member.name}: ${member.value}`;
            } else {
                const params = member.params.join(', ');
                code += `    ${member.name}: function (${params}) {\n`;

                if (member.body && member.body.trim()) {
                    // 缩进方法体
                    const lines = member.body.split('\n');
                    lines.forEach(line => {
                        if (line.trim()) {
                            code += '        ' + line.trim() + '\n';
                        } else {
                            code += '\n';
                        }
                    });
                } else {
                    code += '        // TODO: 实现方法逻辑\n';
                }

                code += '    }';
            }

            if (index < allMembers.length - 1) {
                code += ',';
            }
            code += '\n';
            if (index < allMembers.length - 1 && member.type === 'method') {
                code += '\n';
            }
        });

        code += '};\n\n';
        code += `module.exports = ${className};\n`;

        return code;
    }

    /**
     * 生成游戏模式数据类代码（专门处理gameMode_Json.js这种格式）
     */
    generateGameModeClass(analysis, fileName) {
        let code = '';

        // 添加文件头注释
        code += `/**\n * ${fileName}\n * 游戏模式数据模块 - 从编译后的JS反编译生成\n */\n\n`;

        // 提取枚举定义
        const enumMatch = analysis.originalContent.match(/\(function \(e\) \{([\s\S]*?)\}\)\(\(n = exports\.GAME_MODE \|\| \(exports\.GAME_MODE = \{\}\)\)\);/);
        if (enumMatch) {
            code += '// 游戏模式枚举\n';
            code += 'const GAME_MODE = {\n';

            // 提取枚举值
            const enumContent = enumMatch[1];
            const enumPattern = /e\[\(e\.(\w+) = (\d+)\)\] = "(\w+)";/g;
            let match;
            const enumEntries = [];
            while ((match = enumPattern.exec(enumContent)) !== null) {
                enumEntries.push({ name: match[1], value: match[2], stringValue: match[3] });
            }

            enumEntries.forEach((entry, index) => {
                code += `    ${entry.name}: ${entry.value}`;
                if (index < enumEntries.length - 1) {
                    code += ',';
                }
                code += '\n';
            });

            code += '};\n\n';
        }

        // 提取数据数组
        const dataMatch = analysis.originalContent.match(/var o = \[([\s\S]*?)\];/);
        if (dataMatch) {
            code += '// 游戏模式配置数据\n';
            code += 'const gameModeData = [\n';

            // 简化数据数组的处理
            const dataContent = dataMatch[1];
            const objectPattern = /\{[^}]*\}/g;
            const objects = dataContent.match(objectPattern) || [];

            objects.forEach((obj, index) => {
                // 替换变量引用
                let cleanObj = obj.replace(/n\.(\w+)/g, 'GAME_MODE.$1');
                code += `    ${cleanObj}`;
                if (index < objects.length - 1) {
                    code += ',';
                }
                code += '\n';
            });

            code += '];\n\n';
        }

        // 生成导出函数
        code += '// 获取游戏模式数据的函数\n';
        code += 'function getGameModeData() {\n';
        code += '    return gameModeData;\n';
        code += '}\n\n';

        // 导出
        code += 'module.exports = {\n';
        code += '    GAME_MODE,\n';
        code += '    gameModeData,\n';
        code += '    default: getGameModeData\n';
        code += '};\n';

        return code;
    }

    /**
     * 生成传统模块类代码
     */
    generateModuleClass(analysis, fileName) {
        let code = '';

        // 添加文件头注释
        code += `/**\n * ${fileName}\n * 传统模块类 - 从编译后的JS反编译生成\n */\n\n`;

        // 添加导入（如果有）
        if (analysis.imports.length > 0) {
            analysis.imports.forEach(imp => {
                code += `const ${imp.variable} = require('${imp.path}');\n`;
            });
            code += '\n';
        }

        // 生成类构造函数
        const className = analysis.className || (fileName.charAt(0).toLowerCase() + fileName.slice(1));

        // 如果有基类，生成继承结构
        if (analysis.baseClass && analysis.baseClass !== 'cc.Component') {
            // 处理基类导入
            if (analysis.baseClass.includes('.')) {
                // 如果基类包含点号（如frameNamespace.ServiceBase），需要特殊处理
                const parts = analysis.baseClass.split('.');
                const baseClassName = parts[parts.length - 1]; // 获取最后一部分作为类名
                const modulePath = parts.join('.'); // 完整路径
                code += `const ${baseClassName} = require('./${modulePath}');

`;
                // 更新analysis.baseClass为简单类名，用于后续的继承代码
                analysis.baseClass = baseClassName;
            } else {
                // 普通基类
                code += `const ${analysis.baseClass} = require('./${analysis.baseClass}');

`;
            }

            // 生成继承构造函数
            code += `function ${className}() {\n`;
            code += `    ${analysis.baseClass}.call(this);\n`;

            // 添加属性初始化
            const instanceProps = analysis.properties.filter(p => !p.isStatic);
            if (instanceProps.length > 0) {
                instanceProps.forEach(prop => {
                    code += `    this.${prop.name} = ${prop.default || 'null'};\n`;
                });
            }

            code += '}\n\n';

            // 设置继承
            code += `${className}.prototype = Object.create(${analysis.baseClass}.prototype);\n`;
            code += `${className}.prototype.constructor = ${className};\n\n`;
        } else {
            // 生成普通构造函数
            code += `function ${className}() {\n`;

            // 添加属性初始化
            const instanceProps = analysis.properties.filter(p => !p.isStatic);
            if (instanceProps.length > 0) {
                instanceProps.forEach(prop => {
                    code += `    this.${prop.name} = ${prop.default || 'null'};\n`;
                });
            }

            code += '}\n\n';
        }

        // 添加原型方法
        analysis.methods.forEach(method => {
            if (!method.isStatic) {
                const params = method.params.join(', ');
                code += `${className}.prototype.${method.name} = function (${params}) {\n`;

                if (method.body && method.body.trim()) {
                    // 缩进方法体
                    const lines = method.body.split('\n');
                    lines.forEach(line => {
                        if (line.trim()) {
                            code += '    ' + line.trim() + '\n';
                        } else {
                            code += '\n';
                        }
                    });
                } else {
                    code += '    // TODO: 实现方法逻辑\n';
                }

                code += '};\n\n';
            }
        });

        // 添加静态方法
        const staticMethods = analysis.methods.filter(m => m.isStatic);
        if (staticMethods.length > 0) {
            staticMethods.forEach(method => {
                const params = method.params.join(', ');
                code += `${className}.${method.name} = function (${params}) {\n`;
                if (method.body && method.body.trim()) {
                    const lines = method.body.split('\n');
                    lines.forEach(line => {
                        if (line.trim()) {
                            code += '    ' + line.trim() + '\n';
                        } else {
                            code += '\n';
                        }
                    });
                } else {
                    code += '    // TODO: 实现静态方法逻辑\n';
                }
                code += '};\n\n';
            });
        }

        // 添加访问器方法（如果需要）
        code += `${className}.sers = function() {\n`;
        code += `    return this._servers;\n`;
        code += '};\n\n';

        code += `${className}.mgrs = function() {\n`;
        code += `    return this._managers;\n`;
        code += '};\n\n';

        // 生成模块导出
        code += `module.exports = ${className};\n`;

        return code;
    }

    /**
     * 提取构造函数内容
     */
    extractConstructorContent(content) {
        const constructorLines = [];

        // 查找cc__extends模式，提取构造函数内容
        const extendsPattern = /cc__extends\s*\(\s*(\w+)\s*,\s*([^)]+)\s*\)/;
        const extendsMatch = content.match(extendsPattern);

        if (extendsMatch) {
            const constructorName = extendsMatch[1]; // 通常是_ctor

            // 查找构造函数定义，匹配function _ctor() { ... }
            const simplePattern = /function\s+_ctor\s*\(\)\s*\{/;
            const simpleMatch = content.match(simplePattern);

            if (simpleMatch) {
                // 找到构造函数开始位置，现在提取整个函数体
                const startIndex = content.indexOf(simpleMatch[0]);
                const bodyStartIndex = startIndex + simpleMatch[0].length;

                // 找到匹配的右大括号
                let braceCount = 1;
                let endIndex = bodyStartIndex;

                while (braceCount > 0 && endIndex < content.length) {
                    if (content[endIndex] === '{') {
                        braceCount++;
                    } else if (content[endIndex] === '}') {
                        braceCount--;
                    }
                    endIndex++;
                }

                if (braceCount === 0) {
                    const ctorBody = content.substring(bodyStartIndex, endIndex - 1);

                    // 分行处理，支持多行对象和数组
                    const lines = ctorBody.split('\n');
                    let i = 0;

                    while (i < lines.length) {
                        const line = lines[i].trim();

                        // 处理复杂的对象赋值，如 o.logomap = ((t = {}).tc = 0, t.en = 1, t.th = 2, t.vn = 3, t.cn = 4, t);
                        const complexAssignMatch = line.match(/([a-z])\.(\w+)\s*=\s*\(\(([a-z])\s*=\s*\{\}\)(.+)\);?$/);
                        if (complexAssignMatch) {
                            const [, objVar, propName, tempVar, assignmentChain] = complexAssignMatch;

                            // 解析赋值链，提取所有键值对
                            const assignments = assignmentChain.split(',');
                            const objPairs = [];

                            // 处理所有赋值，除了最后一个（最后一个是变量本身）
                            for (let j = 0; j < assignments.length - 1; j++) {
                                const assignment = assignments[j].trim();

                                // 处理第一个赋值（可能没有变量前缀）
                                if (j === 0 && assignment.startsWith('.')) {
                                    const firstKeyMatch = assignment.match(/\.(\w+)\s*=\s*(.+)/);
                                    if (firstKeyMatch) {
                                        objPairs.push(`${firstKeyMatch[1]}: ${firstKeyMatch[2].trim()}`);
                                    }
                                } else {
                                    // 处理后续赋值
                                    const keyValueMatch = assignment.match(new RegExp(`${tempVar}\.(\w+)\s*=\s*(.+)`));
                                    if (keyValueMatch) {
                                        objPairs.push(`${keyValueMatch[1]}: ${keyValueMatch[2].trim()}`);
                                    } else {
                                        // 尝试匹配任意变量名的赋值
                                        const anyVarMatch = assignment.match(/([a-z])\.(\w+)\s*=\s*(.+)/);
                                        if (anyVarMatch) {
                                            objPairs.push(`${anyVarMatch[2]}: ${anyVarMatch[3].trim()}`);
                                        }
                                    }
                                }
                            }

                            // 构建对象字面量
                            const objLiteral = `{ ${objPairs.join(', ')} }`;
                            constructorLines.push(`this.${propName} = ${objLiteral}`);
                            i++;
                            continue;
                        }

                        // 处理复杂的IIFE对象赋值，如 o.getRewardEvent = ((t = {})[$2RewardEvent.RewardEvent.Type.SkillBuff] = function () { ... }, t[...] = function() { ... }, t);
                        const iifeObjectMatch = line.match(/([a-z])\.(\w+)\s*=\s*\(\(([a-z])\s*=\s*\{\}\)\[(.+?)\]\s*=\s*function/);
                        if (iifeObjectMatch) {
                            const [, objVar, propName, tempVar] = iifeObjectMatch;

                            // 这是一个复杂的IIFE对象，需要读取多行直到找到完整的对象定义
                            let j = i;
                            let braceCount = 0;
                            let parenCount = 0;
                            let fullObjectDef = '';

                            // 从当前行开始收集完整的对象定义
                            while (j < lines.length) {
                                const currentLine = lines[j].trim();
                                fullObjectDef += (j > i ? '\n            ' : '') + currentLine;

                                // 计算括号数量
                                for (let k = 0; k < currentLine.length; k++) {
                                    if (currentLine[k] === '(') parenCount++;
                                    else if (currentLine[k] === ')') parenCount--;
                                    else if (currentLine[k] === '{') braceCount++;
                                    else if (currentLine[k] === '}') braceCount--;
                                }

                                // 如果括号已经平衡且以分号结束，说明对象定义完成
                                if (parenCount === 0 && braceCount === 0 && currentLine.endsWith(';')) {
                                    break;
                                }
                                j++;
                            }

                            // 尝试解析IIFE对象为普通对象字面量
                            let cleanObjectDef = fullObjectDef;

                            // 移除IIFE包装，提取对象内容
                            const iifePattern = /\(\(([a-z])\s*=\s*\{\}\)(.+),\s*\1\);?$/s;
                            const iifeMatch = cleanObjectDef.match(iifePattern);

                            if (iifeMatch) {
                                const [, tempVarName, objectContent] = iifeMatch;

                                // 解析对象内容，将 t[key] = value 转换为 key: value
                                const keyValuePairs = [];

                                // 手动解析IIFE对象，确保提取所有函数
                                // 分步骤处理：1. 提取第一个函数 2. 提取后续函数

                                // 步骤1：提取第一个函数 ((t = {})[key] = function...)
                                const firstFunctionPattern = new RegExp(`\\(\\(${tempVarName}\\s*=\\s*\\{\\}\\)\\[(.+?)\\]\\s*=\\s*(function[\\s\\S]*?)(?=,\\s*${tempVarName}\\[|,\\s*${tempVarName}\\)|$)`);
                                const firstMatch = firstFunctionPattern.exec(objectContent);

                                if (firstMatch) {
                                    const [, firstKey, firstFunction] = firstMatch;

                                    // 清理第一个函数
                                    let cleanFirstFunction = firstFunction.trim();
                                    cleanFirstFunction = cleanFirstFunction.replace(/,\s*$/, '');
                                    cleanFirstFunction = cleanFirstFunction.replace(/\bo\./g, 'this.');

                                    // 格式化第一个函数
                                    const firstLines = cleanFirstFunction.split('\n');
                                    const formattedFirstLines = firstLines.map((line, index) => {
                                        if (index === 0) return line;
                                        return '                ' + line.trim();
                                    });

                                    keyValuePairs.push(`[${firstKey}]: ${formattedFirstLines.join('\n')}`);
                                }

                                // 步骤2：提取后续的函数 t[key] = function...
                                const subsequentPattern = new RegExp(`${tempVarName}\\[(.+?)\\]\\s*=\\s*(function[\\s\\S]*?)(?=,\\s*${tempVarName}\\[|,\\s*${tempVarName}\\)|$)`, 'g');
                                let subsequentMatch;

                                while ((subsequentMatch = subsequentPattern.exec(objectContent)) !== null) {
                                    const [, key, functionDef] = subsequentMatch;

                                    // 清理函数定义
                                    let cleanFunction = functionDef.trim();
                                    cleanFunction = cleanFunction.replace(/,\s*$/, '');
                                    cleanFunction = cleanFunction.replace(/\bo\./g, 'this.');

                                    // 格式化函数定义
                                    const lines = cleanFunction.split('\n');
                                    const formattedLines = lines.map((line, index) => {
                                        if (index === 0) return line;
                                        return '                ' + line.trim();
                                    });

                                    keyValuePairs.push(`[${key}]: ${formattedLines.join('\n')}`);
                                }

                                // 构建对象字面量
                                const objLiteral = `{\n            ${keyValuePairs.join(',\n            ')}\n        }`;
                                constructorLines.push(`this.${propName} = ${objLiteral}`);
                            } else {
                                // 如果无法解析，保持原始格式
                                constructorLines.push(`this.${propName} = ${cleanObjectDef}`);
                            }

                            i = j; // 跳过已处理的行
                            continue;
                        }

                        const match = line.match(/[ot]\.(\w+)\s*=\s*(.*)/);

                        if (match) {
                            const [, propName, valueStart] = match;
                            let value = valueStart;

                            // 如果值以 { 开始但不以 }; 结束，说明是多行对象
                            if (value.trim().startsWith('{') && !value.trim().endsWith('};') && !value.trim().endsWith('}')) {
                                // 继续读取直到找到结束的 }
                                let j = i + 1;
                                let braceCount = 0;

                                // 计算第一行中的括号数量
                                for (let k = 0; k < value.length; k++) {
                                    if (value[k] === '{') braceCount++;
                                    else if (value[k] === '}') braceCount--;
                                }

                                while (j < lines.length && braceCount > 0) {
                                    const nextLine = lines[j].trim();

                                    // 计算当前行的括号数量
                                    for (let k = 0; k < nextLine.length; k++) {
                                        if (nextLine[k] === '{') braceCount++;
                                        else if (nextLine[k] === '}') braceCount--;
                                    }

                                    // 添加当前行
                                    if (nextLine === '};' || nextLine === '}') {
                                        value += '\n        ' + nextLine;
                                    } else {
                                        value += '\n            ' + nextLine;
                                    }

                                    // 如果括号已经平衡，结束循环
                                    if (braceCount === 0) {
                                        i = j; // 更新外层循环的索引
                                        break;
                                    }
                                    j++;
                                }
                            }
                            // 如果值以 [ 开始但不以 ]; 结束，说明是多行数组
                            else if (value.trim().startsWith('[') && !value.trim().endsWith('];') && !value.trim().endsWith(']')) {
                                // 继续读取直到找到结束的 ]
                                let j = i + 1;
                                let bracketCount = 0;

                                // 计算第一行中的括号数量
                                for (let k = 0; k < value.length; k++) {
                                    if (value[k] === '[') bracketCount++;
                                    else if (value[k] === ']') bracketCount--;
                                }

                                while (j < lines.length && bracketCount > 0) {
                                    const nextLine = lines[j].trim();

                                    // 计算当前行的括号数量
                                    for (let k = 0; k < nextLine.length; k++) {
                                        if (nextLine[k] === '[') bracketCount++;
                                        else if (nextLine[k] === ']') bracketCount--;
                                    }

                                    // 添加当前行
                                    if (nextLine === '];' || nextLine === ']') {
                                        value += '\n        ' + nextLine;
                                    } else {
                                        value += '\n            ' + nextLine;
                                    }

                                    // 如果括号已经平衡，结束循环
                                    if (braceCount === 0) {
                                        i = j; // 更新外层循环的索引
                                        break;
                                    }
                                    j++;
                                }
                            }
                            // 如果值以 function 开始但不以 }; 结束，说明是多行函数
                            else if (value.trim().startsWith('function') && !value.trim().endsWith('};') && !value.trim().endsWith('}')) {
                                // 继续读取直到找到结束的 }
                                let j = i + 1;
                                let braceCount = 0;

                                // 计算第一行中的括号数量
                                for (let k = 0; k < value.length; k++) {
                                    if (value[k] === '{') braceCount++;
                                    else if (value[k] === '}') braceCount--;
                                }

                                while (j < lines.length && braceCount > 0) {
                                    const nextLine = lines[j].trim();

                                    // 计算当前行的括号数量
                                    for (let k = 0; k < nextLine.length; k++) {
                                        if (nextLine[k] === '{') braceCount++;
                                        else if (nextLine[k] === '}') braceCount--;
                                    }

                                    // 添加当前行
                                    if (nextLine === '};' || nextLine === '}') {
                                        value += '\n        ' + nextLine;
                                    } else {
                                        value += '\n            ' + nextLine;
                                    }

                                    // 如果括号已经平衡，结束循环
                                    if (braceCount === 0) {
                                        i = j; // 更新外层循环的索引
                                        break;
                                    }
                                    j++;
                                }
                            }

                            // 清理值
                            let cleanValue = value.trim();
                            // 移除末尾的分号
                            if (cleanValue.endsWith(';')) {
                                cleanValue = cleanValue.slice(0, -1);
                            }

                            // 处理cc.v2()调用
                            if (cleanValue === 'cc.v2()') {
                                cleanValue = 'cc.v2(0, 0)';
                            }

                            constructorLines.push(`this.${propName} = ${cleanValue}`);
                        }
                        i++;
                    }
                }
            }

            const ctorMatch = null; // 不再使用原来的匹配方式

            if (ctorMatch) {
                const ctorBody = ctorMatch[1];
                // console.log('Constructor body:', ctorBody);

                // 查找t.property = value的模式
                const propertyPattern = /t\.(\w+)\s*=\s*([^;\n]+);?/g;
                let match;

                while ((match = propertyPattern.exec(ctorBody)) !== null) {
                    const [, propName, propValue] = match;
                    // console.log('Found property:', propName, '=', propValue);

                    // 转换为this.property = value的格式
                    let cleanValue = propValue.trim();

                    // 处理cc.v2()调用
                    if (cleanValue === 'cc.v2()') {
                        cleanValue = 'cc.v2(0, 0)';
                    }

                    constructorLines.push(`this.${propName} = ${cleanValue}`);
                }
            }
        }

        return constructorLines;
    }

    /**
     * 生成组件类代码
     */
    generateComponentClass(analysis, fileName) {
        let code = '';
        // 使用analysis.processedExports记录已处理的导出，避免重复

        // 添加文件头注释
        code += `/**\n * ${fileName}\n * 组件类 - 从编译后的JS反编译生成\n */\n\n`;

        // 添加导入（如果有）
        if (analysis.imports.length > 0) {
            analysis.imports.forEach(imp => {
                code += `const ${imp.variable} = require('${imp.path}');
`;
            });
            code += '\n';
        }

        // 添加全局变量声明和枚举定义
        const globalDeclarations = this.extractGlobalDeclarations(analysis.originalContent || '');
        if (globalDeclarations.length > 0) {
            const declaredVars = new Set();
            globalDeclarations.forEach(decl => {
                if (decl.type === 'variable' && !declaredVars.has(decl.name)) {
                    code += `${decl.declaration}\n`;
                    declaredVars.add(decl.name);
                } else if (decl.type === 'enum') {
                    // 生成枚举定义（枚举定义中已经包含了变量声明）
                    code += `var ${decl.varName};\n`;
                    code += `(function (e) {\n`;
                    decl.entries.forEach(entry => {
                        code += `    e[e.${entry.key} = ${entry.value}] = "${entry.stringValue}";\n`;
                    });
                    code += `})(${decl.varName} = exports.${decl.name} || (exports.${decl.name} = {}));\n`;
                    declaredVars.add(decl.varName);
                    // 记录已处理的导出
                    analysis.processedExports.add(decl.name);
                } else if (decl.type === 'directEnum') {
                    // 生成直接导出的枚举定义（不需要变量声明）
                    code += `(function (e) {\n`;
                    decl.entries.forEach(entry => {
                        code += `    e[e.${entry.key} = ${entry.value}] = "${entry.stringValue}";\n`;
                    });
                    code += `})(exports.${decl.name} || (exports.${decl.name} = {}));\n`;
                    // 记录已处理的导出
                    analysis.processedExports.add(decl.name);
                }
            });
            code += '\n';
        }

        // 处理exports导出 - 在cc.Class定义前添加对应的exports语句
        const allExports = this.extractAllExports(analysis.originalContent || '');
        const defaultExport = allExports.find(e => e.isDefault);
        
        // 查找与当前class匹配的非默认导出
        const matchingExport = allExports.find(e => 
            !e.isDefault && 
            !e.isEnum && 
            (e.name === analysis.className || e.value.includes(analysis.className))
        );

        // 如果有匹配的非默认导出，使用它；否则检查默认导出
        if (matchingExport) {
            code += `exports.${matchingExport.name} = `;
            // 记录已处理的导出
            analysis.processedExports.add(matchingExport.name);
        } else if (defaultExport) {
            code += 'exports.default = ';
            // 记录已处理的导出
            analysis.processedExports.add('default');
        }

        // 生成cc.Class结构
        code += 'cc.Class({\n';

        // extends
        if (analysis.baseClass) {
            code += `    extends: ${analysis.baseClass},\n\n`;
        } else {
            code += '\n';
        }

        // properties (包含普通属性和getter/setter属性)
        const instanceProps = analysis.properties.filter(p => !p.isStatic && !p.isGetter && !p.isSetter);
        const getterSetterProps = analysis.properties.filter(p => p.isGetter || p.isSetter);

        code += '    properties: {\n';

        // 添加普通属性
        instanceProps.forEach((prop, index) => {
            code += `        ${prop.name}: {\n`;
            if (prop.type) {
                // 检查是否为数组类型
                const isArrayType = prop.type.startsWith('[') && prop.type.endsWith(']');

                if (isArrayType) {
                    // 数组类型直接使用type: [ElementType]
                    code += `            type: ${prop.type},\n`;
                } else {
                    // 非数组类型使用原有格式
                    code += `            type: ${prop.type},\n`;
                }
            }

            // 添加displayName（如果有）
            if (prop.displayName) {
                code += `            displayName: "${prop.displayName}",\n`;
            }
            code += `            default: ${prop.default}\n`;
            code += '        }';
            if (index < instanceProps.length - 1 || getterSetterProps.length > 0) {
                code += ',';
            }
            code += '\n';
        });

        // 添加getter/setter属性到properties中
        getterSetterProps.forEach((prop, index) => {
            code += `        ${prop.name}: {\n`;

            // 添加getter
            if (prop.isGetter && prop.getterBody) {
                code += '            get() {\n';
                // 缩进getter函数体
                const getterLines = prop.getterBody.split('\n');
                getterLines.forEach(line => {
                    if (line.trim()) {
                        code += '                ' + line.trim() + '\n';
                    }
                });
                code += '            }';
                if (prop.isSetter && prop.setterBody) {
                    code += ',\n';
                } else {
                    code += ',\n';
                }
            }

            // 添加setter
            if (prop.isSetter && prop.setterBody) {
                code += '            set(value) {\n';
                // 缩进setter函数体，参数替换已经在 replaceSetterParameter 中处理了
                const setterLines = prop.setterBody.split('\n');
                setterLines.forEach(line => {
                    if (line.trim()) {
                        // 不需要额外的参数替换
                        let processedLine = line.trim();
                        code += '                ' + processedLine + '\n';
                    }
                });
                code += '            },\n';
            }

            // 添加visible: false标记
            code += '            visible: false\n';
            code += '        }';
            if (index < getterSetterProps.length - 1) {
                code += ',';
            }
            code += '\n';
        });

        code += '    },\n\n';

        // 添加构造函数（ctor）
        const constructorContent = this.extractConstructorContent(analysis.originalContent || '');
        if (constructorContent.length > 0) {
            code += '    ctor: function () {\n';
            constructorContent.forEach(line => {
                code += '        ' + line + '\n';
            });
            code += '    },\n\n';
        }

        // 生成方法
        const commonMethods = ['onLoad', 'start', 'update', 'onDestroy'];

        // 添加常见生命周期方法（如果不存在）
        if (!analysis.methods.find(m => m.name === 'onLoad')) {
            code += '    // use this for initialization\n';
            code += '    onLoad: function () {\n';
            code += '    },\n\n';
        }

        // getter/setter属性已经在properties中处理，这里不再重复添加

        // 添加现有方法
        analysis.methods.forEach((method, index) => {
            const params = method.params.join(', ');

            // ctor 方法的特殊处理
            if (method.name === 'ctor') {
                code += `    ctor() {\n`;
            } else {
                code += `    ${method.name}: function (${params}) {\n`;
            }

            if (method.body && method.body.trim()) {
                // 缩进方法体
                const lines = method.body.split('\n');
                lines.forEach(line => {
                    if (line.trim()) {
                        code += '        ' + line.trim() + '\n';
                    } else {
                        code += '\n';
                    }
                });
            } else {
                code += '        // TODO: 实现方法逻辑\n';
            }

            code += '    }';

            // 检查是否需要添加逗号：如果不是最后一个方法，或者后面还有update注释
            const hasUpdateComment = !analysis.methods.find(m => m.name === 'update');
            if (index < analysis.methods.length - 1 || hasUpdateComment) {
                code += ',';
            }

            code += '\n';
            if (index < analysis.methods.length - 1) {
                code += '\n';
            }
        });

        // 如果没有update方法，添加注释版本
        if (!analysis.methods.find(m => m.name === 'update')) {
            if (analysis.methods.length > 0) {
                code += '\n';
            }
            code += '    // called every frame, uncomment this function to activate update callback\n';
            code += '    // update: function (dt) {\n';
            code += '    // },\n';
        }

        code += '});\n';

        // 处理其他非默认导出（默认导出已经在cc.Class前处理了）
        // 注意：我们只处理那些在cc.Class前没有处理过的导出
        if (allExports.length > 0) {
            // 过滤掉已处理的导出和默认导出
            const otherExports = allExports.filter(e => 
                !e.isDefault && 
                !e.isEnum && 
                !(analysis.processedExports && analysis.processedExports.has(e.name))
            );

            // 处理非枚举的其他导出
            if (otherExports.length > 0) {
                code += '\n';
                otherExports.forEach(exp => {
                    code += `exports.${exp.name} = ${exp.value};\n`;
                });
            }
        }

        return code;
    }
}

// 命令行使用
if (require.main === module) {
    console.log('Starting JS decompiler...');
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('用法:');
        console.log('  node js_decompiler_to_ccclass.js <文件路径>     # 转换单个文件');
        console.log('  node js_decompiler_to_ccclass.js <目录路径>     # 转换目录下所有JS文件');
        console.log('');
        console.log('示例:');
        console.log('  node js_decompiler_to_ccclass.js scripts/pageBase.js');
        console.log('  node js_decompiler_to_ccclass.js scripts/');
        process.exit(1);
    }

    const inputPath = args[0];
    console.log(`Processing: ${inputPath}`);
    const converter = new JSDecompilerToCCClass();

    if (fs.statSync(inputPath).isDirectory()) {
        converter.convertDirectory(inputPath);
    } else {
        converter.convertFile(inputPath);
    }
    console.log('Conversion completed.');
}

module.exports = JSDecompilerToCCClass;