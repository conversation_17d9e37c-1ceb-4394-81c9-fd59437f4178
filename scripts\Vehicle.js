var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2OrganismBase = require("OrganismBase");
var $2SteeringBehaviors = require("SteeringBehaviors");
cc.v2();
cc.v2();
var c = cc._decorator.ccclass;
var def_Vehicle = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._steering = null;
    t.isSmoother = true;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "steering", {
    get: function () {
      return this._steering;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this._steering || (this._steering = new $2SteeringBehaviors.default(this));
  };
  _ctor.Vec2Truncate = function (e, t) {
    if (t <= 0) {
      return cc.Vec2.ZERO;
    } else {
      return e.magSqr() > t * t && (cc.Vec2.normalize(e, e), cc.Vec2.multiplyScalar(e, e, t)), e;
    }
  };
  _ctor.prototype.isInAttackRange = function () {
    if (!this.steering.targetAgent1) {
      return -1;
    }
    var e = cc.Vec2.squaredDistance(this.position, this.steering.targetAgent1.position);
    var t = this.property.cut.atkArea + this.radius + this.steering.targetAgent1.radius;
    var o = Math.pow(t, 2);
    var i = Math.pow(.4 * t, 2);
    if (e > o) {
      return -1;
    } else {
      if (e < i) {
        return 1;
      } else {
        return 0;
      }
    }
  };
  _ctor.prototype.enforceNonPeretration = function () {};
  return cc__decorate([c], _ctor);
}($2OrganismBase.default);
exports.default = def_Vehicle;