var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_GridViewCell = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onInit = function () {};
  _ctor.prototype.onRefresh = function () {};
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
  };
  _ctor.prototype.changeListener = function (e) {
    this.onRefreshState && $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.onRefreshState, this);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_GridViewCell;