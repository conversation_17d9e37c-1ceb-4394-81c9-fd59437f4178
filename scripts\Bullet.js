var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var u = cc.v2();
var def_Bullet = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (!(this._vo.lifeTime < 0 || this.isDead)) {
      cc.Vec2.multiplyScalar(u, this._vo.shootDir, this.maxSpeed * t);
      cc.Vec2.add(u, this.position, u);
      this.setPosition(u);
      this.isBanRotate || 0 != this.isRotate || this.updateDir(t);
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet;