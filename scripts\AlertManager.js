var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AlertManager = exports.ItemAlertType = exports.AlertType = undefined;
var n;
var r;
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2DialogBox = require("DialogBox");
var $2Game = require("Game");
var $2NodePool = require("NodePool");
var $2SettingModel = require("SettingModel");
var $2NormalTips = require("NormalTips");
(function (e) {
  e.COMMON = "0";
  e.SELECT = "1";
})(n = exports.AlertType || (exports.AlertType = {}));
(function (e) {
  e[e.Tips = 0] = "Tips";
  e[e.NotEnough = 1] = "NotEnough";
})(r = exports.ItemAlertType || (exports.ItemAlertType = {}));
var exp_AlertManager = function () {
  function _ctor() {}
  _ctor.showAlert = function (e, t) {
    var o = new $2MVC.MVC.OpenArgs();
    o.setParam(t);
    if (e == n.COMMON) {
      $2UIManager.UIManager.Open("ui/common/alert/CommonAlert", o);
    } else {
      e == n.SELECT && $2UIManager.UIManager.Open("ui/common/alert/SelectAlert", o);
    }
  };
  _ctor.showPropertyChange = function (e, t, o) {
    undefined === o && (o = cc.Vec2.ZERO);
    var i = 0;
    for (var n in t) {
      var r = e[i];
      r || (r = e[0]);
      t[n];
      var a = .15 * i++;
      cc.tween(e[0]).delay(a).call(function () {}).start();
    }
  };
  _ctor.showCommonTips = function (e, t, o, i) {
    undefined === o && (o = 250);
    undefined === i && (i = 16);
    $2UIManager.UIManager.Open("ui/common/CommonTipsView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setParam({
      str: e,
      worldPos: t,
      maxWidth: o,
      fontSize: i
    }));
  };
  _ctor.showCommonAlert = function (t) {
    _ctor.showAlert(n.COMMON, t);
  };
  _ctor.showSelectAlert = function (t) {
    _ctor.showAlert(n.SELECT, t);
  };
  _ctor.showItemAlert = function (e) {
    var t = $2GameUtil.GameUtil.dateFormat(Date.now());
    r.NotEnough == e.type && (e.confirm = function () {});
    if (t == $2Manager.Manager.storage.getString("ItemAlert_" + e.type + "_" + e.id)) {
      return e.confirm();
    }
    $2UIManager.UIManager.Open("ui/common/alert/ItemAlert", $2MVC.MVC.openArgs().setParam(e));
  };
  _ctor.showCommonGetResourceConfirmView = function (e) {
    $2UIManager.UIManager.Open("ui/common/CommonGetResourceConfirmView", $2MVC.MVC.openArgs().setParam(e));
  };
  _ctor.showNormalTipsOnce = function (t, o, i, n, r) {
    var a = this;
    undefined === o && (o = null);
    undefined === i && (i = .7);
    undefined === n && (n = 50);
    undefined === r && (r = cc.Vec2.ZERO);
    if (_ctor.canShow) {
      _ctor.canShow = false;
      var u = this.tipPool.get();
      var p = function (c) {
        var u;
        var p;
        c.getComponent($2NormalTips.default).setText(t);
        null === (p = null === (u = c.children[0]) || undefined === u ? undefined : u.children[0]) || undefined === p || p.setActive(false);
        c.position = r;
        c.opacity = 20;
        if (o && cc.isValid(o)) {
          c.parent = o;
        } else {
          c.setParent($2UIManager.UIManager.layerRoots($2MVC.MVC.eUILayer.Tips));
        }
        var f = cc.tween().by(i, {
          position: cc.v3(0, n)
        });
        cc.tween(c).parallel(f, cc.tween().to(.4, {
          opacity: 255
        })).delay(.5).by(i, {
          position: cc.v2(0, n),
          opacity: -254
        }).call(function () {
          a.tipPool.put(c);
          _ctor.canShow = true;
        }).start();
      };
      if (u) {
        p(u);
      } else {
        $2Manager.Manager.loader.loadPrefab("ui/common/alert/tip").then(function (e) {
          p(e);
        });
      }
    }
  };
  _ctor.showNormalTips = function (t, o) {
    var i = this;
    undefined === o && (o = {});
    var n = o.currencyID && $2Cfg.Cfg.CurrencyConfig.get(o.currencyID);
    o.time || (o.time = .3);
    o.ydis || (o.ydis = 50);
    o.pos || (o.pos = cc.Vec2.ZERO);
    var r = o.isgameUI ? "ui/common/alert/gametip" : "ui/common/alert/tip";
    $2Manager.Manager.loader.loadPrefab(r).then(function (r) {
      var a;
      var u;
      null == (u = (a = r).getComByPath(cc.Sprite, "bg/icon")) || u.node.setActive(!!n);
      n && $2Manager.Manager.loader.loadSpriteToSprit(n.icon, u);
      a.getComponent($2NormalTips.default).setText(t);
      a.opacity = 255;
      if (cc.isValid(o.parent)) {
        a.setParent(o.parent);
        a.setPosition(0, 0);
      } else {
        a.setParent($2UIManager.UIManager.layerRoots($2MVC.MVC.eUILayer.Guide));
      }
      a.position = o.pos;
      cc.tween(a).parallel(cc.tween().by(o.time, {
        position: cc.v2(0, o.ydis)
      }), cc.tween().to(.3, {
        opacity: 255
      })).delay(o.delayTime || 1).by(o.time, {
        position: cc.v2(0, 2 * o.ydis),
        opacity: -254
      }).call(function () {
        i.tipPool.put(a);
        _ctor.canShow = true;
      }).start();
    });
  };
  _ctor.showAlertDesc = function (t, o) {
    var i = this;
    undefined === o && (o = cc.Vec2.ZERO);
    var n = function (n) {
      var r = n.getComponent($2NormalTips.default).setText(t);
      o.x *= .8;
      n.position = o;
      n.opacity = 20;
      n.group = "UI";
      var a = r.label;
      a.string = t;
      n.setParent($2UIManager.UIManager.layerRoots($2MVC.MVC.eUILayer.Guide));
      if (t.length > 6) {
        a.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
      } else {
        a.overflow = cc.Label.Overflow.NONE;
      }
      a.scheduleOnce(function () {
        n.children[0].height = a.node.height;
      });
      var c = cc.tween().by(.3, {
        position: cc.v3(0, 30)
      });
      cc.tween(n).parallel(c, cc.tween().to(.4, {
        opacity: 255
      })).delay(.6).by(.3, {
        position: cc.v2(0, 50),
        opacity: -254
      }).call(function () {
        i.tipPool.put(n);
        _ctor.canShow = true;
      }).start();
    };
    $2Manager.Manager.loader.loadPrefab("ui/common/alert/AlertDesc").then(function (e) {
      n(e);
    });
  };
  _ctor.showHurtTips = function (e, t, o) {
    undefined === o && (o = false);
    if ($2SettingModel.default.instance.toggle.hurtTips) {
      var n = $2NodePool.NodePool.spawn("ui/common/" + (o ? "CritTips" : "NumTips"));
      n.setNodeAssetFinishCall(function (r) {
        t.parent || (t.parent = $2Game.Game.mgr.topUINode);
        t.color || (t.color = cc.Color.WHITE);
        if (cc.isValid(t.parent)) {
          var a = r.children[0];
          a.color = t.color;
          o && (e = "p" + e);
          a.getComponent(cc.Label).string = "" + e;
          r.setAttribute(cc__assign({
            active: false,
            opacity: 255,
            angle: 0,
            group: "Game"
          }, t));
          if (n.spawner.runNum > 50) {
            $2Game.Game.tween(r).set({
              active: true,
              scale: 5,
              opacity: 255
            }).parallel(cc.tween().to(.1, {
              scale: o ? 4 : 3
            }).to(.2, {
              scale: 1,
              opacity: 50
            }), cc.tween().by(.1, {
              x: $2Game.Game.random(-60, 60),
              y: $2Game.Game.random(30, 100)
            }).by(.1, {
              y: -10
            })).call(function () {
              $2NodePool.NodePool.despawn(r.nodeItem);
            }).start();
          } else {
            $2Game.Game.tween(r).set({
              active: true,
              scale: 0,
              opacity: 255
            }).to(.05, {
              scale: o ? 4 : 3
            }).parallel(cc.tween().to(.3, {
              scale: 1
            }), cc.tween().by(.6, {
              x: $2Manager.Manager.random.randomInt(-100, 100)
            }, {
              easing: cc.easing.sineOut
            }), cc.tween().by(.2, {
              y: 80
            }, {
              easing: cc.easing.sineOut
            }).by(.4, {
              opacity: -255,
              scale: -1,
              y: -80
            })).call(function () {
              $2NodePool.NodePool.despawn(r.nodeItem);
            }).start();
          }
        } else {
          $2NodePool.NodePool.despawn(r.nodeItem);
        }
      });
    }
  };
  _ctor.showDialogBox = function (e, t) {
    $2SettingModel.default.instance.toggle.hurtTips && $2NodePool.NodePool.spawn("ui/common/DialogBox").setNodeAssetFinishCall(function (o) {
      t.parent || (t.parent = $2Game.Game.mgr.topUINode);
      t.position || (t.position = cc.v2(0, 100));
      if (cc.isValid(t.parent)) {
        o.getComponent($2DialogBox.default).string = e.toString();
        o.setAttribute(cc__assign({
          opacity: 255,
          angle: 0,
          scale: 0,
          group: t.parent.group
        }, t));
      } else {
        $2NodePool.NodePool.despawn(o.nodeItem);
      }
    });
  };
  _ctor.showTipsText = function (e, t) {
    var o = this;
    var n = this.TipsTextPool.get();
    var r = function (n) {
      if (cc.isValid(parent)) {
        n.setAttribute(cc__assign({
          active: false,
          opacity: 255,
          angle: 0
        }, t));
        n.getComponent(cc.Label).string = e;
        $2Game.Game.tween(n).set({
          active: true,
          scale: 0,
          opacity: 255
        }).to(.2, {
          scale: 1
        }).delay(.2).by(.3, {
          opacity: -255,
          scale: -2,
          y: 80
        }).call(function () {
          o.TipsTextPool.put(n);
        }).start();
      }
    };
    if (n) {
      r(n);
    } else {
      $2Manager.Manager.loader.loadPrefab("ui/common/alert/TipsText").then(function (e) {
        r(e);
      });
    }
  };
  _ctor.canShow = true;
  _ctor.tipPool = new cc.NodePool("NormalTips");
  _ctor.TipsTextPool = new cc.NodePool();
  return _ctor;
}();
exports.AlertManager = exp_AlertManager;