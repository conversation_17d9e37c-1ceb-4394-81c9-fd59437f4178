var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2StorageID = require("StorageID");
var $2GameUtil = require("GameUtil");
var def_SettingModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.toggle = {
      music: true,
      audio: true,
      shake: true,
      friend: true,
      power: false,
      dub: true,
      hurtTips: true,
      musicValue: 1,
      audioValue: 1
    };
    o.designSize = null;
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  _ctor.prototype.initSetting = function () {
    var e = this;
    var o = $2Manager.Manager.storage.getObject($2StorageID.StorageID.Setting_Data, {});
    Object.getOwnPropertyNames(this.toggle).forEach(function (t) {
      o.hasOwnProperty(t) && (e.toggle[t] = o[t]);
    });
    if (!this.toggle.audio) {
      _ctor.instance.toggle.audioValue = 0;
      $2Manager.Manager.audio.setEnableAudio(true);
    }
    if (this.toggle.music) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Setting_ValueMusic, _ctor.instance.toggle.musicValue);
    } else {
      _ctor.instance.toggle.musicValue = 0;
      $2Manager.Manager.audio.setMusicEnable(true, 0);
      $2Manager.Manager.audio.setMusicVolume(0);
    }
  };
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getRealDesignSize = function () {
    this.designSize || (this.designSize = $2GameUtil.GameUtil.getRealDesignSize());
    return this.designSize;
  };
  _ctor.prototype.save = function () {
    $2Manager.Manager.storage.setObject($2StorageID.StorageID.Setting_Data, this.toggle);
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_SettingModel;