Object.defineProperty(exports, "__esModule", {
  value: true
});
var i;
var n = 1.2;
var r = 2;
var a = 80;
var s = 20;
(function (e) {
  e[e.weighted_average = 0] = "weighted_average";
  e[e.prioritized = 1] = "prioritized";
  e[e.dithered = 2] = "dithered";
})(i || (i = {}));
var c;
var l;
var u = 0;
(function (e) {
  e[e.none = 1 << u++] = "none";
  e[e.seek = 1 << u++] = "seek";
  e[e.flee = 1 << u++] = "flee";
  e[e.arrive = 1 << u++] = "arrive";
  e[e.wander = 1 << u++] = "wander";
  e[e.cohesion = 1 << u++] = "cohesion";
  e[e.separation = 1 << u++] = "separation";
  e[e.allignment = 1 << u++] = "allignment";
  e[e.obstacleAvoidance = 1 << u++] = "obstacleAvoidance";
  e[e.wallAvoidance = 1 << u++] = "wallAvoidance";
  e[e.followPath = 1 << u++] = "followPath";
  e[e.pursuit = 1 << u++] = "pursuit";
  e[e.evade = 1 << u++] = "evade";
  e[e.interpose = 1 << u++] = "interpose";
  e[e.hide = 1 << u++] = "hide";
  e[e.flock = 1 << u++] = "flock";
  e[e.offsetPursuit = 1 << u++] = "offsetPursuit";
  e[e.flow = 1 << u++] = "flow";
})(c || (c = {}));
(function (e) {
  e[e.SLOW = 3] = "SLOW";
  e[e.NORMAL = 2] = "NORMAL";
  e[e.FAST = 1] = "FAST";
})(l || (l = {}));
var p = cc.v2();
var f = cc.v2();
var h = cc.v2();
cc.v2();
var d = cc.v2();
var g = cc.v2();
var y = cc.mat4();
var def_SteeringBehaviors = function () {
  function _ctor(t) {
    this._vehicle = null;
    this._steeringForce = cc.Vec2.ZERO;
    this._targetAgent1 = null;
    this._targetAgent2 = null;
    this._weightSeparation = 4 * _ctor._steeringForceTweaker;
    this._weightCohesion = .5 * _ctor._steeringForceTweaker;
    this._weightAlignment = 1 * _ctor._steeringForceTweaker;
    this._weightObstacleAvoidance = 10 * _ctor._steeringForceTweaker;
    this._weightWallAvoidance = 10 * _ctor._steeringForceTweaker;
    this._weightSeek = 1 * _ctor._steeringForceTweaker;
    this._weightFlee = 1 * _ctor._steeringForceTweaker;
    this._weightArrive = 1 * _ctor._steeringForceTweaker;
    this._weightPursuit = 1 * _ctor._steeringForceTweaker;
    this._weightOffsetPursuit = 1 * _ctor._steeringForceTweaker;
    this._weightInterpose = 1 * _ctor._steeringForceTweaker;
    this._weightHide = 1 * _ctor._steeringForceTweaker;
    this._weightFollowPath = .05 * _ctor._steeringForceTweaker;
    this._weightEvade = .01 * _ctor._steeringForceTweaker;
    this._weightWander = 1 * _ctor._steeringForceTweaker;
    this._weightFlow = 2 * _ctor._steeringForceTweaker;
    this._curTarget = cc.Vec2.ZERO;
    this._boxLength = 40;
    this._feelers = [];
    this.debugPen = null;
    this._wallDetectionFeelerLength = 40;
    this._wanderTarget = cc.Vec2.ZERO;
    this._wanderJitter = a;
    this._wanderRadius = n;
    this._wanderDistance = r;
    this._viewDistance = 50;
    this._wayPointSeekDistSq = s * s;
    this._offset = cc.Vec2.ZERO;
    this._flags = 0;
    this._deceleration = l.FAST;
    this._cellSpaceOn = false;
    this._summingMethod = i.prioritized;
    var o = 2 * Math.random() * Math.PI;
    cc.Vec2.set(this._wanderTarget, this._wanderRadius * Math.cos(o), this.wanderRadius * Math.sin(o));
    this._vehicle = t;
  }
  Object.defineProperty(_ctor.prototype, "curTarget", {
    get: function () {
      return this._curTarget;
    },
    set: function (e) {
      this._curTarget = e.clone();
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "feelers", {
    get: function () {
      return this._feelers;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "wanderTarget", {
    get: function () {
      return this._wanderTarget;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.on = function (e) {
    return (this._flags & e) == e;
  };
  _ctor.prototype.accumulateForce = function (e) {
    var t = this._steeringForce.mag();
    var o = this._vehicle.maxForce - t;
    return !(o <= 0 || (e.mag() < o ? this._steeringForce.addSelf(e) : this._steeringForce.addSelf(e.normalizeSelf().mulSelf(o)), 0));
  };
  _ctor.prototype.seek = function (e) {
    cc.Vec2.subtract(p, e, this._vehicle.position);
    cc.Vec2.normalize(p, p);
    cc.Vec2.multiplyScalar(p, p, this._vehicle.maxSpeed);
    cc.Vec2.subtract(p, p, this._vehicle.velocity);
    return p;
  };
  _ctor.prototype.flee = function (e) {
    cc.Vec2.subtract(p, this._vehicle.position, e);
    cc.Vec2.normalize(p, p);
    cc.Vec2.multiplyScalar(p, p, this._vehicle.maxSpeed);
    cc.Vec2.subtract(p, p, this._vehicle.velocity);
    return p;
  };
  _ctor.prototype.arrive = function (e, t) {
    cc.Vec2.subtract(p, e, this._vehicle.position);
    var o = p.mag();
    if (o > .01) {
      var i = o / (.3 * t);
      i = Math.min(i, this._vehicle.maxSpeed);
      cc.Vec2.multiplyScalar(p, p, i);
      p.divSelf(o);
      cc.Vec2.subtract(p, p, this._vehicle.velocity);
      return p;
    }
    cc.Vec2.set(p, 0, 0);
    return p;
  };
  _ctor.prototype.pursuit = function (e) {
    if (!e) {
      p.set(cc.Vec2.ZERO);
      return p;
    }
    cc.Vec2.subtract(p, e.position, this._vehicle.position);
    var t = this._vehicle.heading.dot(e.heading);
    if (p.dot(this._vehicle.heading) > 0 && t < -.95) {
      return this.seek(e.position);
    }
    var o = p.mag() / (this._vehicle.maxSpeed + e.velocity.mag());
    cc.Vec2.multiplyScalar(f, e.velocity, o);
    cc.Vec2.add(f, f, e.position);
    return this.seek(f);
  };
  _ctor.prototype.offsetPursuit = function (e, t) {
    if (!e) {
      p.set(cc.Vec2.ZERO);
      return p;
    }
    cc.Vec2.set(p, e.side.x * t.x + e.side.y * t.y, e.heading.x * t.x + e.heading.y * t.y);
    cc.Vec2.add(p, e.position, p);
    var o = cc.Vec2.distance(p, this._vehicle.position) / (this._vehicle.maxSpeed + 0);
    cc.Vec2.multiplyScalar(f, e.velocity, o);
    cc.Vec2.add(f, p, f);
    return this.arrive(f, this._deceleration);
  };
  _ctor.prototype.evade = function (e) {
    cc.Vec2.subtract(p, e.position, this._vehicle.position);
    if (p.magSqr() > 4e4) {
      cc.Vec2.set(p, 0, 0);
      return p;
    }
    var t = p.mag() / (this._vehicle.maxSpeed + e.velocity.mag());
    cc.Vec2.multiplyScalar(p, e.velocity, t);
    cc.Vec2.add(f, p, e.position);
    return this.flee(f);
  };
  _ctor.prototype.randomClamped = function () {
    return Math.random() - Math.random();
  };
  _ctor.prototype.wander = function () {
    var e = this._wanderJitter * this._vehicle.timeElapse();
    cc.Vec2.set(f, this.randomClamped() * e, this.randomClamped() * e);
    cc.Vec2.add(this._wanderTarget, this._wanderTarget, f);
    this._wanderTarget.normalizeSelf();
    cc.Vec2.multiplyScalar(this._wanderTarget, this._wanderTarget, this._wanderRadius);
    cc.Vec2.set(f, 0, this._wanderDistance);
    cc.Vec2.add(p, this._wanderTarget, f);
    this._vehicle.node.getLocalMatrix(y);
    cc.Vec2.transformMat4(p, p, y);
    cc.Vec2.subtract(p, p, this._vehicle.position);
    return p;
  };
  _ctor.prototype._createFeelers = function () {
    cc.Vec2.multiplyScalar(p, this._vehicle.heading, 1.2 * this._vehicle.radius);
    cc.Vec2.add(p, this._vehicle.position, p);
    if (this._feelers.length <= 0) {
      this._feelers.push(cc.v2(p));
    } else {
      this._feelers[0].set(p);
    }
    this._vehicle.heading.rotate(cc.misc.degreesToRadians(60), p);
    cc.Vec2.multiplyScalar(p, p, this._vehicle.radius);
    cc.Vec2.add(p, p, this._vehicle.position);
    if (this._feelers.length <= 1) {
      this._feelers.push(cc.v2(p));
    } else {
      this._feelers[1].set(p);
    }
    this._vehicle.heading.rotate(cc.misc.degreesToRadians(-60), p);
    cc.Vec2.multiplyScalar(p, p, this._vehicle.radius);
    cc.Vec2.add(p, p, this._vehicle.position);
    if (this._feelers.length <= 2) {
      this._feelers.push(cc.v2(p));
    } else {
      this._feelers[2].set(p);
    }
  };
  _ctor.prototype.wallAvoidance = function () {
    return p;
  };
  _ctor.prototype.followPath = function () {
    return p;
  };
  _ctor.prototype.interpose = function () {
    return this.arrive(f, l.FAST);
  };
  _ctor.prototype.hide = function (e, t) {
    var o = 999999999;
    var i = -1;
    cc.Vec2.set(h, 0, 0);
    var n = 0;
    for (var r = t.length; n < r; n++) {
      var a = t[n];
      var s = this._getHidingPosition(a.position, a.radius, e.position);
      var c = cc.Vec2.squaredDistance(s, this._vehicle.position);
      if (c < o) {
        o = c;
        cc.Vec2.set(h, s.x, s.y);
        i = n;
      }
    }
    if (-1 == i) {
      return this.evade(e);
    } else {
      return this.arrive(h, l.FAST);
    }
  };
  _ctor.prototype._getHidingPosition = function (e, t, o) {
    var i = t + 30;
    cc.Vec2.subtract(p, e, o);
    p.normalizeSelf();
    cc.Vec2.multiplyScalar(p, p, i);
    cc.Vec2.add(f, p, o);
    return f;
  };
  _ctor.prototype.cohesion = function (e) {
    cc.Vec2.set(g, 0, 0);
    cc.Vec2.set(d, 0, 0);
    var t = 0;
    var o = 0;
    for (var i = e.length; o < i; o++) {
      var n = e[o];
      if (n != this._vehicle && n != this._targetAgent1 && n.isTag) {
        cc.Vec2.add(g, g, n.position);
        t++;
      }
    }
    if (t > 0) {
      g.divSelf(t);
      var r = this.seek(g);
      cc.Vec2.set(d, r.x, r.y);
      d.normalizeSelf();
      return d;
    }
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.separation = function (e) {
    cc.Vec2.set(p, 0, 0);
    var t = 0;
    for (var o = e.length; t < o; t++) {
      if (e[t].ID != this._vehicle.ID && e[t].isTag && e[t] != this._targetAgent1) {
        cc.Vec2.subtract(f, this._vehicle.position, e[t].position);
        var i = f.mag();
        cc.Vec2.normalize(f, f);
        f.divSelf(i);
        cc.Vec2.add(p, f, p);
      }
    }
    return p;
  };
  _ctor.prototype.alignment = function (e) {
    cc.Vec2.set(g, 0, 0);
    cc.Vec2.set(d, 0, 0);
    var t = 0;
    var o = 0;
    for (var i = e.length; o < i; o++) {
      if (e[o].ID != this._vehicle.ID && e[o].isTag && e[o] != this._targetAgent1) {
        cc.Vec2.add(g, g, e[o].heading);
        t++;
      }
    }
    if (t > 0) {
      g.divSelf(t);
      cc.Vec2.subtract(d, g, this._vehicle.heading);
    }
    return d;
  };
  _ctor.prototype.cohesionPlus = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.separationPlus = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.alignmentPlus = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.calculateWeightedSum = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.calculatePrioritized = function () {
    var e;
    if (this.on(c.seek) && (e = this.seek(this._targetAgent1.position), cc.Vec2.multiplyScalar(e, e, this._weightSeek), !this.accumulateForce(e))) {
      return this._steeringForce;
    } else {
      if (this.on(c.arrive) && (e = this.arrive(this._curTarget, this._deceleration), cc.Vec2.multiplyScalar(e, e, this._weightArrive), !this.accumulateForce(e))) {
        return this._steeringForce;
      } else {
        return this.on(c.pursuit) && (e = this.pursuit(this._targetAgent1), cc.Vec2.multiplyScalar(e, e, this._weightPursuit), this.accumulateForce(e)), this._steeringForce;
      }
    }
  };
  _ctor.prototype.calculateDithered = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.getHidingPosition = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.calculate = function () {
    this._steeringForce.set(cc.Vec2.ZERO);
    if (this.isSpacePartitioningOn()) {
      this.on(c.separation) || this.on(c.allignment) || this.on(c.cohesion);
    } else {
      (this.on(c.separation) || this.on(c.allignment) || this.on(c.cohesion)) && this._vehicle.tagVehiclesWithinViewRange(this._vehicle, this._viewDistance);
    }
    this.calculatePrioritized();
    this.debugRender();
    return this._steeringForce;
  };
  _ctor.prototype.setTarget = function (e) {
    this._curTarget = e;
  };
  _ctor.prototype.setTargetAgent1 = function (e) {
    this._targetAgent1 = e;
  };
  _ctor.prototype.setTargetAgent2 = function (e) {
    this._targetAgent2 = e;
  };
  Object.defineProperty(_ctor.prototype, "targetPos", {
    get: function () {
      return this._curTarget;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "targetAgent1", {
    get: function () {
      return this._targetAgent1;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "targetAgent2", {
    get: function () {
      return this._targetAgent2;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "offset", {
    get: function () {
      return this._offset;
    },
    set: function (e) {
      this._offset = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "force", {
    get: function () {
      return this._steeringForce;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.toggleSpacePartitioningOnOff = function () {
    this._cellSpaceOn = !this._cellSpaceOn;
  };
  _ctor.prototype.isSpacePartitioningOn = function () {
    return this._cellSpaceOn;
  };
  _ctor.prototype.setSummingMethod = function (e) {
    this._summingMethod = e;
  };
  Object.defineProperty(_ctor.prototype, "boxLength", {
    get: function () {
      return this._boxLength;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "wanderJitter", {
    get: function () {
      return this._wanderJitter;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "wanderRadius", {
    get: function () {
      return this._wanderRadius;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "separationWeight", {
    get: function () {
      return this._weightSeparation;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "alignmentWeight", {
    get: function () {
      return this._weightAlignment;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cohesionWeight", {
    get: function () {
      return this._weightCohesion;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.fleeOn = function () {
    this._flags |= c.flee;
  };
  _ctor.prototype.seekOn = function () {
    this._flags |= c.seek;
  };
  _ctor.prototype.arriveOn = function (e) {
    e && this.curTarget.set(e);
    this._flags |= c.arrive;
  };
  _ctor.prototype.wanderOn = function () {
    this._flags |= c.wander;
  };
  _ctor.prototype.pursuitOn = function (e) {
    this._flags |= c.pursuit;
    this._targetAgent1 = e;
  };
  _ctor.prototype.evadeOn = function (e) {
    this._flags |= c.evade;
    this._targetAgent1 = e;
  };
  _ctor.prototype.cohesionOn = function () {
    this._flags |= c.cohesion;
  };
  _ctor.prototype.separationOn = function () {
    this._flags |= c.separation;
  };
  _ctor.prototype.alignmentOn = function () {
    this._flags |= c.allignment;
  };
  _ctor.prototype.obstacleAvoidanceOn = function () {
    this._flags |= c.obstacleAvoidance;
  };
  _ctor.prototype.wallAvoidanceOn = function () {
    this._flags |= c.wallAvoidance;
  };
  _ctor.prototype.followPathOn = function () {
    this._flags |= c.followPath;
  };
  _ctor.prototype.interposeOn = function (e, t) {
    this._flags |= c.interpose;
    this._targetAgent1 = e;
    this._targetAgent2 = t;
  };
  _ctor.prototype.hideOn = function (e) {
    this._flags |= c.hide;
    this._targetAgent1 = e;
  };
  _ctor.prototype.offsetPursuitOn = function (e, t) {
    this._flags |= c.offsetPursuit;
    this._offset = t;
    this._targetAgent1 = e;
  };
  _ctor.prototype.flockingOn = function () {
    this.cohesionOn();
    this.alignmentOn();
    this.separationOn();
  };
  _ctor.prototype.flowOn = function () {
    this._flags |= c.flow;
  };
  _ctor.prototype.fleeOff = function () {
    this.on(c.flee) && (this._flags ^= c.flee);
  };
  _ctor.prototype.seekOff = function () {
    this.on(c.seek) && (this._flags ^= c.seek);
  };
  _ctor.prototype.arriveOff = function () {
    this.on(c.arrive) && (this._flags ^= c.arrive);
  };
  _ctor.prototype.wanderOff = function () {
    this.on(c.wander) && (this._flags ^= c.wander);
  };
  _ctor.prototype.pursuitOff = function () {
    this.on(c.pursuit) && (this._flags ^= c.pursuit);
  };
  _ctor.prototype.evadeOff = function () {
    this.on(c.evade) && (this._flags ^= c.evade);
  };
  _ctor.prototype.cohesionOff = function () {
    this.on(c.cohesion) && (this._flags ^= c.cohesion);
  };
  _ctor.prototype.separationOff = function () {
    this.on(c.separation) && (this._flags ^= c.separation);
  };
  _ctor.prototype.alignmentOff = function () {
    this.on(c.allignment) && (this._flags ^= c.allignment);
  };
  _ctor.prototype.obstacleAvoidanceOff = function () {
    this.on(c.obstacleAvoidance) && (this._flags ^= c.obstacleAvoidance);
  };
  _ctor.prototype.wallAvoidanceOff = function () {
    this.on(c.wallAvoidance) && (this._flags ^= c.wallAvoidance);
  };
  _ctor.prototype.followPathOff = function () {
    this.on(c.followPath) && (this._flags ^= c.followPath);
  };
  _ctor.prototype.interposeOff = function () {
    this.on(c.interpose) && (this._flags ^= c.interpose);
  };
  _ctor.prototype.hideOff = function () {
    this.on(c.hide) && (this._flags ^= c.hide);
  };
  _ctor.prototype.offsetPursuitOff = function () {
    this.on(c.offsetPursuit) && (this._flags ^= c.offsetPursuit);
  };
  _ctor.prototype.flockingOff = function () {
    this.cohesionOff();
    this.alignmentOff();
    this.separationOff();
  };
  _ctor.prototype.flowOff = function () {
    this.on(c.flow) && (this._flags ^= c.flow);
  };
  _ctor.prototype.isFleeOn = function () {
    return this.on(c.flee);
  };
  _ctor.prototype.isSeekOn = function () {
    return this.on(c.seek);
  };
  _ctor.prototype.isArriveOn = function () {
    return this.on(c.arrive);
  };
  _ctor.prototype.isWanderOn = function () {
    return this.on(c.wander);
  };
  _ctor.prototype.isPursuitOn = function () {
    return this.on(c.pursuit);
  };
  _ctor.prototype.isEvadeOn = function () {
    return this.on(c.evade);
  };
  _ctor.prototype.isCohesionOn = function () {
    return this.on(c.cohesion);
  };
  _ctor.prototype.isSeparationOn = function () {
    return this.on(c.separation);
  };
  _ctor.prototype.isAlignmentOn = function () {
    return this.on(c.allignment);
  };
  _ctor.prototype.isObstacleAvoidanceOn = function () {
    return this.on(c.obstacleAvoidance);
  };
  _ctor.prototype.isWallAvoidanceOn = function () {
    return this.on(c.wallAvoidance);
  };
  _ctor.prototype.isFollowPathOn = function () {
    return this.on(c.followPath);
  };
  _ctor.prototype.isInterposeOn = function () {
    return this.on(c.interpose);
  };
  _ctor.prototype.isHideOn = function () {
    return this.on(c.hide);
  };
  _ctor.prototype.isOffsetPursuitOn = function () {
    return this.on(c.offsetPursuit);
  };
  _ctor.prototype.isFlowOn = function () {
    return this.on(c.flow);
  };
  _ctor.prototype.resetFlag = function () {
    this._flags = 0;
  };
  _ctor.prototype._lineIntersection2D = function (e, t, o, i, n) {
    var r = (e.y - o.y) * (i.x - o.x) - (e.x - o.x) * (i.y - o.y);
    var a = (t.x - e.x) * (i.y - o.y) - (t.y - e.y) * (i.x - o.x);
    var s = (e.y - o.y) * (t.x - e.x) - (e.x - o.x) * (t.y - e.y);
    var c = (t.x - e.x) * (i.y - o.y) - (t.y - e.y) * (i.x - o.x);
    if (0 == a || 0 == c) {
      return [false, 0];
    }
    var l = r / a;
    var u = s / c;
    if (l > 0 && l < 1 && u > 0 && u < 1) {
      return n.x = (t.x - e.x) * l + e.x, n.y = (t.y - e.y) * l + e.y, [true, cc.Vec2.distance(e, t) * l];
    } else {
      return [false, 0];
    }
  };
  _ctor.prototype.debugRender = function () {};
  _ctor._steeringForceTweaker = 2e3;
  return _ctor;
}();
exports.default = def_SteeringBehaviors;