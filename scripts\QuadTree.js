Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.QuadTree = undefined;
var i = [cc.Color.BLACK, cc.Color.RED, cc.Color.BLUE, cc.Color.ORANGE, cc.Color.GREEN];
var exp_QuadTree = function () {
  function _ctor(e, t, o, i) {
    this.nodes = null;
    this.children = null;
    this._bounds = null;
    this._depth = 0;
    this._maxChildren = 6;
    this._maxDepth = 6;
    this._bounds = e;
    this.children = [];
    this.nodes = [];
    this._maxChildren = i || 10;
    this._maxDepth = o || 4;
    this._depth = t || 0;
    this._bounds.halfHeight || (this._bounds.halfHeight = this._bounds.height / 2);
    this._bounds.halfWidth || (this._bounds.halfWidth = this._bounds.width / 2);
  }
  _ctor.prototype.getAllNeedTestColliders = function (e) {
    if (this.children.length) {
      e.push(this.children);
      for (var t = 0; t < this.children.length; t++) {
        this.children[t].cindex.push(e.length - 1);
      }
    }
    var o = 0;
    for (var i = this.nodes.length; o < i; o++) {
      this.nodes[o].getAllNeedTestColliders(e);
    }
  };
  _ctor.prototype.render = function (e) {
    var t = 0;
    for (var o = this.nodes.length; t < o; t++) {
      var n = this.nodes[t];
      n && n.render(e);
    }
    e.lineWidth = cc.misc.clampf(8 - this._depth, 2, 8);
    e.strokeColor = i[this._depth];
    e.moveTo(this._bounds.x, this._bounds.y);
    e.lineTo(this._bounds.x + this._bounds.width, this._bounds.y);
    e.lineTo(this._bounds.x + this._bounds.width, this._bounds.y + this._bounds.height);
    e.lineTo(this._bounds.x, this._bounds.y + this._bounds.height);
    e.close();
    e.stroke();
  };
  _ctor.prototype.insert = function (e) {
    if (this.nodes.length) {
      var t = 0;
      for (var o = (n = this._findIndexs(e)).length; t < o; t++) {
        this.nodes[n[t]].insert(e);
      }
    } else {
      this.children.push(e);
      var i = this.children.length;
      if (this._depth < this._maxDepth && i > this._maxChildren) {
        this.nodes.length || this.subdivide();
        for (t = 0; t < i; t++) {
          var n;
          var r = 0;
          for (var a = (n = this._findIndexs(this.children[t])).length; r < a; r++) {
            this.nodes[n[r]].insert(this.children[t]);
          }
        }
        this.children.length = 0;
      }
    }
  };
  _ctor.prototype.retrieve = function (e, t) {
    var o = this._findIndexs(e);
    this.children.length && t.push.apply(t, this.children);
    if (this.nodes.length) {
      var i = 0;
      for (var n = o.length; i < n; i++) {
        this.nodes[o[i]].retrieve(e, t);
      }
    }
    t = t.filter(function (e, o) {
      return t.indexOf(e) >= o;
    });
  };
  _ctor.prototype._findIndexs = function (e) {
    var t = this._bounds;
    var o = t.x + t.halfWidth;
    var i = t.y + t.halfHeight;
    var n = e.y < i;
    var r = e.x < o;
    var a = e.x + e.width > o;
    var s = e.y + e.height > i;
    var c = [];
    s && r && c.push(0);
    n && r && c.push(1);
    n && a && c.push(2);
    s && a && c.push(3);
    return c;
  };
  _ctor.prototype.subdivide = function () {
    var t = this._depth + 1;
    var o = this._bounds.halfWidth;
    var i = this._bounds.halfHeight;
    var n = this._bounds.x;
    var r = this._bounds.y;
    this.nodes[0] = new _ctor({
      x: n,
      y: r + i,
      width: o,
      height: i
    }, t, this._maxDepth, this._maxChildren);
    this.nodes[1] = new _ctor({
      x: n,
      y: r,
      width: o,
      height: i
    }, t, this._maxDepth, this._maxChildren);
    this.nodes[2] = new _ctor({
      x: n + o,
      y: r,
      width: o,
      height: i
    }, t, this._maxDepth, this._maxChildren);
    this.nodes[3] = new _ctor({
      x: n + o,
      y: r + i,
      width: o,
      height: i
    }, t, this._maxDepth, this._maxChildren);
  };
  _ctor.prototype.clear = function () {
    this.children.length = 0;
    var e = 0;
    for (var t = this.nodes.length; e < t; e++) {
      this.nodes[e].clear();
    }
    this.nodes.length = 0;
  };
  return _ctor;
}();
exports.QuadTree = exp_QuadTree;