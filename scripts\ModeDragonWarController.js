var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ModeDragonWarController = undefined;
var $2MVC = require("MVC");
var $2ModeDragonWarModel = require("ModeDragonWarModel");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2CallID = require("CallID");
var $2NotifyID = require("NotifyID");
var $2Time = require("Time");
var exp_ModeDragonWarController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2ModeDragonWarModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "cutMode", {
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_BackToMain, this.backToMain, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Replay, this.onReplay, this);
  };
  _ctor.prototype.backToMain = function () {
    $2UIManager.UIManager.Close("ui/ModeDragonWar/M37_FightUIView");
    $2UIManager.UIManager.Close("ui/ModeDragonWar/M37_FightScene");
  };
  _ctor.prototype.onOpenGame = function (e, t) {
    if (this._model.gameMode == e) {
      this.oldArgs = t;
      this._model.initMonsterConfig(t.param.id);
      t.setIsNeedLoading(false).setNodeGroup(0);
      $2UIManager.UIManager.Open("ui/ModeDragonWar/M37_FightScene", t);
    }
  };
  _ctor.prototype.onReplay = function () {
    var e = this;
    if (this._model.gameMode == this.cutMode) {
      this.closeGame();
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, true);
      $2Time.Time.delay(.3, function () {
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        $2Notifier.Notifier.send($2ListenID.ListenID.Game_Load, e.mode.gameMode, e.oldArgs);
      });
    }
  };
  _ctor.prototype.closeGame = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    $2UIManager.UIManager.Close("ui/ModeDragonWar/M37_FightUIView");
    $2UIManager.UIManager.Close("ui/ModeDragonWar/M37_FightScene");
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.ModeDragonWarController = exp_ModeDragonWarController;