var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ActivityPass = undefined;
var $2MVC = require("MVC");
var $2KnapsackVo = require("KnapsackVo");
var $2Game = require("Game");
var $2Cfg = require("Cfg");
var $2GameUtil = require("GameUtil");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameSeting = require("GameSeting");
var $2RedPointTree = require("RedPointTree");
var $2CallID = require("CallID");
var $2RecordVo = require("RecordVo");
var $2GameatrCfg = require("GameatrCfg");
var $2RBadgeModel = require("RBadgeModel");
exports.ActivityPass = function (e) {
  this.cgIntegral = 0;
  this.cgReward = [];
  this.cgBuffList = [];
  this.cgBuffLock = [];
  this.data = [];
  this.id = e.id;
  this.cgNum = e.fightCount[0];
};
var w = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.dailyData = {
      curday: new Date(Date.now()).getDate(),
      sweep_count: $2Manager.Manager.vo.switchVo.lvSweep[0] + $2Manager.Manager.vo.switchVo.lvSweep[1] + ($2Manager.Manager.Shop.Subscrib_30 ? 1 : 0) + ($2Manager.Manager.Shop.Subscrib_Long ? 3 : 0),
      isSign: false,
      isAdSign: false,
      isADSweepNum: false
    };
    t.activityList = {};
    t.helplist = [];
    t.signIndex = 0;
    t.adSignList = [];
    t.lvRoundReward = {};
    t.lvRound = {};
    t.lvIdUnlock = [1];
    t.curPassLv = 0;
    return t;
  }
  cc__extends(t, e);
  return t;
}($2RecordVo.RecordVo.Data);
var def_ModeBackpackHeroModel = function (e) {
  function _ctor() {
    var o;
    var i = e.call(this) || this;
    i.gameMode = $2Game.Game.Mode.BACKPACKHERO;
    i.energyMinoffset = 20;
    i.ischeckdone = false;
    i.totalCell = 6;
    i._lockcell = 2;
    i.canMergeEquip = [];
    i._curlv = 1;
    i.treeroot = ["shop", "fight", "equip", "role", "activity"];
    i.treeNodes = [["shop", "shop_freecoin", "shop_freediamond"], ["fight", "fight_adreward", "fight_adreward_list", "fight_platformbar", "fight_signin", "fight_signin_ad", "fight_signin_normal"], ["equip"], ["role"], ["activity", "activity_challenge"]];
    i.rewardmark = [];
    i.treemap = {};
    i._adGameSS = "";
    i.buffmap = ((o = {})[$2GameSeting.GameSeting.RarityType.C] = $2GameSeting.GameSeting.RarityType.C, o[$2GameSeting.GameSeting.RarityType.B] = $2GameSeting.GameSeting.RarityType.B, o[$2GameSeting.GameSeting.RarityType.A] = $2GameSeting.GameSeting.RarityType.A, o[$2GameSeting.GameSeting.RarityType.S] = $2GameSeting.GameSeting.RarityType.S, o[$2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments] = $2GameSeting.GameSeting.RarityType.C, o[$2CurrencyConfigCfg.CurrencyConfigDefine.BEquipfragments] = $2GameSeting.GameSeting.RarityType.B, o[$2CurrencyConfigCfg.CurrencyConfigDefine.AEquipfragments] = $2GameSeting.GameSeting.RarityType.A, o[$2CurrencyConfigCfg.CurrencyConfigDefine.SEquipfragments] = $2GameSeting.GameSeting.RarityType.S, o);
    i.fragments = [$2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.BEquipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.AEquipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.SEquipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_xs, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_xh, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_xm, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_dz, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_ed, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_hzg, $2CurrencyConfigCfg.CurrencyConfigDefine.Herofragments_sb, $2CurrencyConfigCfg.CurrencyConfigDefine.tachiEquipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.axeEquipfragments];
    null == _ctor._instance && (_ctor._instance = i);
    return i;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "lockcell", {
    get: function () {
      return this._lockcell;
    },
    set: function (e) {
      this._lockcell = e;
      this.totalCell = 8 - this._lockcell;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "rVo", {
    get: function () {
      return this.recordVo.vo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "curActivity", {
    get: function () {
      return this.game && $2Cfg.Cfg.activity.get(this.game.passType);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.loginFinish = function () {
    var e = this;
    this.recordVo = new $2RecordVo.RecordVo.Mgr("MBack", function () {
      return new w();
    }, {
      isAutoSave: true,
      isAutoToform: false,
      extraDailyKey: ["activityList"],
      onChange: function () {}
    });
    var t = this.fightinfopack.has("pass_round");
    if (t) {
      for (var o = 1; o < t + 2; o++) {
        this.rVo.lvIdUnlock.add(o);
      }
      this.rVo.curPassLv = t;
      this.fightinfopack.del("pass_round");
    }
    this.userEquipPack = new k(true, "UserEquipPack");
    0 == this.userEquipPack.size && $2Cfg.Cfg.RoleUnlock.getArray().filter(function (t) {
      return e.checkCanUnlock(t) && 1 == t.type;
    }).forEach(function (t) {
      e.userEquipPack.add({
        id: t.id,
        isFitOut: true,
        type: $2GameSeting.GameSeting.GoodsType.Equip,
        num: 1,
        lv: 1
      });
    });
    this.checkCanUpgrade();
    $2Cfg.Cfg.RoleUnlock.filter({
      type: 1,
      isShow: 1
    }).forEach(function (t) {
      $2Cfg.Cfg.EquipMergeLv.filter({
        equipId: t.id,
        lv: 4
      }).length > 2 && e.canMergeEquip.push(t.id);
    });
    $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 10, this.rVo.curPassLv);
  };
  _ctor.prototype.cleanShopdata = function () {
    var e = this;
    this.shopGoodspck.forEach(function (t) {
      e.shopGoodspck.del(t.id);
    });
    var t = [100, 101, 200, 300];
    this.dailyAdpack.forEach(function (o) {
      var i = o.id.toString();
      i.indexOf("limit") >= 0 && !t.includes(Number(i.slice(0, 3))) && e.dailyAdpack.del(o.id);
    });
  };
  _ctor.prototype.useRole = function (e) {
    this.fightinfopack.has("role") && this.fightinfopack.del("role");
    this.fightinfopack.addGoods("role", e);
  };
  Object.defineProperty(_ctor.prototype, "cutUseRole", {
    get: function () {
      var e = this.fightinfopack.has("role");
      return {
        roleId: e,
        lv: this.getRoleLv(e)
      };
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getRoleList = function () {
    var e = [];
    this.fightinfopack.getList().forEach(function (t) {
      var o = t.id.toString();
      o.includes("role") && o.length > 4 && e.push(Number(o.split("role")[1]));
    });
    return e;
  };
  _ctor.prototype.getRoleLv = function (e) {
    var t = this.fightinfopack.getVal("role" + e);
    if (0 == t) {
      return 1;
    } else {
      return t;
    }
  };
  _ctor.prototype.roleIsUnlock = function (e) {
    return this.fightinfopack.getVal("role" + e) > 0;
  };
  _ctor.prototype.unlockRole = function (e) {
    this.fightinfopack.has("role" + e) || this.fightinfopack.addGoods("role" + e);
  };
  _ctor.prototype.upgradeRole = function (e) {
    this.fightinfopack.addGoods("role" + e);
  };
  _ctor.prototype.addAdCount = function () {
    var e = $2Cfg.Cfg.CurrencyConfig.get($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
    this.fightinfopack.addGoods(e.define + "_sum", 1, $2GameSeting.GameSeting.GoodsType.System);
    this.fightinfopack.addGoods(e.define, 1, $2GameSeting.GameSeting.GoodsType.System);
    this.updateAdReward();
  };
  _ctor.prototype.useAdCount = function (e) {
    var t = $2Cfg.Cfg.CurrencyConfig.get($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
    if (this.fightinfopack.getVal(t.define) >= e) {
      return this.fightinfopack.useUp(t.define, e), this.updateAdReward(), this.fightinfopack.getVal(t.define);
    } else {
      return -1;
    }
  };
  _ctor.prototype.addFragment = function (e, t) {
    this.fragmentsPack.addGoods(e, t);
    $2Notifier.Notifier.send($2ListenID.ListenID.M20_EquipUpgrade, e);
    $2Notifier.Notifier.send($2ListenID.ListenID.M20_GetRoleFrag, e);
    this.checkCanUpgrade();
  };
  _ctor.prototype.useUpFragment = function (e, t) {
    this.fragmentsPack.useUp(e, t);
    $2Notifier.Notifier.send($2ListenID.ListenID.M20_EquipUpgrade, e);
    $2Notifier.Notifier.send($2ListenID.ListenID.M20_GetRoleFrag, e);
    this.checkCanUpgrade();
  };
  _ctor.prototype.checkCanUpgrade = function () {
    var e = this;
    this.userEquipPack.forEach(function (t) {
      var o;
      var i = $2Cfg.Cfg.EquipLv.find({
        equipId: t.id,
        lv: t.num
      });
      if (!i) {
        3 == (null === (o = $2Cfg.Cfg.RoleUnlock.get(t.id)) || undefined === o ? undefined : o.type) && e.unlockRole(t.id);
        return void e.userEquipPack.del(t.id);
      }
      var n = e.fragmentsPack.has(t.id) >= i.upgradeNeedle;
      $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Equip_FragmentUpgrade, n, t.id);
    });
    $2Cfg.Cfg.RoleUnlock.filter({
      type: 3,
      isShow: 1
    }).forEach(function (t) {
      var o = $2Cfg.Cfg.RoleLv.find({
        roleId: t.id,
        lv: Math.max(e.fightinfopack.getVal("role" + t.id), 1)
      });
      var i = e.fragmentsPack.has(t.id) >= o.upgradeNeedle && o.lv < 20;
      $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Role_FragmentUpgrade, i, t.id);
    });
  };
  _ctor.prototype.currencyIsEnough = function (e, t) {
    var o = $2Manager.Manager.vo.knapsackVo.getVal(e) >= t;
    e != $2CurrencyConfigCfg.CurrencyConfigDefine.Energy || o || $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GetEnergy", $2MVC.MVC.openArgs());
    return o;
  };
  _ctor.prototype.firstLvCheck = function () {
    if (!this.fightinfopack.has("fstcheck")) {
      this.fightinfopack.addGoods("fstcheck");
      $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, 100);
      this.addFragment(1e3, 6);
    }
  };
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "CurChallengeLv", {
    get: function () {
      var e = $2Cfg.Cfg.BagModeLv.getArray().filter(function (e) {
        return 1 == e.type;
      });
      return Math.min(e.lastVal.lvid, this.rVo.curPassLv + 1);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setUnlockPassRound = function (e) {
    var t = $2Cfg.Cfg.BagModeLv.get(e);
    var o = e + 1;
    var i = $2Cfg.Cfg.BagModeLv.get(o);
    t.unlockLvid && this.rVo.lvIdUnlock.add(t.unlockLvid[0]);
    if (i && 1 == i.type && o > this.rVo.curPassLv) {
      this.rVo.lvIdUnlock.includes(o) || this.rVo.lvIdUnlock.push(o);
      4 != o || this.fightinfopack.has("rolemenu") || $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_NewFunctionUnlock", $2MVC.MVC.openArgs().setIsNeedLoading(false));
      this.updateRewardMark(o);
      $2Notifier.Notifier.send($2ListenID.ListenID.ModeBack_RoundUnlock, o);
      this.checkUnlockEquip();
    }
  };
  Object.defineProperty(_ctor.prototype, "PlayingLv", {
    get: function () {
      return this._curlv;
    },
    set: function (e) {
      this._curlv = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getEquipLv = function (e) {
    var t;
    return (null === (t = this.userEquipPack.getItem(e)) || undefined === t ? undefined : t.lv) || 1;
  };
  _ctor.prototype.getEquip = function (e) {
    return this.userEquipPack.getItem(e);
  };
  _ctor.prototype.AssembleEquip = function (e) {
    this.userEquipPack.setFitOut(e, true);
  };
  _ctor.prototype.UnAssembleEquip = function (e) {
    this.userEquipPack.setFitOut(e, false);
  };
  _ctor.prototype.setAutoChipChangeUnEquip = function (e) {
    if (!this.userEquipPack.has(e)) {
      var t = $2Cfg.Cfg.RoleUnlock.get(e);
      if (t && this.fragmentsPack.getVal(e) >= t.Count && 1 == t.type) {
        var o = this.fragmentsPack.getItem(e);
        o && (o.num = o.num - t.Count);
        this.userEquipPack.add({
          id: t.id,
          isFitOut: false,
          type: $2GameSeting.GameSeting.GoodsType.Equip,
          num: 1,
          lv: 1
        });
      }
    }
  };
  Object.defineProperty(_ctor.prototype, "cardPool", {
    get: function () {
      return $2Cfg.Cfg.BagModeSkillPool.get(1);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.fightPropWidth = function (e, t, o) {
    var i;
    var n = this;
    undefined === e && (e = false);
    undefined === t && (t = false);
    var s = e ? this.cardPool.adPool : this.cardPool.normalPool;
    var c = this.game.packView;
    var l = c.checkCanGetBlockType;
    var d = $2Manager.Manager.vo.switchVo;
    c.isFullLimt && (t = true);
    var y = c.allList;
    var m = [];
    var _ = cc__spreadArrays(s[0]);
    var M = cc__spreadArrays(s[1]);
    var C = [];
    var w = function (e) {
      if ([2e4].includes(e)) {
        return 1;
      } else {
        if ([20001, 20002].includes(e)) {
          return 2;
        } else {
          if ([20003, 20004, 20005, 20006, 20007, 20008].includes(e)) {
            return 2;
          } else {
            return 0;
          }
        }
      }
    };
    var S = this.userEquipPack.filter(function (e) {
      return e.isFitOut;
    }).map(function (e) {
      return e.id;
    });
    var k = d.grid12Appear.find(function (e) {
      return e[0] == n.game.level && n.game.batchNum > e[1] && n.game.batchNum <= e[2];
    });
    var P = d.gemAppearWeight.filter(function (e) {
      return e[0] == n.game.level;
    });
    0 == P.length && (P = d.gemAppearWeight.filter(function (e) {
      return 999 == e[0];
    }));
    var O = P.find(function (e) {
      return n.game.batchNum > e[1] && n.game.batchNum <= e[2];
    });
    var I = d.gemAppearBegin.find(function (e) {
      return n.game.level == e[0] && n.game.batchNum >= e[1];
    }) || d.gemAppearBegin.lastVal;
    var D = d.lv2Equip.find(function (e) {
      return e[0] == n.game.level && n.game.batchNum <= e[1];
    });
    var T = this.role.buffMgr.getAttr($2GameatrCfg.GameatrDefine.bagGridAppear);
    _.forEach(function (e, i) {
      var r;
      var a;
      var s = $2Cfg.Cfg.EquipMergeLv.get(e);
      var p = $2Cfg.Cfg.RoleUnlock.get(s.equipId);
      var f = p.rarity;
      if (1 != p.type || (null === (r = n.userEquipPack.getItem(s.equipId)) || undefined === r ? undefined : r.isFitOut)) {
        if (4 == p.type) {
          if (0 == d.gemAppearSwitch) {
            return;
          }
          if (n.game.recordVo.vo.gemAppearBegin[f] <= 0) {
            return;
          }
          if (n.game.gemGetNum(s.equipId) <= 0) {
            return;
          }
        }
        if (3 != (null === (a = n.game) || undefined === a ? undefined : a.passType) || ![1e4, 4011].includes(p.id)) {
          var h = {
            id: e,
            w: M[i],
            type: p.type
          };
          var g = 2 == p.type;
          if (!g || !t) {
            var y = w(s.id);
            c.isFull && 3 == y && (h.w += d.gridWeight[1]);
            g && k && (h.w += k[2 + y]);
            2 == s.lv && D && (h.w += D[2]);
            4 == p.type && O && (h.w += O[f]);
            T && (h.w += T);
            if (g) {
              if (l.includes(p.grid)) {
                h.w += 0;
              } else {
                h.w -= 20;
              }
            }
            o && o(h, s);
            m.push(h);
          }
        }
      }
    });
    this.role.buffMgr.use(5120, false, function () {
      m.forEach(function (e) {
        [1e4, 10001].includes(e.id) && (e.id = 10003);
      });
    });
    for (var A = m.length - 1; A >= 0; A--) {
      m[A].w <= 0 && $2GameUtil.GameUtil.deleteArrItem(m, m[A]);
    }
    $2GameUtil.GameUtil.weightGetList(m, e ? 4 : 3, "w").forEach(function (e) {
      C.push($2Cfg.Cfg.EquipMergeLv.get(e.id));
    });
    var R = d.Lv1EquipAppear.find(function (e) {
      return e[0] == n.game.level && n.game.batchNum >= e[1] && n.game.batchNum < e[2];
    });
    if (R && $2GameUtil.GameUtil.weight(R[3])) {
      var B = $2GameUtil.GameUtil.getRandomInArray([2, 3])[0];
      var L = $2GameUtil.GameUtil.weight(R[4]) ? $2GameUtil.GameUtil.getRandomInArray(S)[0] : $2GameUtil.GameUtil.getRandomInArray(c.propList.filter(function (e) {
        return 1 == e.propType;
      }).map(function (e) {
        return e.roleID;
      }))[0];
      var N = L && $2Cfg.Cfg.EquipMergeLv.find({
        equipId: +L,
        lv: 1
      });
      if (N) {
        for (A = 0; A < B; A++) {
          var E = C.findIndex(function (e) {
            return 1 == e.lv && e.equipId <= 2e4 && e.equipId != N.equipId;
          });
          E >= 0 && C.splice(E, 1, N);
        }
      }
    }
    var V = c.propList.filter(function (e) {
      return 3 == e.mergeCfg.lv;
    });
    if (!e && this.game.batchNum >= d.Lv3EquipAppear[1] && this.game.recordVo.vo.Lv3EquipAppear > 0 && $2GameUtil.GameUtil.weight(d.Lv3EquipAppear[3])) {
      var G = undefined;
      var j = $2Cfg.Cfg.EquipMergeLv.filter({
        lv: 3
      }).filter(function (e) {
        var t;
        return (null === (t = n.userEquipPack.getItem(e.equipId)) || undefined === t ? undefined : t.isFitOut) && n.canMergeEquip.includes(e.equipId);
      });
      if ($2GameUtil.GameUtil.weight(d.Lv3EquipAppear[4]) && j.length > 0) {
        G = $2GameUtil.GameUtil.randomArr(j);
      } else if (V.length > 0) {
        G = $2GameUtil.GameUtil.randomArr(V).mergeCfg;
      } else {
        j = $2Cfg.Cfg.EquipMergeLv.filter({
          lv: 3
        }).filter(function (e) {
          var t;
          if (null === (t = n.userEquipPack.getItem(e.equipId)) || undefined === t) {
            return undefined;
          } else {
            return t.isFitOut;
          }
        });
        G = $2GameUtil.GameUtil.randomArr(j);
      }
      A = C.findIndex(function (e) {
        return e.equipId < 2e4;
      });
      C.splice(A, 1, G);
    }
    var x = C.findIndex(function (e) {
      return e.id >= 20003;
    });
    if (x >= 0 && C.length >= 2 && $2GameUtil.GameUtil.weight(d.fit3GridEquip[0])) {
      var F = C[x];
      var U = $2Cfg.Cfg.RoleUnlock.get(F.equipId);
      var H = $2Cfg.Cfg.RoleUnlock.find({
        type: 1,
        grid: U.grid
      });
      if (H && (null === (i = this.userEquipPack.getItem(H.id)) || undefined === i ? undefined : i.isFitOut)) {
        G = $2Cfg.Cfg.EquipMergeLv.find({
          equipId: H.id,
          lv: $2GameUtil.GameUtil.weight(d.fit3GridEquip[1]) ? 2 : 1
        });
        var q = [0, 1, 2];
        q.splice(x, 1);
        C.splice($2GameUtil.GameUtil.getRandomInArray(q)[0], 1, G);
      }
    }
    if (0 == d.gemAppearSwitch && I && $2GameUtil.GameUtil.weightFloat(I[4])) {
      var W = e ? this.cardPool.adGem : this.cardPool.norGem;
      m.length = 0;
      W[0].forEach(function (e, t) {
        var o = $2Cfg.Cfg.EquipMergeLv.get(e);
        var i = $2Cfg.Cfg.RoleUnlock.get(o.equipId);
        var r = i.rarity;
        if (!(n.game.recordVo.vo.gemAppearBegin[r] <= 0 || n.game.gemGetNum(i.id) <= 0)) {
          var a = {
            id: e,
            w: W[1][t],
            type: i.type
          };
          O && (a.w += O[r]);
          m.push(a);
        }
      });
      G = $2GameUtil.GameUtil.weightGetValue(m);
      C.push($2Cfg.Cfg.EquipMergeLv.get(G.id));
    }
    if (e) {
      var z = C.find(function (e) {
        return 2 == e.lv;
      });
      var K = C.find(function (e) {
        return 3 == e.lv;
      });
      if (!z && !K) {
        G = undefined;
        var J = undefined;
        J = y.filter(function (e) {
          return 2 == e.mergeCfg.lv;
        }).map(function (e) {
          return e.mergeCfg.id;
        });
        if (J) {
          G = $2Cfg.Cfg.EquipMergeLv.get($2GameUtil.GameUtil.randomArr(J));
        }
        G || (J = y.filter(function (e) {
          return 3 == e.mergeCfg.lv;
        }).map(function (e) {
          return e.mergeCfg.id;
        })) && (G = $2Cfg.Cfg.EquipMergeLv.get($2GameUtil.GameUtil.randomArr(J)));
        if (!G) {
          var Y = $2GameUtil.GameUtil.weightGetValue([{
            id: 2,
            w: d.adRefreshEquip[2]
          }, {
            id: 3,
            w: d.adRefreshEquip[3]
          }]).id;
          G = $2GameUtil.GameUtil.randomArr($2Cfg.Cfg.EquipMergeLv.getArray().filter(function (e) {
            var t;
            return (null === (t = n.userEquipPack.getItem(e.equipId)) || undefined === t ? undefined : t.isFitOut) && e.lv == Y;
          }));
        }
        C.splice(C.findIndex(function (e) {
          return 1 == e.lv && e.equipId <= 2e4;
        }), 1, G);
      }
      var Z = C.filter(function (e) {
        return e.id >= 20001;
      });
      if (!t && 0 == Z.length && $2GameUtil.GameUtil.weightFloat(d.adRefreshEquip[1])) {
        var Q = $2Cfg.Cfg.EquipMergeLv.getArray().filter(function (e) {
          return e.id >= 20001 && l.includes($2Cfg.Cfg.RoleUnlock.get(e.equipId).grid);
        });
        G = $2GameUtil.GameUtil.getRandomInArray(Q)[0];
        if (G) {
          C.splice(C.findIndex(function (e) {
            return 1 == e.lv && e.equipId <= 2e4;
          }), 1, G);
        }
      }
    }
    C.filter(function (e) {
      return e.id >= 2e4;
    }).forEach(function (e) {
      var t = w(e.id);
      $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "gridAppearCount", cc__assign({
        Type: t
      }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    });
    return C;
  };
  _ctor.prototype.fightBuffWidth = function (e, t) {
    var o = this;
    undefined === e && (e = false);
    undefined === t && (t = 3);
    var i = $2GameUtil.GameUtil.deepCopy(e ? this.cardPool.adBuffPool : this.cardPool.norBuffPool);
    var n = [];
    var r = i[0];
    var a = i[1];
    var s = this.game.packView;
    var c = s.propList.filter(function (e) {
      return e.isMerge;
    });
    var f = [];
    this.role.buffMgr.use(3042, false, function () {
      t += 1;
    });
    var h = this.userEquipPack.filter(function (e) {
      return e.isFitOut;
    }).map(function (e) {
      return e.id;
    });
    var d = [];
    var y = [];
    this.userEquipPack.forEach(function (e) {
      if (e.isFitOut) {
        var t = $2Cfg.Cfg.EquipLv.find({
          equipId: e.id
        });
        t.skill && d.push.apply(d, t.skill);
        $2Cfg.Cfg.EquipLv.filter({
          equipId: e.id
        }).forEach(function (t) {
          t.lv > e.lv && t.unlockBuff && y.push(t.unlockBuff);
        });
      }
    });
    s.propList.forEach(function (e) {
      return !d.includes(e.prorSkillId) && d.push(e.prorSkillId);
    });
    var _ = [];
    r.forEach(function (e, t) {
      var i = $2Game.ModeCfg.Buff.get(e);
      if (!i.skillId || $2GameUtil.GameUtil.hasIntersection(d, i.skillId)) {
        var r = o.role.buffMgr.get(e);
        if (r) {
          if (r.isMaxLayer) {
            return;
          }
          if (1 == i.isSelect) {
            return;
          }
        }
        if (![13202, 13203].includes(e) || h.includes(1016)) {
          if (y.includes(e)) {
            i.skillId && c.find(function (e) {
              return i.skillId[0] == e.prorSkillId;
            }) && f.push(e);
          } else {
            i.skillId && _.push(e);
            var s = {
              id: e,
              w: a[t],
              isAd: 0
            };
            o.role.buffMgr.use(3043, true, function (e) {
              [$2GameSeting.GameSeting.RarityType.B, $2GameSeting.GameSeting.RarityType.A, $2GameSeting.GameSeting.RarityType.S].includes(i.rarity) && (s.w += e.cutVo.value[0][0]);
            });
            n.push(s);
          }
        }
      }
    });
    for (var v = n.length - 1; v >= 0; v--) {
      n[v].w <= 0 && $2GameUtil.GameUtil.deleteArrItem(n, n[v]);
    }
    var M = [];
    $2GameUtil.GameUtil.weightGetList(n, t).forEach(function (e) {
      M.push({
        id: e.id,
        isAd: e.isAd
      });
    });
    var b = $2Manager.Manager.vo.switchVo.equipbuff.find(function (e) {
      return e[0] == o.game.passType;
    });
    null == b && (b = $2Manager.Manager.vo.switchVo.equipbuff.find(function (e) {
      return 1 == e[0];
    }));
    if (f.length > 0 && $2GameUtil.GameUtil.weight(b[2])) {
      M.splice(0, 1);
      M.push({
        id: $2GameUtil.GameUtil.randomArr(f),
        isAd: 1
      });
    }
    var C = M.filter(function (e) {
      return _.includes(e.id) || f.includes(e.id);
    });
    if ($2GameUtil.GameUtil.weight(b[1]) && _.length > 0 && 0 == C.length) {
      M.splice(0, 1);
      M.push({
        id: $2GameUtil.GameUtil.randomArr(_),
        isAd: 0
      });
    }
    return M;
  };
  _ctor.prototype.fightHeroBuff = function (e, t) {
    var o = this;
    var i = [];
    i.push.apply(i, $2Cfg.Cfg.RoleLv.find({
      roleId: e,
      lv: t
    }).buff);
    $2Cfg.Cfg.RoleUnlock.filter({
      type: 3
    }).forEach(function (t) {
      t.id != e && 20 == o.getRoleLv(t.id) && i.push.apply(i, $2Cfg.Cfg.RoleLv.find({
        roleId: t.id,
        lv: 20
      }).buff);
    });
    return i;
  };
  _ctor.prototype.checkCanUnlock = function (e) {
    if (1 == e.unlock) {
      return $2Manager.Manager.leveMgr.vo.curPassLv >= e.Count;
    } else {
      if (2 == e.unlock) {
        return this.fragmentsPack.has(e.id) >= e.Count;
      } else {
        return 3 != e.unlock && undefined;
      }
    }
  };
  _ctor.prototype.checkIsUnlock = function (e) {
    return this.userEquipPack.has(e.id);
  };
  _ctor.prototype.getUnlockBuff = function () {
    var e = [];
    var t = [];
    this.userEquipPack.forEach(function (o, i, n) {
      o.isFitOut && $2Cfg.Cfg.EquipLv.filter({
        equipId: +n
      }).forEach(function (i) {
        if (i.unlockBuff) {
          if (o.num < i.lv) {
            t.push(i.unlockBuff);
          } else {
            e.push(i.unlockBuff);
          }
        }
      });
    });
    return {
      unlock: e,
      notUnlock: t
    };
  };
  _ctor.prototype.getPassMainID = function (e) {
    var t = $2Cfg.Cfg.BagModeLv.getArray().find(function (t) {
      var o;
      if (null === (o = t.unlockLvid) || undefined === o) {
        return undefined;
      } else {
        return o.includes(e);
      }
    });
    if (t) {
      return t.lvid;
    } else {
      return e;
    }
  };
  _ctor.prototype.getLvRewardData = function (e, t) {
    var o;
    if (null === (o = this.rVo.lvRoundReward[e]) || undefined === o) {
      return undefined;
    } else {
      return o.includes(t);
    }
  };
  _ctor.prototype.setLvRewardData = function (e, t) {
    var o;
    (o = this.rVo.lvRoundReward)[e] || (o[e] = []);
    this.rVo.lvRoundReward[e].includes(t) || this.rVo.lvRoundReward[e].push(t);
  };
  _ctor.prototype.setLvMaxRound = function (e, t, o) {
    var i = 1 == $2Cfg.Cfg.BagModeLv.get(e).type;
    if (o && i) {
      this.rVo.curPassLv = Math.max(this.rVo.curPassLv, e);
      this.setUnlockPassRound(e);
      $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 10, e);
    }
    if (t > this.getLvMaxRound(e)) {
      this.rVo.lvRound[e] = t;
      this.updateRewardMark(e);
    }
  };
  _ctor.prototype.getLvMaxRound = function (e) {
    return this.rVo.lvRound[e] || 0;
  };
  _ctor.prototype.checkLvIsPass = function (e) {
    var t = $2Cfg.Cfg.BagModeLv.get(e);
    return this.getLvMaxRound(e) >= t.wave.lastVal;
  };
  _ctor.prototype.getRandomFragById = function (e) {
    var t;
    var o = this;
    var i = [];
    (t = {})[$2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments] = $2GameSeting.GameSeting.RarityType.C;
    t[$2CurrencyConfigCfg.CurrencyConfigDefine.AEquipfragments] = $2GameSeting.GameSeting.RarityType.A;
    t[$2CurrencyConfigCfg.CurrencyConfigDefine.BEquipfragments] = $2GameSeting.GameSeting.RarityType.B;
    t[$2CurrencyConfigCfg.CurrencyConfigDefine.SEquipfragments] = $2GameSeting.GameSeting.RarityType.S;
    var n = t;
    var r = $2Cfg.Cfg.RoleUnlock.getArray().filter(function (e) {
      return o.userEquipPack.has(e.id) && 1 == e.type;
    });
    var a = e[1];
    var s = e[0];
    var c = r.filter(function (e) {
      return e.rarity == n[s];
    });
    if (0 == c.length) {
      return c;
    }
    for (var l = 0; l < a; l++) {
      var f = $2GameUtil.GameUtil.getRandomInArray(c)[0];
      i[f.id] || (i[f.id] = 0);
      i[f.id]++;
    }
    return i;
  };
  _ctor.prototype.initTreeNode = function () {
    var e = this;
    $2Cfg.Cfg.RoleUnlock.getArray().filter(function (e) {
      return 1 == e.type;
    }).forEach(function (t) {
      return e.treeNodes[2].push("equip_" + t.id);
    });
    $2Cfg.Cfg.RoleUnlock.getArray().filter(function (e) {
      return 3 == e.type;
    }).forEach(function (t) {
      return e.treeNodes[3].push("role_" + t.id);
    });
    var t = $2Cfg.Cfg.BagModeLv.getArray();
    for (var o = 0; o < t.length; o++) {
      var i = t[o];
      var n = i.lvid;
      var r = this.getLvMaxRound(n);
      for (var a = 0; a < 3; a++) {
        if (n <= this.rVo.curPassLv) {
          var s = this.getLvRewardData(n, a);
          i.wave[a] <= r && !s && this.rewardmark.push("fight_" + a + "|" + n + "|" + i.wave[a]);
        }
        this.treeNodes[1].push("fight_" + a + "|" + n + "|" + i.wave[a]);
      }
    }
    for (o = 0; o < this.treeroot.length; o++) {
      var c = this.treeroot[o];
      this.treemap[c] = new $2RedPointTree.default(c, this.treeNodes[o]);
    }
    $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView).menunodes.forEach(function (t, o) {
      var i = e.treeroot[o];
      e.treemap[i].SetCallBack(i, i, function (e) {
        t.active = e > 0;
      });
    });
  };
  _ctor.prototype.updateAdReward = function () {
    if (this.treemap.fight) {
      var e = $2Cfg.Cfg.CurrencyConfig.get($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
      var t = this.fightinfopack.getVal(e.define);
      var o = this.fightinfopack.getVal("adrewardC");
      var i = this.fightinfopack.getVal(e.define + "_sum");
      var n = $2Cfg.Cfg.adReward.getArray()[o];
      if (n && i >= n.adcount && t >= 10) {
        this.treemap.fight.ChangeRedPointCnt("fight_adreward_list", 1);
      } else {
        var r = this.treemap.fight.getRedpointCnt("fight_adreward_list");
        this.treemap.fight.ChangeRedPointCnt("fight_adreward_list", -r);
      }
    }
  };
  _ctor.prototype.updateRewardMark = function (e) {
    var t;
    var o = $2Cfg.Cfg.BagModeLv.getArray()[e - 1];
    var i = this.getLvMaxRound(e);
    for (var n = 0; n < 3; n++) {
      if (e <= this.rVo.curPassLv) {
        var r = "fight_" + n + "|" + e + "|" + o.wave[n];
        var a = this.getLvRewardData(e, n);
        if (o.wave[n] <= i && !a && !this.rewardmark.includes(r)) {
          null === (t = this.treemap.fight) || undefined === t || t.ChangeRedPointCnt(r, 1);
          this.rewardmark.push(r);
        }
      }
    }
  };
  _ctor.prototype.checkUnlockEquip = function () {
    var e = this;
    $2Cfg.Cfg.RoleUnlock.getArray().filter(function (t) {
      return e.checkCanUnlock(t) && 1 == t.type;
    }).forEach(function (t) {
      if (!e.userEquipPack.has(t.id)) {
        e.userEquipPack.add({
          id: t.id,
          isFitOut: false,
          type: $2GameSeting.GameSeting.GoodsType.Equip,
          num: 1,
          lv: 1
        });
        $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_NewEquipUnlock", $2MVC.MVC.openArgs().setParam({
          id: t.id,
          cb: function () {}
        }), 1);
      }
    });
  };
  Object.defineProperty(_ctor.prototype, "adGameSS", {
    get: function () {
      return this._adGameSS;
    },
    set: function (e) {
      this._adGameSS = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_ModeBackpackHeroModel;
var k = function (e) {
  function t(t, o) {
    undefined === t && (t = true);
    var i = e.call(this, t, o) || this;
    var n = 1;
    for (var r in i.list) {
      null == i.list[r].sort && (i.list[r].sort = n);
      n++;
    }
    return i;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "mode", {
    get: function () {
      return def_ModeBackpackHeroModel.instance;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.resetItem = function (e, t) {
    for (var o in t) {
      e[o] = t[o];
    }
    this.list[e.id] = e;
    this.SaveData();
  };
  t.prototype.upgrade = function (e, t) {
    undefined === t && (t = 1);
    this.getItem(e).lv += t;
    this.SaveData();
    this.SendEvent(e);
    this.mode.checkCanUpgrade();
  };
  t.prototype.setFitOut = function (e, t) {
    var o = this.getItem(e);
    o.isFitOut = t;
    1 == t && (o.sort = this.findVoid);
    this.SaveData();
    this.SendEvent(e);
  };
  t.prototype.replace = function (e, t) {
    var o = this.getItem(e);
    o.isFitOut = false;
    var i = this.getItem(t);
    i.isFitOut = true;
    i.sort = o.sort;
    this.SaveData();
    this.SendEvent(t);
  };
  Object.defineProperty(t.prototype, "findVoid", {
    get: function () {
      var e = 0;
      var t = this.filter(function (e) {
        return e.isFitOut;
      }).sort(function (e, t) {
        return e.sort - t.sort;
      });
      t.forEach(function (t) {
        0 == e && e == t.sort && (e = t.sort + 1);
      });
      0 == e && (e = t.lastVal.sort + 1);
      return e;
    },
    enumerable: false,
    configurable: true
  });
  return t;
}($2KnapsackVo.KnapsackVo.Mgr);