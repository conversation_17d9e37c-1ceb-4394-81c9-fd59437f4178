Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TMap = exports.GameSeting = undefined;
(function (e) {
  var t;
  var o;
  var i;
  var n;
  (function (e) {
    e[e.Not = 0] = "Not";
    e[e.Money = 1] = "Money";
    e[e.Fragment = 2] = "Fragment";
    e[e.Goods = 3] = "Goods";
    e[e.Skin = 4] = "Skin";
    e[e.Equip = 5] = "Equip";
    e[e.Cell = 6] = "Cell";
    e[e.Skill = 7] = "Skill";
    e[e.MergeEquip = 30] = "MergeEquip";
    e[e.OpenBuffSelect = 96] = "OpenBuffSelect";
    e[e.BuffDropSelect = 97] = "BuffDropSelect";
    e[e.RandomBuffDrop = 98] = "RandomBuffDrop";
    e[e.Special = 99] = "Special";
    e[e.DragonBall = 100] = "DragonBall";
    e[e.System = 999] = "System";
  })(e.GoodsType || (e.GoodsType = {}));
  (function (e) {
    e[e.Ease = 1] = "Ease";
    e[e.Hard = 2] = "Hard";
    e[e.Hell = 3] = "Hell";
    e[e.Nightmare = 4] = "Nightmare";
    e[e.Activity_10 = 10] = "Activity_10";
    e[e.Activity_11 = 11] = "Activity_11";
  })(n = e.DiffType || (e.DiffType = {}));
  var r;
  (t = {})[n.Ease] = {
    name: "普通",
    colorStr: "#48d230",
    color: cc.color("#48d230")
  };
  t[n.Hard] = {
    name: "困难",
    colorStr: "#cd3dff",
    color: cc.color("#cd3dff")
  };
  t[n.Hell] = {
    name: "地狱",
    colorStr: "#f04b5d",
    color: cc.color("#f04b5d")
  };
  t[n.Nightmare] = {
    name: "噩梦",
    colorStr: "##FF00E0",
    color: cc.color("##FF00E0")
  };
  var a = t;
  e.getDiffDef = function (e) {
    return a[e] || a[n.Ease];
  };
  (function (e) {
    e[e.COMPLETE = 0] = "COMPLETE";
    e[e.NOT_SUPPORT = 1] = "NOT_SUPPORT";
    e[e.FAIL = 2] = "FAIL";
  })(e.ProgressCode || (e.ProgressCode = {}));
  (function (e) {
    e[e.Not = 0] = "Not";
    e[e.Game = 9] = "Game";
  })(e.TweenType || (e.TweenType = {}));
  (function (e) {
    e[e.C = 1] = "C";
    e[e.B = 2] = "B";
    e[e.A = 3] = "A";
    e[e.S = 4] = "S";
    e[e.SS = 5] = "SS";
    e[e.SSS = 6] = "SSS";
  })(r = e.RarityType || (e.RarityType = {}));
  var s;
  (o = {})[r.C] = {
    img: "img/common/tpdg_icon_midj_01",
    buffImg: "v1/images/map/bg_buff_02",
    blockImg: "v1/images/bg/bg_icon_01",
    color: cc.color("#FFFFFF"),
    framgimg: "v1/images/icon/icon_card_01",
    tagImg: "img/ModeBackpackHero/role/c",
    colorStr: "#FFFFFF",
    name: "普通",
    hCardBg: "img/ModeBackpackHero/ui/img_tj_zbpzd3",
    hCardBlock: "img/ModeBackpackHero/ui/img_tj_zbpzk3",
    tag: "img/ModeBackpackHero/ui/img_tj_zbbqd3",
    bubbleBg: "img/buff/img_buff_hui"
  };
  o[r.B] = {
    img: "img/common/tpdg_icon_midj_02",
    buffImg: "v1/images/map/bg_buff_03",
    blockImg: "v1/images/bg/bg_icon_02",
    color: cc.color("#62FE8B"),
    framgimg: "v1/images/icon/icon_card_03",
    tagImg: "img/ModeBackpackHero/role/b",
    colorStr: "#62FE8B",
    name: "稀有",
    hCardBg: "img/ModeBackpackHero/ui/img_tj_zbpzd3",
    hCardBlock: "img/ModeBackpackHero/ui/img_tj_zbpzk3",
    tag: "img/ModeBackpackHero/ui/img_tj_zbbqd3",
    bubbleBg: "img/buff/img_buff_lan"
  };
  o[r.A] = {
    img: "img/common/tpdg_icon_midj_03",
    buffImg: "v1/images/map/bg_buff_04",
    blockImg: "v1/images/bg/bg_icon_03",
    color: cc.color("#B16BFF"),
    framgimg: "v1/images/icon/icon_card_04",
    tagImg: "img/ModeBackpackHero/role/a",
    colorStr: "#B16BFF",
    name: "史诗",
    hCardBg: "img/ModeBackpackHero/ui/img_tj_zbpzd2",
    hCardBlock: "img/ModeBackpackHero/ui/img_tj_zbpzk2",
    tag: "img/ModeBackpackHero/ui/img_tj_zbbqd2",
    bubbleBg: "img/buff/img_buff_zi"
  };
  o[r.S] = {
    img: "img/common/tpdg_icon_midj_04",
    buffImg: "v1/images/map/bg_buff_05",
    blockImg: "v1/images/bg/bg_icon_04",
    color: cc.color("#ff782e"),
    framgimg: "v1/images/icon/icon_card_05",
    tagImg: "img/ModeBackpackHero/role/s",
    colorStr: "#ff782e",
    name: "传说",
    hCardBg: "img/ModeBackpackHero/ui/img_tj_zbpzd1",
    hCardBlock: "img/ModeBackpackHero/ui/img_tj_zbpzk1",
    tag: "img/ModeBackpackHero/ui/img_tj_zbbqd1",
    bubbleBg: "img/buff/img_buff_cheng"
  };
  o[r.SS] = {
    img: "img/common/tpdg_icon_midj_04",
    buffImg: "v1/images/map/bg_buff_05",
    blockImg: "v1/images/bg/bg_icon_05",
    tagImg: "img/ModeBackpackHero/role/ss",
    color: cc.color("#d53c3c"),
    colorStr: "#d53c3c"
  };
  var c = o;
  function l(e) {
    return [null, "C", "B", "A", "S", "SS"].indexOf(e);
  }
  e.getRarity = function (e) {
    return c[e] || c[r.C];
  };
  e.getRarityByStr = function (e) {
    return c[l(e)] || c[r.C];
  };
  e.strToRarityType = l;
  (function (e) {
    e[e.Nearby = 1] = "Nearby";
    e[e.RandomTarget = 2] = "RandomTarget";
    e[e.Self = 3] = "Self";
    e[e.RandomPos = 4] = "RandomPos";
    e[e.Similar = 5] = "Similar";
    e[e.JoytPos = 10] = "JoytPos";
    e[e.Enemy = 100] = "Enemy";
  })(s = e.SelectType || (e.SelectType = {}));
  (function (e) {
    e[e.Start = 1] = "Start";
    e[e.Process = 2] = "Process";
    e[e.HitEnd = 3] = "HitEnd";
    e[e.TimeEnd = 4] = "TimeEnd";
    e[e.Hit = 5] = "Hit";
    e[e.SetActive = 6] = "SetActive";
    e[e.SetCoolingTime = 7] = "SetCoolingTime";
    e[e.OnLoad = 10] = "OnLoad";
    e[e.OnBinding = 11] = "OnBinding";
    e[e.OnBullet = 15] = "OnBullet";
    e[e.OnBehit = 20] = "OnBehit";
    e[e.OnSpawnHurt = 21] = "OnSpawnHurt";
    e[e.OnTreat = 25] = "OnTreat";
    e[e.CodeSet = 99] = "CodeSet";
  })(e.Release || (e.Release = {}));
  (function (e) {
    e[e.Self = 1] = "Self";
    e[e.Current = 2] = "Current";
    e[e.HitEnemy = 3] = "HitEnemy";
    e[e.JoytPos = 8] = "JoytPos";
    e[e.ToParent = 10] = "ToParent";
    e[e.ToBullet = 20] = "ToBullet";
  })(e.SpawningPos || (e.SpawningPos = {}));
  e.SelectSetting = ((i = {})[s.Nearby] = {
    name: "就近目标"
  }, i[s.RandomTarget] = {
    name: "随机目标"
  }, i[s.Self] = {
    name: "自身"
  }, i[s.RandomPos] = {
    name: "随机位置"
  }, i[s.JoytPos] = {
    name: "前方目标"
  }, i);
  e.getSelectVal = function (t) {
    return e.SelectSetting[t];
  };
  (function (e) {
    e[e.ADCouponsConfig = 1001] = "ADCouponsConfig";
  })(e.PopViewType || (e.PopViewType = {}));
  var u = function () {
    function e() {}
    e.prototype.setAttribute = function (e) {
      for (var t in e) {
        this[t] = e[t];
      }
      return this;
    };
    return e;
  }();
  e.CompBase = u;
})(exports.GameSeting || (exports.GameSeting = {}));
var exp_TMap = function () {
  function _ctor(e) {
    this.arrID = 0;
    this._arr = [];
    this.changeID = 0;
    this.getor = function (e, t) {
      return this.get(e) || t;
    };
    this._map = new Map(e);
  }
  Object.defineProperty(_ctor.prototype, "map", {
    get: function () {
      return this._map;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "arr", {
    get: function () {
      if (this.arrID == this.changeID) {
        return this._arr;
      } else {
        return this.getArr();
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getFirst = function () {
    var e = this.map.values().next();
    if (e.done) {
      return undefined;
    } else {
      return e.value;
    }
  };
  _ctor.prototype.set = function (e, t) {
    this.map.set(e, t);
    this.changeID++;
    return this;
  };
  _ctor.prototype.has = function (e) {
    return this.map.has(e);
  };
  _ctor.prototype.get = function (e) {
    return this.map.get(e);
  };
  _ctor.prototype.delete = function (e) {
    var t = this.map.delete(e);
    this.changeID++;
    return t;
  };
  _ctor.prototype.clear = function () {
    this.map.clear();
  };
  _ctor.prototype.getArr = function () {
    var e = this;
    this._arr.length = 0;
    this.forEach(function (t) {
      return e._arr.push(t);
    });
    this.arrID = this.changeID;
    return this._arr;
  };
  _ctor.prototype.add = function (e, t) {
    undefined === t && (t = 1);
    this._map.set(e, this.getor(e, 0) + t);
    this.changeID++;
  };
  _ctor.prototype.forEach = function (e, t) {
    var o = 0;
    this.map.forEach(function (t, i) {
      e(t, o, i);
      o++;
    }, t);
  };
  _ctor.prototype.filter = function (e) {
    var t = [];
    this.map.forEach(function (o) {
      e(o) && t.push(o);
    });
    return t;
  };
  _ctor.prototype.find = function (e) {
    return this.arr.find(e);
  };
  Object.defineProperty(_ctor.prototype, "size", {
    get: function () {
      return this._map.size;
    },
    enumerable: false,
    configurable: true
  });
  return _ctor;
}();
exports.TMap = exp_TMap;