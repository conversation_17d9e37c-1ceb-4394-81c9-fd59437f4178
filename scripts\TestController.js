var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TestController = undefined;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2Game = require("Game");
var $2TestModel = require("TestModel");
var exp_TestController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2TestModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "TestController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Test_OpenView, this.onOpenView, this);
    cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onkeyDown, this);
  };
  _ctor.prototype.onOpenView = function () {
    $2UIManager.UIManager.Open("ui/test/TestView", $2MVC.MVC.openArgs());
  };
  _ctor.prototype.onkeyDown = function (e) {
    var t;
    var o;
    switch (e.keyCode) {
      case cc.macro.KEY.g:
        this.onOpenView();
        break;
      case cc.macro.KEY.p:
        if ($2UIManager.UIManager.getView("ui/common/PauseView")) {
          $2UIManager.UIManager.Close("ui/common/PauseView");
        } else {
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
          $2UIManager.UIManager.Open("ui/common/PauseView", $2MVC.MVC.openArgs());
        }
        break;
      case cc.macro.KEY.f1:
        var i = this.mode.Game.packView.propList.getFirst();
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Fight_Turntable", $2MVC.MVC.openArgs().setParam(i));
        break;
      case cc.macro.KEY["+"]:
        null === (t = this.game) || undefined === t || t.gameCamera.setZoomRatio(this.game.gameCamera.cutZoomRatio + .1, .1);
        break;
      case cc.macro.KEY["-"]:
        null === (o = this.game) || undefined === o || o.gameCamera.setZoomRatio(this.game.gameCamera.cutZoomRatio - .1, .1);
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.TestController = exp_TestController;