var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Buff = require("Buff");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc.v2();
cc.v2();
var def_Weather = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.curWeatherList = [];
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onRoundTickStart, this, $2Notifier.PriorLowest);
    $2Notifier.Notifier.changeCall(t, $2CallID.CallID.Fight_GetWeatherID, this.getWeatherData, this);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.resetConfig();
    this.effctNode = this.node.getORaddChildByName("effctNode").setAttribute({
      y: cc.winSize.height / 2
    });
    this.buffMgr = new $2Buff.Buff.BuffManager(this);
    this.skillMgr = new $2SkillManager.Skill.SkillManager(this);
    this.entityType = $2BaseEntity.EntityType.Neutrality;
    this.campType = $2BaseEntity.CampType.One;
    this.property = new $2PropertyVo.Property.Vo(this, {
      hp: 99999999,
      atk: 2 * this.game.mainRole.property.base.atk,
      atkArea: 1
    });
    this.property.base.Picking = this.property.cut.Picking = 100;
    var t = cc.find("map", this.game._mapNode);
    this.effectMask = new cc.Node("effectMask").addComponent(cc.Sprite);
    this.effectMask.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    this.effectMask.node.setAttribute({
      parent: this.game._topEffectNode,
      width: t.width,
      height: t.height,
      anchorY: 0
    });
    this.effectSke = new cc.Node("effectSke").addComponent(sp.Skeleton);
    this.effectSke.node.setAttribute({
      parent: this.game._topEffectNode,
      width: t.width,
      height: t.height,
      anchorY: 0,
      scale: 1.2,
      position: cc.v2(0, cc.winSize.height / 2)
    });
    this.switchWeather(1);
  };
  Object.defineProperty(_ctor.prototype, "roundNum", {
    get: function () {
      var e;
      return (null === (e = this.game.bronMonsterMgr) || undefined === e ? undefined : e.batchNum) || 1;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getWeatherData = function () {
    return $2Cfg.Cfg.Weather.get(this.curWeather);
  };
  _ctor.prototype.resetConfig = function () {
    var e = $2Manager.Manager.vo.switchVo.weatherWegiht;
    if (this.curWeatherList.length != e.length) {
      this.curWeatherList.length = 0;
      for (var t = 0; t < e.length; t += 2) {
        this.curWeatherList.push({
          id: e[t],
          w: e[t + 1]
        });
      }
    }
  };
  _ctor.prototype.onRoundTickStart = function () {
    if (1 == $2Manager.Manager.vo.switchVo.weather[0]) {
      var e = $2Manager.Manager.vo.switchVo.weather[1] - 1;
      this.roundNum >= e && (this.roundNum - e) % $2Manager.Manager.vo.switchVo.weather[2] == 0 && this.randomWeather();
    }
    this.bmsAtrConfig();
  };
  _ctor.prototype.randomWeather = function () {
    var e = $2GameUtil.GameUtil.weightGetValue(this.curWeatherList);
    if (e.id == this.curWeather) {
      $2GameUtil.GameUtil.deleteArrItem(this.curWeatherList, e);
      this.randomWeather();
    } else {
      this.switchWeather(e.id);
      this.resetConfig();
    }
  };
  _ctor.prototype.switchWeather = function (e) {
    var t;
    var o = this;
    var i = $2Cfg.Cfg.Weather.get(e);
    var n = cc.find("map", this.game._mapNode);
    var r = n.getComponent(cc.Sprite);
    this.effectMask.spriteFrame = null;
    this.effectSke.skeletonData = null;
    this.effctNode.removeAllChildren();
    this.buffMgr.clearBuff();
    this.skillMgr.clearAll();
    i.particle && $2Manager.Manager.loader.loadPrefab(i.particle).then(function (e) {
      e.setParent(o.effctNode);
    });
    $2Manager.Manager.loader.loadSpriteAsync("img/map/" + i.scene, this.game.gameNode).then(function (e) {
      var t = new cc.Node("efNode");
      t.addComponent(cc.Sprite).spriteFrame = e;
      t.setAttribute({
        parent: n,
        zIndex: -1,
        opacity: 0,
        position: cc.Vec2.ZERO,
        anchorY: 0,
        width: n.width,
        height: n.height
      });
      $2Game.Game.tween(t).to(2, {
        opacity: 255
      }).call(function () {
        r.isValid && (r.spriteFrame = e);
        t.destroy();
      }).start();
    });
    i.mask && $2Manager.Manager.loader.loadSpriteAsync("img/map/" + i.mask, this.game.gameNode).then(function (e) {
      $2Game.Game.tween(o.effectMask.node).set({
        opacity: 0
      }).call(function () {
        o.effectMask.spriteFrame = e;
      }).to(1, {
        opacity: 255
      }).start();
    });
    i.sfx && $2Manager.Manager.loader.loadSpine("bones/weather/" + i.sfx, this.game.gameNode).then(function (e) {
      $2Game.Game.tween(o.effectMask.node).set({
        opacity: 0
      }).call(function () {
        o.effectSke.skeletonData = e;
        o.effectSke.setAnimation(0, "more", true);
      }).to(1, {
        opacity: 255
      }).start();
    });
    i.buff.forEach(function (e) {
      return o.addBuff(e);
    });
    null === (t = i.skill) || undefined === t || t.forEach(function (e) {
      return o.addSkill(e);
    });
  };
  _ctor.prototype.bmsAtrConfig = function () {};
  _ctor.prototype.unuse = function () {
    e.prototype.unuse.call(this);
    this.skillMgr.clearAll();
    this.buffMgr.clearBuff();
    this.buffMgr = null;
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2OrganismBase.default);
exports.default = def_Weather;