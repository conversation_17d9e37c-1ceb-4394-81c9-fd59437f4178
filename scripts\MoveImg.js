var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_MoveImg = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.refreshFrequency = 20;
    t._date = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    this._mal = this.node.getComponent(cc.Sprite).getMaterial(0);
  };
  Object.defineProperty(_ctor.prototype, "rolePos", {
    get: function () {
      var e;
      if (null === (e = $2Game.Game.Mgr.instance.mainRole) || undefined === e) {
        return undefined;
      } else {
        return e.position;
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.update = function () {
    if ($2Game.Game.Mgr.instance.gameState == $2Game.Game.State.START) {
      this._date++;
      if (this._date > this.refreshFrequency && this.rolePos) {
        this._date = 0, this.node.position = this.rolePos, this._mal.setProperty("dir", cc.v2(49e-5 * this.rolePos.x, -49e-5 * this.rolePos.y));
      }
    }
  };
  cc__decorate([ccp_property({
    displayName: "刷新帧率"
  })], _ctor.prototype, "refreshFrequency", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_MoveImg;