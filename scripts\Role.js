var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2GameatrCfg = require("GameatrCfg");
var $2SoundCfg = require("SoundCfg");
var $2Notifier = require("Notifier");
var $2StateMachine = require("StateMachine");
var $2Manager = require("Manager");
var $2Game = require("Game");
var $2Buff = require("Buff");
var $2SkillManager = require("SkillManager");
var $2SkillModel = require("SkillModel");
var $2RoleState = require("RoleState");
var $2PropertyVo = require("PropertyVo");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var w = cc.v2();
cc.v2();
var def_Role = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._myData = null;
    t.petList = [];
    t.removeTime = 99999;
    t._isCanRelive = false;
    t.levelCfg = null;
    t.extraExp = 0;
    t.addExpVal = 0;
    t._stateMachine = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
        return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.spine, this.mySkeleton.node).then(function (e) {
          o.mySkeleton.skeletonData = e;
          o.playAction("idle", true);
          var t = o.mySkeleton.node.height;
          o.LifeBar.node.parent.y = t + 20;
          o.skillMgr.launchPoint.set(o.roleNode.position.add(cc.v2(0, t / 2)));
        })));
      }
      var i = this.node.height;
      this.LifeBar.node.parent.y = i + 20;
      this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, i / 2)));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_onRoleMove, this.setJoyStickPos, this);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_onRoleIdle, this.resetJoy, this);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_onChangeRole, this.changeRole, this);
  };
  _ctor.prototype.resetJoy = function () {
    this.isDead || this.toIdle();
  };
  _ctor.prototype.setJoyStickPos = function (e) {
    this.forwardDirection.set(e);
    this.isDead || this.toMove(e);
  };
  Object.defineProperty(_ctor.prototype, "isCanRelive", {
    get: function () {
      return this._isCanRelive;
    },
    set: function (e) {
      this._isCanRelive = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.isCanRelive = true;
    this.buffMgr = new $2Buff.Buff.BuffManager(this);
    this.skillMgr = new $2SkillManager.Skill.SkillManager(this);
    this.entityType = $2BaseEntity.EntityType.Role;
    this.campType = $2BaseEntity.CampType.One;
    this.registerState();
  };
  _ctor.prototype.setRole = function (e) {
    this.level = 1;
    this.levelExp = 0;
    this.myData = $2Cfg.Cfg.RoleUnlock.get(e.heroid);
    var t = $2Game.ModeCfg.Role.filter({
      roleId: e.heroid,
      lv: e.level
    })[0];
    this.skillMgr.add(this.myData.startSkill, true, true);
    this.buffMgr.add(this.myData.startBuff);
    this.extraExp = 0;
    this.levelExp = 0;
    this.toIdle();
    this.property = new $2PropertyVo.Property.Vo(this, t);
    this.updateProperty();
    this.initHp();
  };
  _ctor.prototype.changeRole = function (e, t) {
    undefined === t && (t = true);
    this.property = null;
    var o = $2SkillModel.default.getInstance.cutLevelSkill;
    o.lvupPool.splice(o.lvupPool.indexOf(this.myData.startSkill), 1);
    this.skillMgr.clearByID(this.myData.startSkill, true);
    this.buffMgr.clearBuff(this.buffMgr.get(this.myData.startBuff));
    var i = $2Cfg.Cfg.Role.filter({
      roleId: e,
      lv: this.level
    })[0];
    this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
    this.skillMgr.add(this.myData.startSkill, true, true);
    this.buffMgr.add(this.myData.startBuff);
    o.lvupPool.push(this.myData.startSkill);
    this.property = new $2PropertyVo.Property.Vo(this, i);
    this.property.base.atkArea = this.property.cut.atkArea = 100;
    this.updateProperty();
    t && this.initHp();
  };
  _ctor.prototype.relive = function () {
    this.isDead = false;
    this.initHp();
    this.toIdle();
  };
  _ctor.prototype.updateProperty = function () {
    var t;
    if (this.property) {
      e.prototype.updateProperty.call(this);
      var o = (null === (t = this.buffMgr) || undefined === t ? undefined : t.attrMapAll) || $2OrganismBase.nullMap;
      this.extraExp = o.getor($2GameatrCfg.GameatrDefine.exp, 0);
    }
  };
  _ctor.prototype.unuse = function () {
    e.prototype.unuse.call(this);
    this.level = 1;
  };
  Object.defineProperty(_ctor.prototype, "level", {
    get: function () {
      return this._level;
    },
    set: function (e) {
      var t;
      var o = this._level != e;
      this._level = e;
      this.levelCfg = $2Game.ModeCfg.LevelExp.get(e);
      this._nextLevelExp = (null === (t = $2Game.ModeCfg.LevelExp.get(e + 1)) || undefined === t ? undefined : t.levelUpExp) || 1e16;
      if (o && e > 1) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_LevelUp, e);
        $2Game.Game.Mgr.instance.showEffectByType("entity/fight/effect/Effect_Levelup", cc.Vec2.ZERO, true, -1, {
          parent: this.node
        });
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "levelExpProgress", {
    get: function () {
      return (this.levelExp - this.levelCfg.levelUpExp) / (this._nextLevelExp - this.levelCfg.levelUpExp);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "levelExp", {
    get: function () {
      return this._levelExp;
    },
    set: function (e) {
      this._levelExp = Math.ceil(e);
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ExpUpdate, this.levelExp, this.addExpVal);
      this.levelExp >= this._nextLevelExp && this.level++;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.addExp = function (e) {
    this.addExpVal = e * (1 + this.extraExp);
    this.levelExp += this.addExpVal;
    $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.reward);
  };
  _ctor.prototype.registerState = function () {
    if (!this._stateMachine) {
      this._stateMachine = new $2StateMachine.State.Machine(this);
      this._stateMachine.addState(new $2RoleState.RoleState.IdleState(this));
      this._stateMachine.addState(new $2RoleState.RoleState.MoveState(this));
      this._stateMachine.addState(new $2RoleState.RoleState.DeadState(this, false));
      this._stateMachine.registerGlobalState(new $2RoleState.RoleState.AttackState(this));
    }
  };
  _ctor.prototype.toIdle = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.IDLE) || this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.IDLE);
  };
  _ctor.prototype.toDead = function () {
    this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
  };
  _ctor.prototype.toMove = function (e) {
    cc.Vec2.multiplyScalar(w, e, this.maxSpeed);
    this.velocity = w;
    if (this.velocity.magSqr() > 1e-5) {
      cc.Vec2.normalize(w, this.velocity);
      this.heading = w;
    }
    this._stateMachine.isInState($2StateMachine.State.Type.MOVE) || this.isDead || this._stateMachine.changeState($2StateMachine.State.Type.MOVE);
  };
  _ctor.prototype.behit = function (e) {
    if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
      this.curHp -= e.val;
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      this.materialTwinkle();
      this.curHp <= 0 && this.toDead();
      wonderSdk.vibrate(0);
      return e;
    }
  };
  _ctor.prototype.materialTwinkle = function () {};
  return cc__decorate([ccp_ccclass], _ctor);
}($2OrganismBase.default);
exports.default = def_Role;