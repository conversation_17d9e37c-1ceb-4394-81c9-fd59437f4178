var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
require("Manager");
require("SdkConfig");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_ExSprite = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.start = function () {};
  _ctor.prototype.onLoad = function () {};
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Sprite);
exports.default = def_ExSprite;