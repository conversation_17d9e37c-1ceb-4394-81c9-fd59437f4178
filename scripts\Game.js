var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ModeCfg = exports.Game = undefined;
var $2CallID = require("CallID");
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var $2BaseEntity = require("BaseEntity");
var $2GameEffect = require("GameEffect");
var $2Goods = require("Goods");
var $2GameCamera = require("GameCamera");
var $2CompManager = require("CompManager");
var $2NodePool = require("NodePool");
var $2BronMonsterManger = require("BronMonsterManger");
var $2FColliderManager = require("FColliderManager");
var $2PropertyVo = require("PropertyVo");
var $2Pet = require("Pet");
var $2MVC = require("MVC");
var $2LatticeMap = require("LatticeMap");
var $2LifeBar = require("LifeBar");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2BulletBase = require("BulletBase");
var $2GameSeting = require("GameSeting");
var $2GameSkeleton = require("GameSkeleton");
var $2Time = require("Time");
var $2LifeLabel = require("LifeLabel");
(function (e) {
  var t;
  var o;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.START = 1] = "START";
    e[e.PAUSE = 2] = "PAUSE";
  })(e.State || (e.State = {}));
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.ROUGUELIKE = 1] = "ROUGUELIKE";
    e[e.TOWER = 2] = "TOWER";
    e[e.CATGAME = 3] = "CATGAME";
    e[e.HIDEGAME = 4] = "HIDEGAME";
    e[e.MOYUTOWER = 5] = "MOYUTOWER";
    e[e.BACKPACKHERO = 20] = "BACKPACKHERO";
    e[e.THROWINGKNIFE = 30] = "THROWINGKNIFE";
    e[e.PICKUPBULLETS = 31] = "PICKUPBULLETS";
    e[e.BULLETSREBOUND = 32] = "BULLETSREBOUND";
    e[e.CHAINS = 33] = "CHAINS";
    e[e.TIDEDEFEND = 34] = "TIDEDEFEND";
    e[e.MANGUARDS = 35] = "MANGUARDS";
    e[e.ALLOUTATTACK = 36] = "ALLOUTATTACK";
    e[e.DRAGONWAR = 37] = "DRAGONWAR";
  })(o = e.Mode || (e.Mode = {}));
  e.ModeMouth = ((t = {})[o.NONE] = {
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.BACKPACKHERO] = {
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.THROWINGKNIFE] = {
    name: "飞刀模式",
    icon: "img/ModeTKnife/icon_sjxyx",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.PICKUPBULLETS] = {
    name: "让子弹飞",
    icon: "img/ModePickupBullet/icon_zbzd",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.BULLETSREBOUND] = {
    name: "弹来弹去",
    icon: "img/ModeBulletsRebound/icon_tltq",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.CHAINS] = {
    name: "末日屠龙",
    icon: "v1/images/fight/icon_tcl",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.TIDEDEFEND] = {
    name: "尸潮防线",
    icon: "img/ModeTideDefend/icon_mtwf",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.MANGUARDS] = {
    name: "一弹射穿",
    icon: "img/ModeManGuards/icon_ydsc",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.ALLOUTATTACK] = {
    name: "全军出击",
    icon: "img/ModeAllOutAttack/icon_qjcj",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t[o.DRAGONWAR] = {
    name: "龙神战争",
    icon: "img/ModeAllOutAttack/icon_qjcj",
    mouth: $2ListenID.ListenID.Game_Load,
    get data() {
      return $2MVC.MVC.openArgs();
    }
  }, t);
  e.getMouth = function (t) {
    var i = e.ModeMouth[t] || e.ModeMouth[o.NONE];
    i.type = t;
    return i;
  };
  var T = cc.Vec2.ZERO;
  var A = cc.Vec2.ZERO;
  cc.v3();
  var R = function () {
    function t() {
      this.canSkill = true;
      this.timeDelay = new $2Time.TimeDelay();
      this._gameState = e.State.NONE;
      this.offsetX = 0;
      this.offsetY = 0;
      this.gameSpeed = 1;
      this._mainRole = null;
      this._gameCamera = null;
      this.cameraZoomRatio = .7;
      this.gameNode = null;
      this._finishCall = null;
      this.chainsList = [];
      this._gameTime = 0;
      this.killMonsterNum = 0;
      this._entityNode = null;
      this._mapNode = null;
      this._bulletNode = null;
      this._botEffectNode = null;
      this._topEffectNode = null;
      this.topUINode = null;
      this.behitUI = null;
      this.LifeBarUI = null;
      this._spawnMonsterList = [];
      this._pauseCount = 0;
      this._countSort = 5;
      this._MonsterOnScreenList = [];
      this._cameraSize = $2GameUtil.GameUtil.getDesignSize;
      this._waitToDestroyList = [];
      this.amClip = new Map();
      this.GameSpeedSetting = {
        1: $2Manager.Manager.vo.switchVo.gameSpeed || 1,
        2: 1.8,
        4: 2.3
      };
      this._pauseCount = 0;
      this.gameTime = 0;
      this.killMonsterNum = 0;
      if (!this._gameCamera) {
        var o = cc.Camera.cameras.find(function (e) {
          return "GameCamera" == e.node.name;
        });
        this._gameCamera = new $2GameCamera.GameCamera(o, null, this);
      }
      this._monsterMap = new $2GameSeting.TMap();
      this._elementMap = new $2GameSeting.TMap();
      this.bulletList = new Set();
      this.LatticeElementMap = new $2LatticeMap.LatticeMap.Mgr({
        list: this.elementMap
      });
      this.changeListener(true);
      t.instance = this;
      $2FColliderManager.default.instance.enable = true;
      cc.Tween.resumeByTag($2GameSeting.GameSeting.TweenType.Game);
    }
    Object.defineProperty(t, "instance", {
      get: function () {
        return t._instance;
      },
      set: function (o) {
        t._instance = o;
        e.mgr = o;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "mode", {
      get: function () {
        return null;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "gameState", {
      get: function () {
        return this._gameState;
      },
      set: function (e) {
        this._gameState = e;
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OnGameState, this._gameState);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "isChallenge", {
      get: function () {
        return false;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "curActivity", {
      get: function () {
        return null;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "adcoupons", {
      get: function () {
        var e;
        return $2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_in) + ((null === (e = this.knapsackMgr) || undefined === e ? undefined : e.has($2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_in)) || 0);
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.changeListener = function (e) {
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_SetPause, this.gamePause, this);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_End, this.gameEnd, this);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_ShowEffect, this.showEffectByType, this);
      $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Fight_GetMainRole, this.getMainRole, this);
      $2Notifier.Notifier.changeCall(e, $2CallID.CallID.Fight_GetCutGameData, this.getCutGameData, this);
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_ReliveSuccess, this.reliveSuccess, this);
    };
    t.prototype.showEffectByType = function (e, t, o, i, n) {
      var r = this;
      undefined === o && (o = true);
      undefined === i && (i = .3);
      undefined === n && (n = {});
      return new Promise(function (a) {
        $2NodePool.NodePool.spawn(e).setNodeAssetFinishCall(function (e) {
          var s = e.getComponent($2GameEffect.default);
          n.parent || (n.parent = r.topUINode);
          n.scale || (n.scale = 1);
          n.active = o;
          n.position || (n.position = cc.v2(t.x, t.y));
          n.opacity || (n.opacity = 255);
          e.setAttribute(n);
          i < 0 && (s._isNotDead = true);
          s.init();
          s.deadTime = i;
          $2CompManager.default.Instance.registerComp(s);
          a(s);
        });
      });
    };
    t.prototype.showEffectByPath = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        $2NodePool.NodePool.spawn("entity/fight/effect/Effect_Default").setNodeAssetFinishCall(function (n) {
          var r = n.getComponent($2GameSkeleton.default);
          $2Manager.Manager.loader.loadSpine(e, o.gameNode).then(function (e) {
            var a;
            var s;
            var c;
            var l;
            var u;
            var p;
            var f;
            var h;
            r.reset(e);
            (a = t.nodeAttr).parent || (a.parent = o._topEffectNode);
            (s = t.nodeAttr).group || (s.group = "Game");
            (c = t.nodeAttr).scale || (c.scale = 1);
            (l = t.nodeAttr).opacity || (l.opacity = 255);
            if (t.nodeAttr.parent.isValid) {
              (u = t.spAttr).animation || (u.animation = "animation");
              (p = t.spAttr).isPlayerOnLoad || (p.isPlayerOnLoad = true);
              (f = t.spAttr).type || (f.type = $2GameSeting.GameSeting.TweenType.Game);
              (h = t.spAttr).loop || (h.loop = false);
              r.setAttribute(t.spAttr);
              n.setAttribute(t.nodeAttr);
              t.delayRemove && cc.tween(n).delay(t.delayRemove).to(.2, {
                opacity: 0
              }).call(function () {
                n.isValid && $2NodePool.NodePool.despawn(n.nodeItem);
              }).start();
              i(r);
            } else {
              $2NodePool.NodePool.despawn(n.nodeItem);
            }
          });
        });
      });
    };
    t.prototype.showItemTips = function (t, o, i) {
      var n = this;
      undefined === t && (t = $2CurrencyConfigCfg.CurrencyConfigDefine.Mana);
      undefined === o && (o = 1);
      var a = $2Cfg.Cfg.CurrencyConfig.get(t);
      cc.log(a);
      $2NodePool.NodePool.spawn("ui/common/itemTips").setNodeAssetFinishCall(function (t) {
        t.setAttribute({
          parent: n.topUINode,
          position: i,
          scale: 0,
          opacity: 255
        });
        $2Manager.Manager.loader.loadSpriteToSprit(a.icon, t.getComByPath(cc.Sprite, "bg/icon"));
        t.getComByPath(cc.Label, "bg/label").string = "+" + o;
        e.tween(t).by(.2, {
          scale: 1,
          y: 20
        }).to(.1, {
          scale: 1.3
        }).to(.1, {
          scale: 1
        }).delay(.6).by(.2, {
          y: 20,
          opacity: -255
        }).call(function () {
          $2NodePool.NodePool.despawn(t.nodeItem);
        }).start();
      });
    };
    t.prototype.showEntityDieEffect = function (e, t) {
      var o = this;
      undefined === e && (e = .5);
      return new Promise(function (n) {
        0 != $2Manager.Manager.vo.switchVo.deadAnim && $2NodePool.NodePool.spawn("entity/fight/effect/Effect_Die").setNodeAssetFinishCall(function (r) {
          var a = r.getComponent($2GameEffect.default);
          r.setAttribute(cc__assign({
            parent: o._entityNode,
            active: true
          }, t));
          a.init();
          a.deadTime = e;
          var s = a.getComponent(sp.Skeleton);
          s && s.setAnimation(0, s.defaultAnimation, false);
          $2CompManager.default.Instance.registerComp(a);
          n(a);
        });
      });
    };
    t.prototype.showSkillHerald = function (t, o, i, n) {
      var r = this;
      undefined === i && (i = 100);
      undefined === n && (n = .3);
      var a = $2NodePool.NodePool.spawn(t);
      i += 20;
      a.setNodeAssetFinishCall(function (t) {
        var a = t.getComponent($2GameEffect.default);
        t.setAttribute({
          parent: r._botEffectNode,
          active: true,
          scale: 1,
          opacity: 0,
          x: o.x,
          y: o.y,
          width: i,
          height: .6 * i
        });
        a.init();
        a.deadTime = n;
        e.tween(t).to(.2, {
          opacity: 255
        }).delay(n - .5).to(.3, {
          opacity: 0
        }).start();
        $2CompManager.default.Instance.registerComp(a);
      });
    };
    Object.defineProperty(t.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
        this.gameCamera.setTargetNode(e);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "rolePos", {
      get: function () {
        return this.mainRole.node.getChildByName("role");
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.getRoleGap = function (e) {
      return $2GameUtil.GameUtil.getDistance(e, this.mainRole.position);
    };
    t.prototype.isVaild = function (e) {
      return $2GameUtil.GameUtil.getDistance(e, this.mainRole.position) < 3e3;
    };
    Object.defineProperty(t.prototype, "gameCamera", {
      get: function () {
        return this._gameCamera;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "monsterMap", {
      get: function () {
        return this._monsterMap;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "elementMap", {
      get: function () {
        return this._elementMap;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "gameTime", {
      get: function () {
        return this._gameTime;
      },
      set: function (e) {
        this._gameTime = e;
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_UpdateGameTime, this._gameTime);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "botEffectNode", {
      get: function () {
        return this._botEffectNode;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.spawnMonsterByFrame = function (e, t, o) {
      this._spawnMonsterList.push({
        monId: e,
        pos: t,
        param: o
      });
    };
    t.prototype.spawnBullet = function (e, t, o) {
      var i = this;
      undefined === o && (o = {});
      return new Promise(function (n) {
        var r = t;
        $2NodePool.NodePool.spawn(e).setNodeAssetFinishCall(function (e) {
          var t;
          var a = e.getComponent($2BulletBase.default);
          cc.Tween.stopAllByTarget(e);
          o.parent || (o.parent = r.belongSkill.showTier || i._bulletNode);
          o.opacity || (o.opacity = 255);
          o.position || (o.position = r.startPos);
          o.scale || (o.scale = r.scale);
          a.setBulletVo(r);
          e.setAttribute(o);
          a.init();
          null === (t = a.collider) || undefined === t || t.setActive(o.active);
          i.bulletList.add(a);
          null != o.opacity && (e.opacity = o.opacity);
          n(a);
          $2CompManager.default.Instance.registerComp(a);
          r.belongSkill.checkSubSkill($2GameSeting.GameSeting.Release.OnBullet, {
            pos: a.position,
            start: a
          });
          r.belongSkill.excuteBuffToEnemy(a, $2GameSeting.GameSeting.Release.OnBullet);
        });
      });
    };
    t.prototype.createLifeBar = function (e, t) {
      var o = this;
      undefined === t && (t = {});
      return new Promise(function (i) {
        cc.log("创建生命条");
        $2NodePool.NodePool.spawn("ui/fight/LifeBar").setNodeAssetFinishCall(function (n) {
          var r = n.getComponent($2LifeBar.default);
          t.parent || (t.parent = o.LifeBarUI);
          r.set(e);
          n.setAttribute(t);
          i(r);
        });
      });
    };
    t.prototype.createLifeLabel = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        $2NodePool.NodePool.spawn("ui/fight/LifeLabel").setNodeAssetFinishCall(function (n) {
          var r = n.getComponent($2LifeLabel.default);
          n.setParent(o.LifeBarUI);
          r.set(e);
          t && n.setAttribute(t);
          i(r);
        });
      });
    };
    t.prototype.createPet = function (e, t, o) {
      var i = this;
      undefined === e && (e = 1);
      return new Promise(function (n) {
        i.mainRole && $2NodePool.NodePool.spawn("entity/fight/Pet").setNodeAssetFinishCall(function (r) {
          var a;
          var s;
          if (t) {
            var c = r.getComponent($2Pet.default);
            c && c.constructor.name != t.constructor.name && c.destroy();
          } else {
            t || (t = $2Pet.default);
          }
          var l = r.getORaddComponent(t);
          (a = o.nodeAttr).parent || (a.parent = i._entityNode);
          (s = o.nodeAttr).position || (s.position = cc.v2(0, 0));
          r.setAttribute(o.nodeAttr);
          l.init();
          l.setPet(e, i.mainRole);
          i.elementMap.set(l.ID, l);
          $2CompManager.default.Instance.registerComp(l);
          n(l);
        });
      });
    };
    t.prototype._createBronMonster = function () {
      this.bronMonsterMgr = this.gameNode.getORaddComponent($2BronMonsterManger.BronMonsterManger);
      this.bronMonsterMgr.init();
    };
    t.prototype.createGoods = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        var n = t.clone();
        $2NodePool.NodePool.spawn("entity/fight/Goods").setNodeAssetFinishCall(function (t) {
          var r = t.getComponent($2Goods.default);
          r.data = e;
          t.parent = o._topEffectNode;
          r.setPosition(n);
          r.init();
          $2CompManager.default.Instance.registerComp(r);
          i(r);
        });
      });
    };
    Object.defineProperty(t.prototype, "colliderWorld", {
      get: function () {
        return $2FColliderManager.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.getTarget = function (e) {
      e.target || (e.target = this.mainRole);
      e.pos || (e.pos = e.target.position.clone());
      e.radius || (e.radius = 350);
      e.maxNum || (e.maxNum = 10);
      e.ignoreID || (e.ignoreID = []);
      e.targetCamp || (e.targetCamp = [e.target.atkCamp]);
      e.minRadius || (e.minRadius = 30);
      e.target.entityType != $2BaseEntity.EntityType.Monster && (e.target = this.mainRole);
      if (e.target.entityType == $2BaseEntity.EntityType.Monster) {
        return [this.mainRole];
      }
      var t = this.LatticeElementMap.seekByPos(e);
      if (t.length > 0) {
        for (; t.length < e.maxNum;) {
          t.push($2GameUtil.GameUtil.getRandomInArray(t)[0]);
        }
      }
      return t;
    };
    t.prototype.gamePause = function (t) {
      if (t) {
        this.gameState = e.State.PAUSE;
        cc.Tween.pauseByTag($2GameSeting.GameSeting.TweenType.Game);
        this.pauseAllActions(true);
        $2FColliderManager.default.instance.enable = false;
        this.bronMonsterMgr.enabled = false;
      } else if (this.gameState == e.State.PAUSE) {
        this.gameState = e.State.START, cc.Tween.resumeByTag($2GameSeting.GameSeting.TweenType.Game), this.pauseAllActions(false), $2FColliderManager.default.instance.enable = true, this.bronMonsterMgr.enabled = true;
      }
    };
    t.prototype.pauseAllActions = function (e) {
      for (var t = 0; t < this._entityNode.childrenCount; t++) {
        if (e) {
          this._entityNode.children[t].pauseAllActions();
        } else {
          this._entityNode.children[t].resumeAllActions();
        }
      }
    };
    t.prototype.reliveSuccess = function () {};
    t.prototype.gameEnd = function () {
      this._gameState = e.State.NONE;
    };
    t.prototype.getMainRole = function () {
      return this._mainRole;
    };
    t.prototype.showDamageDisplay = function (e, t, o) {
      undefined === o && (o = cc.Color.WHITE);
      e.type != $2PropertyVo.Hurt.Type.Slash && 0 != e.val && $2AlertManager.AlertManager.showHurtTips(e.val, {
        parent: this.topUINode,
        position: t,
        color: o
      }, e.isCrit);
    };
    t.prototype.showNumTips = function (e, t, o) {
      undefined === o && (o = cc.Color.WHITE);
      cc.Vec2.set(A, t.x, t.y + 20);
      $2AlertManager.AlertManager.showHurtTips(e, {
        parent: this._topEffectNode,
        position: A,
        color: o
      });
    };
    t.prototype.onUpdate = function (t) {
      var o;
      var i;
      var n;
      if (this._waitToDestroyList.length > 0) {
        for (var r = this._waitToDestroyList.length - 1; r >= 0; r--) {
          this.removeEntity(this._waitToDestroyList[r]);
        }
        this._waitToDestroyList.length = 0;
      }
      if (this._gameState == e.State.START) {
        null === (o = this._gameCamera) || undefined === o || o.lateUpdate(t);
        $2CompManager.default.Instance.onUpdate(t);
        this.checkMonsterOuDate(t);
        this.LatticeElementMap.onUpdate(t);
        if (this._entityNode && ! --this._countSort) {
          this._countSort = 5;
          var a = this._entityNode._children;
          $2GameUtil.GameUtil.sort(a, function (e, t) {
            return t.y > e.y;
          }) && this._entityNode._delaySort();
        }
        null === (i = this.bronMonsterMgr) || undefined === i || i.onUpdate(t);
        this.timeDelay.onUpdate(t);
        null === (n = this.uiView) || undefined === n || n.onUpdate(t);
      }
      this.gameTime += t;
    };
    t.prototype.checkMonsterOuDate = function (e) {
      var t = this;
      this._MonsterOnScreenList.length = 0;
      var o = this._cameraSize.width / this.gameCamera.cutZoomRatio * .5;
      var i = this._cameraSize.height / this.gameCamera.cutZoomRatio * .5;
      this.monsterMap.forEach(function (n) {
        T = n.node.position.sub(t.gameCamera.position);
        Math.abs(T.x) < o && Math.abs(T.y) < i && t._MonsterOnScreenList.push(n);
        n.onUpdate(e);
      });
    };
    t.prototype.removeEntity = function (e) {
      var t;
      var o;
      if (e.entityType == $2BaseEntity.EntityType.Monster) {
        this._monsterMap.delete(e.ID);
        e.isValid && $2NodePool.NodePool.despawn(null === (t = e.node) || undefined === t ? undefined : t.nodeItem);
      } else {
        $2CompManager.default.Instance.removeComp(e);
        e.entityType == $2BaseEntity.EntityType.Bullet && this.bulletList.delete(e);
        e.isValid && $2NodePool.NodePool.despawn(null === (o = e.node) || undefined === o ? undefined : o.nodeItem);
      }
      this.elementMap.delete(e.ID);
    };
    t.prototype.clearAllMonster = function () {
      this._monsterMap.forEach(function (e) {
        e.removeEntityToUpdate();
      });
    };
    t.prototype.clearAllBullet = function () {
      $2CompManager.default.Instance.compMap.forEach(function (e) {
        e.entityType == $2BaseEntity.EntityType.Bullet && e.removeEntityToUpdate();
      });
    };
    Object.defineProperty(t.prototype, "waitToDestroyList", {
      get: function () {
        return this._waitToDestroyList;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.pushToDestroyList = function (e) {
      this.waitToDestroyList.includes(e) || this._waitToDestroyList.push(e);
    };
    t.prototype.destroy = function () {
      e.setGameSpeed(1);
      cc.Tween.stopAllByTag($2GameSeting.GameSeting.TweenType.Game);
      this.changeListener(false);
      if (this._mainRole) {
        if (this._mainRole.node) {
          $2NodePool.NodePool.despawn(this._mainRole.node.nodeItem);
          this._mainRole = null;
        } else {
          console.warn("[mainRole]丢失");
        }
      }
      $2CompManager.default.Instance.compMap.forEach(function (e, t) {
        if (!e.node) {
          return console.warn("[compMapVal]丢失", e, t);
        }
        $2NodePool.NodePool.despawn(e.node.nodeItem);
      });
      this.bronMonsterMgr && (this.bronMonsterMgr = null);
      $2CompManager.default.Instance.clear();
      this._waitToDestroyList.length = 0;
      this.amClip.forEach(function (e) {
        return e.decRef();
      });
      this.amClip.clear();
      $2FColliderManager.default.instance.enable = false;
      $2FColliderManager.default.instance.clearMgr();
      this.timeDelay.destroy();
      this.timeDelay = null;
      e.mgr = null;
      $2PropertyVo.Hurt.Pool.clear();
      e.Mgr.instance = null;
    };
    t.prototype.getDroppedItems = function (e) {
      var t = e.map(function (e) {
        return {
          id: e[0],
          num: e[1],
          w: e[2],
          param: e[3]
        };
      });
      return $2GameUtil.GameUtil.weightGetList(t, 1).map(function (e) {
        var t = $2Cfg.Cfg.CurrencyConfig.get(e.id);
        return {
          id: e.id,
          num: e.num,
          param: e.param,
          type: null == t ? undefined : t.type,
          rarity: null == t ? undefined : t.rarity
        };
      });
    };
    t.prototype.getAmClip = function (e) {
      var t = this;
      return new Promise(function (o) {
        if (t.amClip.has(e.assetName)) {
          return o(t.amClip.get(e.assetName));
        }
        e.path && $2Manager.Manager.loader.loadSpriteFrameList(e.path, t.gameNode).then(function (i) {
          var n = $2GameUtil.CCTool.UI.CreateAnimationClip(e.amName, i, e.frame_time || 20);
          t.amClip.set(e.assetName, n);
          o(n);
        });
      });
    };
    t.prototype.getCutGameData = function () {
      var e = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
      return {
        Mode: e,
        AdNums: $2Manager.Manager.AD.data.getor(e, 0),
        FightID: $2Notifier.Notifier.call($2CallID.CallID.Fight_GetFightID) || 0,
        Lv: this.bronMonsterMgr.level,
        Batch: this.bronMonsterMgr.batchNum
      };
    };
    t.prototype.sendEvent = function (e, t) {
      undefined === t && (t = {});
      $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Game", cc__assign(cc__assign({
        Scene: e
      }, this.getCutGameData()), t));
    };
    t.prototype.getShowTier = function (e) {
      if (1 == e) {
        return this._botEffectNode;
      } else {
        if (2 == e) {
          return this._entityNode;
        } else {
          return this._bulletNode;
        }
      }
    };
    t._instance = null;
    return t;
  }();
  e.Mgr = R;
  e.setGameSpeed = function (t) {
    var o;
    undefined === t && (t = 1);
    var i = (null === (o = e.mgr) || undefined === o ? undefined : o.GameSpeedSetting[t]) || 1;
    if (e.mgr) {
      if (e.mgr.gameSpeed == i) {
        return;
      }
      e.mgr.gameSpeed = i;
    }
    sp.timeScale = i;
  };
  e.touchToGamePos = function (t) {
    T.set(t);
    T.addSelf(cc.v2(-cc.winSize.width / 2, -cc.winSize.height / 2)).mulSelf(e.mgr.gameCamera.cutZoomRatio).addSelf(e.mgr.gameCamera.position);
    return T;
  };
  e.tween = function (e) {
    return cc.tween(e).tag($2GameSeting.GameSeting.TweenType.Game);
  };
  e.warnTween = function (e) {
    e.setAttribute({
      opacity: 0
    });
    cc.tween(e).tag($2GameSeting.GameSeting.TweenType.Game).to(.3, {
      opacity: 255
    }).to(.3, {
      opacity: 0
    }).union().repeat(3).start();
  };
  e.timerOnce = function (t, o, i) {
    undefined === o && (o = 0);
    return e.mgr.timeDelay.delay(o, t, null, i);
  };
  e.timer = function (t, o, i, n) {
    undefined === i && (i = 0);
    return e.mgr.timeDelay.delay(o, t, null, n, i);
  };
  e.sendEvent = function (t, o) {
    undefined === o && (o = {});
    e.mgr.sendEvent(t, o);
  };
  e.checkProperty = function (e, t) {
    var o = {};
    for (var i in e) {
      if (t[i] && "number" == typeof e[i]) {
        var n = +t[i] - +e[i];
        (n > 0 || n < 0) && (o[i] = n);
      }
    }
    return o;
  };
  var B = {
    atk: "攻击力",
    hp: "血量",
    atkArea: "攻击范围",
    crit: "暴击率",
    speed: "移动速度"
  };
  var L = ["crit"];
  e.transformDesc = function (e, t) {
    var o = "";
    var i = 0;
    for (var n in e) {
      if (t.includes(n)) {
        var r = e[n].toFixed(3);
        r = L.includes(n) ? Math.round(100 * r) + "%" : r = Math.round(r);
        o += (0 == i ? "" : ", ") + cc.js.formatStr((B[n] || n) + (e[n] > 0 ? "+" : "") + "%s", r + "");
        i++;
      }
    }
    return o;
  };
  e.random = function (e, t) {
    return $2Manager.Manager.random.randomDouble(e, t);
  };
  e.randomInt = function (e, t) {
    return $2Manager.Manager.random.randomInt(e, t);
  };
  e.weight = function (e) {
    return $2Manager.Manager.random.randomInt(0, 100) < e;
  };
  e.weightFloat = function (e) {
    return $2Manager.Manager.random.randomDouble(0, 1) < e;
  };
  e.getCutMode = function () {
    return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
  };
})(exports.Game || (exports.Game = {}));
var exp_ModeCfg = function () {
  function _ctor() {}
  Object.defineProperty(_ctor, "cutMode", {
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "Buff", {
    get: function () {
      var e;
      return (null === (e = this._modeConfig[this.cutMode]) || undefined === e ? undefined : e.Buff) || $2Cfg.Cfg.Buff;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "Skill", {
    get: function () {
      var e;
      return (null === (e = this._modeConfig[this.cutMode]) || undefined === e ? undefined : e.Skill) || $2Cfg.Cfg.BagSkill;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "MonsterLv", {
    get: function () {
      var e;
      return (null === (e = this._modeConfig[this.cutMode]) || undefined === e ? undefined : e.MonsterLv) || $2Cfg.Cfg.bagMonsterLv;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "LevelExp", {
    get: function () {
      var e;
      return (null === (e = this._modeConfig[this.cutMode]) || undefined === e ? undefined : e.LevelExp) || $2Cfg.Cfg.LevelExp;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "Skiilpool", {
    get: function () {
      var e;
      return (null === (e = this._modeConfig[this.cutMode]) || undefined === e ? undefined : e.Skiilpool) || $2Cfg.Cfg.Skiilpool;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor, "Role", {
    get: function () {
      var e;
      return (null === (e = this._modeConfig[this.cutMode]) || undefined === e ? undefined : e.Role) || $2Cfg.Cfg.Role;
    },
    enumerable: false,
    configurable: true
  });
  _ctor._modeConfig = {};
  return _ctor;
}();
exports.ModeCfg = exp_ModeCfg;