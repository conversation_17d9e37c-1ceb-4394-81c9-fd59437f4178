var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2MVC = require("MVC");
var $2Game = require("Game");
var def_FightModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.cutMode = $2Game.Game.Mode.NONE;
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "getInstance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {};
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_FightModel;