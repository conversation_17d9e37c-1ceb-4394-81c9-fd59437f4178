var i;
var cc__extends = __extends;
var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Buff = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2GameatrCfg = require("GameatrCfg");
var $2Notifier = require("Notifier");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2BaseEntity = require("BaseEntity");
(function (e) {
  var t = {};
  e.MoveAtr = [$2GameatrCfg.GameatrDefine.movespeed, $2GameatrCfg.GameatrDefine.freeze];
  var o;
  var i = ["Buff_EntityDead", "Buff_Hurt", "Buff_ResistDamage", "Buff_SetSlash"];
  e.FreezeIDList = function () {
    return o || (o = [], $2Game.ModeCfg.Buff.forEach(function (e) {
      2 == e.type && e.attr.includes($2GameatrCfg.GameatrDefine.freeze) && o.push(e.id);
    }), o);
  };
  e.cacheBuff = function (e) {
    return function () {
      var o = cc._RF.peek();
      var i = o && o.script;
      t[e] = i;
    };
  };
  var d = function () {
    function e() {
      this.attrMap = new $2GameSeting.TMap();
      this.specialMap = [];
      this._curBuffTime = 0;
      this.isForever = false;
      this._isActive = true;
      this.owner = null;
      this.caster = null;
      this._buffLayer = 0;
      this.tempData = {};
      this.isEnemyAddDebuff = false;
      this.cutEffect = [];
      this.cutEffectPath = [];
    }
    Object.defineProperty(e.prototype, "buffCfg", {
      get: function () {
        return this._cutVo;
      },
      set: function (e) {
        var t;
        this.ID = e.id;
        this._buffCfg = cc__assign({}, e);
        this._buffCfg.weight = "number" == typeof this._buffCfg.weight ? this._buffCfg.weight : 1;
        (t = this._buffCfg).isOverlay || (t.isOverlay = 1);
        this._cutVo = JSON.parse(JSON.stringify(this._buffCfg));
        this.checkGain();
        this.curBuffTime = this.isForever ? 999 : this._cutVo.time;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "cutVo", {
      get: function () {
        return this._cutVo;
      },
      set: function (e) {
        this._cutVo = e;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "otherValue", {
      get: function () {
        return this.cutVo.otherValue;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.checkGain = function (e, t, o) {
      var i;
      undefined === e && (e = this._cutVo);
      undefined === t && (t = this._buffCfg);
      undefined === o && (o = this.relatedBuffAttr);
      e.weight = t.weight + o.getor($2GameatrCfg.GameatrDefine.buffWeight, 0);
      e.isOverlay = t.isOverlay + o.getor($2GameatrCfg.GameatrDefine.buffOverlay, 0);
      var n = o.getor($2GameatrCfg.GameatrDefine.otherValueAdd, 0);
      null === (i = t.otherValue) || undefined === i || i.forEach(function (t, o) {
        e.otherValue[o] = t * (1 + n);
      });
      e.value.forEach(function (e, i) {
        -1 != t.attr[i][0] && (e[0] = t.value[i][0] * (1 + o.getor($2GameatrCfg.GameatrDefine.buffEffectAdd, 0)));
      });
      -1 != t.time && (e.time = t.time + o.getor($2GameatrCfg.GameatrDefine.buffDur, 0));
      this.isSpecificSkill = e.skillId;
      this.isForever || (this.isForever = -1 == e.time);
    };
    Object.defineProperty(e.prototype, "curBuffTime", {
      get: function () {
        return this._curBuffTime;
      },
      set: function (e) {
        this._curBuffTime = e;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "isActive", {
      get: function () {
        return this._isActive && this.buffLayer > 0;
      },
      set: function (e) {
        this._isActive = e;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "buffLayer", {
      get: function () {
        return this._buffLayer;
      },
      set: function (e) {
        var t = this;
        this._buffLayer = e;
        this.attrMap.clear();
        this.specialMap.length = 0;
        this.cutVo.attr.forEach(function (e, o) {
          if (-1 != e && t.isActive) {
            var i = t.cutVo.value[o];
            if (0 == $2Cfg.Cfg.Gameatr.get(e).isOverLay) {
              t.attrMap.set(e, i[0]);
              t.specialMap.push({
                type: e,
                data: i
              });
            } else {
              var n = i[0] * t.buffLayer;
              t.attrMap.set(e, n);
            }
          }
        });
        this.curBuffTime = this.isForever ? 999 : this._cutVo.time;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "isMaxLayer", {
      get: function () {
        return !this.cutVo.isOverlay || this.buffLayer >= this.cutVo.isOverlay;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "mgr", {
      get: function () {
        return this.owner.buffMgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "isWeight", {
      get: function () {
        return $2Game.Game.weightFloat(this.cutVo.weight);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "relatedBuff", {
      get: function () {
        return this.mgr.getSpecificBuff({
          buffID: this.cutVo.id
        });
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "relatedBuffAttr", {
      get: function () {
        return this.mgr.getSpecificBuffAttr({
          buffID: this.cutVo.id
        });
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "saveData", {
      get: function () {
        return {
          ID: this.ID,
          cfg: this._buffCfg,
          buffLayer: this.buffLayer,
          curBuffTime: this.curBuffTime,
          tempData: this.tempData
        };
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "listener", {
      get: function () {
        if (2 == this.cutVo.listenObject) {
          return this.owner.parent;
        } else {
          return this.owner;
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "targetCamp", {
      get: function () {
        if (2 == this.cutVo.object) {
          return [this.owner.campType];
        } else {
          if (1 == this.cutVo.object) {
            return [this.owner.atkCamp];
          } else {
            return [this.owner.campType, this.owner.atkCamp];
          }
        }
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setLayer = function (e) {
      var t;
      var o = this;
      this.buffLayer = e;
      null === (t = this.mgr) || undefined === t || t.onBuffChanges(this.cutVo);
      this.cutEffect.forEach(function (e) {
        e.setActive(o.buffLayer > 0);
      });
    };
    e.prototype.setCaster = function (e) {
      this.caster = e;
      return this;
    };
    e.prototype.setOwner = function (e) {
      this.owner = e;
    };
    e.prototype.clearAllEffect = function () {
      for (var e = this.cutEffect.length - 1; e >= 0; e--) {
        var t = this.cutEffect[e].getComponent($2BaseEntity.default);
        if (t) {
          this.game.removeEntity(t);
        } else {
          this.cutEffect[e].destroy();
        }
      }
      this.cutEffect.length = 0;
      this.cutEffectPath.length = 0;
    };
    e.prototype.showBuffEffect = function () {
      var e = this;
      this.cutVo.res && (this.cutEffectPath.includes(this.cutVo.res[0]) || (this.cutEffectPath.push(this.cutVo.res[0]), this.game.showEffectByPath(this.cutVo.res[0], {
        nodeAttr: {
          parent: this.owner.getShowTier(+this.cutVo.res[1]),
          position: cc.Vec2.ZERO,
          scale: 1
        },
        spAttr: {
          defaultAnimation: "animation",
          premultipliedAlpha: false,
          loop: true
        }
      }).then(function (t) {
        var o;
        null !== (o = e.owner) && undefined !== o && o.isDead || e.cutEffect.push(t.node);
      })));
    };
    e.prototype.addLayer = function () {
      this.isMaxLayer || this.setLayer(this.buffLayer + 1);
    };
    e.prototype.unuseLayer = function (e) {
      undefined === e && (e = -1);
      this.setLayer(Math.max(0, this.buffLayer + e));
    };
    e.prototype.load = function (e) {
      var t;
      var o = this;
      this.buffCfg = e;
      this.isActive = !this.cutVo.skillId || this.cutVo.skillId.includes(0) || this.owner.skillMgr && $2GameUtil.GameUtil.hasIntersection(this.cutVo.skillId, this.owner.skillMgr.skillIDs);
      this.buffLayer = 1;
      this.tempData = {};
      this.changeListener(false);
      this.changeListener(true);
      this.owner.delayByGame(function () {
        o.onLoad();
      });
      i.includes(this.cutVo.className) || this.showBuffEffect();
      this.attrMap.has($2GameatrCfg.GameatrDefine.replaceBuff) && this.mgr.clearBuffByID(this.attrMap.get($2GameatrCfg.GameatrDefine.replaceBuff));
      if (this.attrMap.has($2GameatrCfg.GameatrDefine.replaceSkill) && this.owner.skillMgr.skillIDs.includes(this.cutVo.skillId[0])) {
        var n = null === (t = this.owner.skillMgr.get(this.cutVo.skillId[0])) || undefined === t ? undefined : t.skillCfg.dam;
        this.owner.skillMgr.clearByID(this.cutVo.skillId[0]);
        var r = this.owner.addSkill(this.attrMap.get($2GameatrCfg.GameatrDefine.replaceSkill));
        r.skillCfg.dam = n * r.skillCfg.dam;
        r.checkGain();
      }
      this.attrMap.has($2GameatrCfg.GameatrDefine.addSkill) && this.specialMap.forEach(function (e) {
        return e.type == $2GameatrCfg.GameatrDefine.addSkill && o.owner.addSkill(e.data[0]);
      });
    };
    e.prototype.unload = function () {
      var e = this;
      this.changeListener(false);
      this.attrMap.clear();
      this.clearAllEffect();
      this.attrMap.has($2GameatrCfg.GameatrDefine.addSkill) && this.specialMap.forEach(function (t) {
        return t.type == $2GameatrCfg.GameatrDefine.addSkill && e.owner.skillMgr.clearByID(t.data[0]);
      });
    };
    e.prototype.onLoad = function () {};
    e.prototype.excute = function () {};
    e.prototype.changeListener = function () {};
    e.prototype.onUpdate = function (e) {
      this._isActive && (this.isForever || (this._curBuffTime -= e));
    };
    Object.defineProperty(e.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setAttribute = function (e) {
      for (var t in e) {
        this[t] = e[t];
      }
      return this;
    };
    return e;
  }();
  e.BuffItem = d;
  var g = function () {
    function e(e) {
      this.isBanMove = false;
      this.isInvincible = false;
      this.isDisarm = false;
      this.attrMapAll = new $2GameSeting.TMap();
      this.attrBuffMap = new $2GameSeting.TMap();
      this._letMap = new $2GameSeting.TMap();
      this.ower = e;
      this._bufflist = [];
      this.ower.botEffectBox = this.ower.node.getORaddChildByName("botEffectBox").setAttribute({
        zIndex: -9,
        position: this.ower._bodyPosition
      });
      this.ower.topEffectBox = this.ower.node.getORaddChildByName("topEffectBox").setAttribute({
        zIndex: 999,
        position: this.ower._bodyPosition
      });
      $2GameUtil.GameUtil.sort(this.ower.node.children, function (e, t) {
        return t.zIndex > e.zIndex;
      }) && this.ower.node._delaySort();
    }
    Object.defineProperty(e.prototype, "bufflist", {
      get: function () {
        return this._bufflist;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.add = function (e, t) {
      undefined === t && (t = 1);
      var o = $2Game.ModeCfg.Buff.get(e);
      if (o) {
        return this.addByData(o, t);
      }
      cc.error("没有找到buffid:", e);
    };
    e.prototype.addByData = function (e, t) {
      undefined === t && (t = 1);
      var o = this.isHasID([e.id]);
      if (o) {
        if (e.isOverlay > 1) {
          o.cutVo.isOverlay != e.isOverlay && (o._buffCfg.isOverlay = e.isOverlay);
          if (o.isMaxLayer) {
            return;
          }
          o.addLayer();
        } else {
          o.load(e);
        }
      } else {
        var i = this.createBuffByType(e);
        i.setOwner(this.ower);
        i.load(e);
        this._bufflist.push(i);
        o = i;
      }
      t > 1 && o.setLayer(t);
      this.onBuffChanges(e);
      return o;
    };
    e.prototype.onUpdate = function (e) {
      for (var t = 0; t < this._bufflist.length; t++) {
        var o = this._bufflist[t];
        if (o) {
          o.onUpdate(e);
          o.curBuffTime <= 0 && this.clearBuff(o);
        }
      }
    };
    e.prototype.clearBuff = function (e) {
      undefined === e && (e = null);
      if (e) {
        t = this._bufflist.indexOf(e);
        this._bufflist[t].unload();
        this._bufflist[t] = null;
        this._bufflist.splice(t, 1);
      } else {
        for (var t = this._bufflist.length - 1; t >= 0; t--) {
          this._bufflist[t].unload();
          this._bufflist[t] = null;
          this._bufflist.splice(t, 1);
        }
        this._bufflist.length = 0;
      }
      this.onBuffChanges(null == e ? undefined : e.cutVo);
    };
    e.prototype.clearBuffByID = function (e) {
      var t;
      for (var o = this._bufflist.length - 1; o >= 0; o--) {
        var i = this._bufflist[o];
        if (i.cutVo.id == e) {
          t = i.cutVo;
          i.unload();
          this._bufflist[o] = null;
          this._bufflist.splice(o, 1);
        }
      }
      this.onBuffChanges(t);
    };
    e.prototype.clearDebuff = function () {
      var e;
      for (var t = this._bufflist.length - 1; t >= 0; t--) {
        var o = this._bufflist[t];
        if (2 == o.cutVo.type) {
          e = o.cutVo;
          o.unload();
          this._bufflist[t] = null;
          this._bufflist.splice(t, 1);
          this.onBuffChanges(e);
        }
      }
    };
    e.prototype.forEach = function (e) {
      for (var t = this._bufflist.length - 1; t >= 0; t--) {
        e(this._bufflist[t], t);
      }
    };
    Object.defineProperty(e.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.onBuffChanges = function (e) {
      var t = this;
      if (this.ower.isValid) {
        this.attrBuffMap.clear();
        this.attrMapAll.clear();
        this.bufflist.forEach(function (e) {
          e.checkGain();
          e.cutVo.attr.forEach(function (o) {
            t.attrBuffMap.has(o) || t.attrBuffMap.set(o, []);
            t.attrBuffMap.get(o).push(e);
          });
          e.attrMap.forEach(function (e, o, i) {
            t.attrMapAll.add(i, e);
          });
        });
        this.isBanMove = this.attrMapAll.has($2GameatrCfg.GameatrDefine.freeze);
        this.isInvincible = this.attrMapAll.has($2GameatrCfg.GameatrDefine.invincible);
        this.isDisarm = this.attrMapAll.has($2GameatrCfg.GameatrDefine.disarm);
        if (this.ower.entityType == $2BaseEntity.EntityType.Role) {
          this.game.timeDelay.cancelBy(this._buffChangMsg), this._buffChangMsg = this.ower.delayByGame(function () {
            t._bufflist && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_OnBuffChange, t._bufflist);
          }, .2).id;
        }
        this.ower.node.emit($2ListenID.ListenID.Fight_OnBuff, e);
      }
    };
    e.prototype.createBuffByType = function (e) {
      var t = e.className || "Buff_Default";
      var o = cc.js.getClassByName(t);
      if (o) {
        return new o();
      } else {
        return cc.error("can't find class by name", t), null;
      }
    };
    e.prototype.getAttr = function (e, t) {
      undefined === e && (e = $2GameatrCfg.GameatrDefine.exp);
      undefined === t && (t = 0);
      return this.attrMapAll.getor(e, t);
    };
    e.prototype.getAttrSync = function (e, t) {
      undefined === e && (e = $2GameatrCfg.GameatrDefine.exp);
      var o = 0;
      this.attrBuffMap.getor(e, []).forEach(function (t) {
        o += t.attrMap.getor(e, 0);
      });
      t(o);
    };
    e.prototype.getAttrAsync = function (e) {
      var t = this;
      undefined === e && (e = $2GameatrCfg.GameatrDefine.exp);
      return new Promise(function (o) {
        var i = 0;
        t.attrBuffMap.getor(e, []).forEach(function (t) {
          i += t.attrMap.getor(e, 0);
        });
        o(i);
      });
    };
    e.prototype.use = function (e, t, o) {
      undefined === t && (t = true);
      var i = this.get(e);
      if (i) {
        o(i);
        t && this.clearBuff(i);
      }
    };
    e.prototype.isHasAttr = function (e) {
      for (var t = 0; t < this._bufflist.length; t++) {
        if (this._bufflist[t].attrMap.has(e)) {
          return true;
        }
      }
      return false;
    };
    e.prototype.isHasID = function (e) {
      for (var t = 0; t < this._bufflist.length; t++) {
        if (e.includes(this._bufflist[t].cutVo.id)) {
          return this._bufflist[t];
        }
      }
      return null;
    };
    e.prototype.getByID = function (e) {
      var t = this;
      return new Promise(function (o) {
        var i = t.get(e);
        i && o(i);
      });
    };
    e.prototype.get = function (e) {
      return this.bufflist.find(function (t) {
        return t.cutVo.id == e;
      });
    };
    e.prototype.getArrAttr = function (e, t) {
      undefined === t && (t = this._letMap);
      t.clear();
      e.forEach(function (e) {
        e.attrMap.forEach(function (e, o, i) {
          t.add(i, e);
        });
      });
      return t;
    };
    e.prototype.getAttrByIDs = function (e, t) {
      t(this.getArrAttr(this.bufflist.filter(function (t) {
        return e.includes(t.ID);
      })));
    };
    e.prototype.getSpecificBuff = function (e) {
      return this.bufflist.filter(function (t) {
        return t.isActive && (!t.cutVo.buffId || undefined === e.buffID || t.cutVo.buffId.includes(e.buffID)) && (!t.cutVo.skillId || undefined === e.skillID || t.cutVo.skillId.includes(e.skillID));
      });
    };
    e.prototype.getSpecificBuffAttr = function (e) {
      return this.getArrAttr(this.getSpecificBuff(e));
    };
    e.prototype.destroy = function () {
      this.clearBuff();
      this.attrBuffMap.clear();
      this.attrMapAll.clear();
    };
    return e;
  }();
  e.BuffManager = g;
  var y = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    t.prototype.onEnter = function () {};
    t.prototype.onExecute = function () {
      return false;
    };
    t.prototype.onExit = function () {};
    t.prototype.onReEnter = function () {};
    return t;
  }(d);
  e.DefaultBuff = y;
})(exports.Buff || (exports.Buff = {}));