var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2StateMachine = require("StateMachine");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2TideDefendModel = require("TideDefendModel");
var $2Game = require("Game");
var $2BaseEntity = require("BaseEntity");
var $2MonsterTidal = require("MonsterTidal");
var $2MTideDefendRebound = require("MTideDefendRebound");
cc.v2();
cc.v2();
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_MonsterTideDefend = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.SoundId = 0;
    t.bombImgStr = "";
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2TideDefendModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
    this.LifeLabelMn = this.node.getComByChild(cc.Label, "LifeLabelMn");
    this.LifeLabelBoss = this.node.getComByChild(cc.Label, "LifeLabelBoss");
    this.img_bx = this.node.getComByChild(cc.Sprite, "img_bx");
  };
  Object.defineProperty(_ctor.prototype, "bBarrel", {
    get: function () {
      return this.lvCfg.dropExpRatio;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bAligning", {
    get: function () {
      return this.lvCfg.bronMatrix;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    var t = this._stateMachine.getStateByTag($2StateMachine.State.Type.MOVE);
    t && t.dtMoveX && (t.dtMoveX = 0);
    this.LifeLabelMn.string = 1 == this.curHp ? "" : "" + this.curHp;
    if (this.bBarrel) {
      var o = 0;
      for (var i in this.lvCfg.dropExpRatio) {
        var n = this.lvCfg.dropExpRatio[i];
        n && 27 == n[0] && (o = 1 == n[1] ? n[1] : 3);
      }
      if (o) {
        this.img_bx.node.active = true;
        var r = "v1/images/fight/icon_tcs_bx0" + o;
        $2Manager.Manager.loader.loadSpriteToSprit(r, this.img_bx);
      }
    } else {
      this.img_bx.node.active = false;
    }
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this.upMonHp();
    this.reachPos();
  };
  _ctor.prototype.reachPos = function () {
    if (this.position.y <= this.mode.role.haedPosition.y) {
      if (this.bBarrel) {
        this.removeEntityToUpdate();
      } else {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
      }
    }
  };
  _ctor.prototype.upMonHp = function () {
    if (this.bBarrel) {
      this.LifeLabelBoss.string = this.curHp > 0 ? $2GameUtil.GameUtil.changeNumStr(this.curHp, 4) : "";
      this.LifeLabelMn.string = "";
    } else {
      this.LifeLabelMn.string = this.curHp > 0 ? $2GameUtil.GameUtil.changeNumStr(this.curHp, 4) : "";
      this.LifeLabelMn.node.setPosition(cc.v2(0, this.roleNode.height / 2));
      this.LifeLabelBoss.string = "";
    }
  };
  _ctor.prototype.droppedItems = function () {
    var e = this;
    if (this.lvCfg.dropExpRatio) {
      var t = false;
      this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (o) {
        if (o.id == $2CurrencyConfigCfg.CurrencyConfigDefine.buffSelect && !t) {
          var i = 2 == o.num ? $2MTideDefendRebound.MTideDefendRebound.poolType.HighBuff : $2MTideDefendRebound.MTideDefendRebound.poolType.NormalBuff;
          $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
            type: i,
            getPool: function () {
              return e.mode.fightBuffWidth(i);
            }
          }).setDailyTime(.3));
          t = true;
        }
      });
    }
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    var t;
    if ((null === (t = e.comp) || undefined === t ? undefined : t.entityType) == $2BaseEntity.EntityType.Bullet && !this.bBarrel) {
      var o = this.mode.role;
      if (o && o.myData && 30400 != o.myData.id) {
        var i = cc.v2(e.comp.position.x + 15, e.comp.position.y + 15);
        var n = this.SoundId.valueOf();
        n && $2Manager.Manager.audio.playAudio(n);
        "" != this.bombImgStr && $2Game.Game.mgr.showEffectByType(this.bombImgStr.toString(), i, true, .3, {
          parent: this.game.botEffectNode
        });
      }
    }
  };
  cc__decorate([ccp_property], _ctor.prototype, "SoundId", undefined);
  cc__decorate([ccp_property], _ctor.prototype, "bombImgStr", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}($2MonsterTidal.default);
exports.default = def_MonsterTideDefend;