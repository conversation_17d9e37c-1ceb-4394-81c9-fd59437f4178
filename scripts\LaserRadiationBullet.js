var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSkeleton = require("GameSkeleton");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_LaserRadiationBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.isMove = true;
    t.offset = cc.Vec2.ZERO;
    t._maxWidth = 120;
    t.isReady = false;
    t.isAutoChangTarget = false;
    t._changeTime = .5;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "target", {
    get: function () {
      return this._target;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.node.active = false;
    this.mySkeleton || (this.mySkeleton = this.node.getComByChild($2GameSkeleton.default));
  };
  _ctor.prototype.set = function (e) {
    var t = this;
    this._target = e;
    // this.mySkeleton.playQueue(["in", "loop"], true);
    this.mySkeleton.playQueue(["huiju", "guangzhu_loop"], true);
    this.delayByGame(function () {
      // t.playAction("out", false);
      t.playAction("daiji", false)
    }, this.vo.lifeTime - .3);
    this.node.active = true;
  };
  _ctor.prototype.changeTarget = function () {
    var e = $2Game.Game.Mgr.instance.getTarget({
      target: this.target,
      radius: this.vo.belongSkill.dis,
      maxNum: 1
    })[0];
    if (e) {
      this.atkTaget = e;
      var t = $2GameUtil.GameUtil.GetAngle(this.node, e.position) + 90;
      var o = 20 * Math.abs(this.angle - t) / this.vo.skillCfg.barrangeSpeed;
      $2Game.Game.tween(this.node).to(o, {
        angle: t
      }).start();
    }
    this._changeTime = .5;
  };
  _ctor.prototype.onUpdate = function (t) {
    this._changeTime -= t;
    if (this.target) {
      var o = $2GameUtil.GameUtil.GeAngletDir(this.angle + 90);
      this.setPosition(this.target.position.add(this.offset).add(o.mul(100)));
    }
    this.isAutoChangTarget && this.atkTaget.isDead && this._changeTime <= 0 && this.changeTarget();
    e.prototype.onUpdate.call(this, t);
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/LaserRadiationBullet")], _ctor);
}($2BulletBase.default);
exports.default = def_LaserRadiationBullet;