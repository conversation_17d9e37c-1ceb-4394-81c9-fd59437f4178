var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2MovingBGAssembler = require("MovingBGAssembler");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var f = false;
var def_MovingBGSprite = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t._moveSpeed = cc.Vec2.ZERO;
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.NO_BG_Moving, t.noMoving, t);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onDestroy = function () {
    e.prototype.onDestroy.call(this);
    $2Notifier.Notifier.changeListener(false, $2ListenID.ListenID.NO_BG_Moving, this.noMoving, this);
  };
  _ctor.prototype.noMoving = function () {
    f = true;
    this.moveSpeed = cc.Vec2.ZERO;
  };
  Object.defineProperty(_ctor.prototype, "moveSpeed", {
    get: function () {
      return this._moveSpeed;
    },
    set: function (e) {
      f && (e = cc.Vec2.ZERO);
      this._moveSpeed = e;
      this.FlushProperties();
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.FlushProperties = function () {
    var e = this._assembler;
    if (e) {
      e.moveSpeed = f ? cc.Vec2.ZERO : this._moveSpeed;
      this.setVertsDirty();
    }
  };
  _ctor.prototype.onEnable = function () {
    e.prototype.onEnable.call(this);
  };
  _ctor.prototype._resetAssembler = function () {
    this.setVertsDirty();
    var e = this._assembler = new $2MovingBGAssembler.default();
    this.FlushProperties();
    e.init(this);
    this._updateColor();
  };
  _ctor.prototype.setMoveSpeed = function (e) {
    var t;
    this.moveSpeed.set(e);
    null === (t = this._assembler) || undefined === t || t.moveSpeed.set(e);
  };
  cc__decorate([ccp_property(cc.Vec2)], _ctor.prototype, "moveSpeed", null);
  cc__decorate([ccp_property(cc.Vec2)], _ctor.prototype, "_moveSpeed", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Sprite);
exports.default = def_MovingBGSprite;