var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2GameEffect = require("GameEffect");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_EntityDieEffect = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._noiseThreshold = .2;
    t._speed = 1;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    $2Game.Game.tween(this.node.children[0]).stopLast().set({
      x: 0,
      y: 0
    }).parallel(cc.tween().to(.2, {
      y: 100
    }).to(.5, {
      y: 0
    }, {
      easing: cc.easing.bounceOut
    }), cc.tween().to(.5, {
      x: $2Game.Game.random(-100, 100),
      angle: 350 * $2GameUtil.GameUtil.getRandomInArray([-1, 1])[0]
    })).start();
  };
  return cc__decorate([ccp_ccclass, ccp_menu("GameEffect/EntityDieEffect")], _ctor);
}($2GameEffect.default);
exports.default = def_EntityDieEffect;