var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RecordVo = undefined;
var $2StorageID = require("StorageID");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2GameUtil = require("GameUtil");
function u(e, t) {
  if (e && "object" == typeof e) {
    return new Proxy(e, {
      set: function (e, o, i, n) {
        var r = Reflect.set(e, o, i, n);
        t(o, i);
        return r;
      },
      deleteProperty: function (e, o) {
        var i = Reflect.deleteProperty(e, o);
        i && t(o, undefined);
        return i;
      }
    });
  } else {
    return e;
  }
}
function p(e, t) {
  if ("object" != typeof e || null == e) {
    throw new Error("The observable must be an object.");
  }
  var o = Array.isArray(e) ? [] : {};
  Object.keys(e).forEach(function (i) {
    e[i] instanceof Object && Object.keys(e[i]).forEach(function (o) {
      e[i][o] = u(e[i][o], t);
    });
    o[i] = u(e[i], t);
  });
  return u(o, t);
}
(function (e) {
  function t(e) {
    return $2StorageID.StorageID.GameTag + "-Record-" + e;
  }
  function o(e) {
    var o = $2Manager.Manager.storage.getString(t(e));
    if (o) {
      return JSON.parse(o);
    } else {
      return null;
    }
  }
  function u(e) {
    $2Manager.Manager.storage.remove(t(e));
  }
  function f(e, o) {
    $2Manager.Manager.storage.setString(t(e), o);
  }
  e.Data = function () {
    this.dailyData = {
      curday: new Date(Date.now()).getDate()
    };
    this.expireData = {
      time: 0
    };
  };
  e.ReadData = o;
  e.CleanData = u;
  e.SaveData = f;
  var h = function () {
    function e(e, t, o) {
      undefined === o && (o = {});
      this.name = "NotDefine";
      this.isAutoSave = false;
      this.isSendMsg = true;
      this.isAutoToform = true;
      this.isAutoSave = o.isAutoSave;
      this.isAutoToform = o.isAutoToform;
      this.extraDailyKey = o.extraDailyKey || [];
      this.onChangeCall = o.onChange;
      this.name = e;
      this.getNewDataCall = t;
      this.defaultData = t();
      var i = this.ReadData();
      if (i) {
        for (var n in this.defaultData) {
          null == i[n] && (i[n] = this.defaultData[n]);
        }
        o.isAutoToform && (i = this.toform(i));
        this.vo = this.isAutoSave ? p(i, this.onVoChange.bind(this)) : $2GameUtil.GameUtil.deepCopy(i);
        this.checkDailyData(this.defaultData);
      } else {
        i = this.defaultData;
        o.isAutoSave && this.SaveData();
        this.vo = this.isAutoSave ? p(i, this.onVoChange.bind(this)) : $2GameUtil.GameUtil.deepCopy(i);
      }
      this.onChangeCall && this.onChangeCall(this.vo);
      $2Notifier.Notifier.addListener($2NotifyID.NotifyID.Time_NewDay, this.onNewDay, this);
    }
    e.prototype.onVoChange = function () {
      this.onChangeCall && this.onChangeCall(this.vo);
      this.isAutoSave && this.SaveData();
    };
    e.prototype.onNewDay = function () {
      this.defaultData = null;
      this.defaultData = this.getNewDataCall();
      this.checkDailyData(this.defaultData);
    };
    e.prototype.testDailyData = function () {
      this.vo.dailyData.curday -= 2;
      this.checkDailyData(this.defaultData);
    };
    e.prototype.checkDailyData = function (e) {
      var t = this;
      var o = new Date(Date.now()).getDate();
      var n = cc__spreadArrays(["dailyData"], this.extraDailyKey);
      var r = this.vo.dailyData.curday !== o;
      n.forEach(function (o) {
        var i = e[o];
        if (r) {
          t.vo[o] = p(i, t.onVoChange.bind(t));
        } else {
          for (var n in i) {
            null == t.vo[o][n] && (t.vo[o][n] = i[n]);
          }
        }
      });
    };
    e.prototype.toform = function (e) {
      if (e && "object" == typeof e) {
        if (null != e.x && null != e.y) {
          e = cc.v2(e.x, e.y);
        } else {
          for (var t in e) {
            e[t] = this.toform(e[t]);
          }
        }
      }
      return e;
    };
    e.prototype.ReadData = function () {
      return o(this.name);
    };
    e.prototype.SaveData = function () {
      var e = this;
      $2Time.Time.timeDelay.cancelBy(this.sTimer);
      this.sTimer = $2Time.Time.delay(.3, function () {
        var t = JSON.stringify(e.vo);
        f(e.name, t);
        $2Manager.Manager.vo.pushRemoteDataSave();
      }).id;
    };
    e.prototype.SetVal = function (e, t) {
      undefined === t && (t = false);
      for (var o in e) {
        this.vo[o] = e[o];
      }
      (this.isAutoSave || t) && this.SaveData();
    };
    e.prototype.CleanData = function () {
      u(this.name);
    };
    return e;
  }();
  e.Mgr = h;
})(exports.RecordVo || (exports.RecordVo = {}));