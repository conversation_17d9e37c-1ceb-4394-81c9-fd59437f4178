var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MoreGames = undefined;
var a;
var $2GridView = require("GridView");
var $2VideoButton = require("VideoButton");
var $2Cfg = require("Cfg");
var $2SoundCfg = require("SoundCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Pop = require("Pop");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2ModeChainsModel = require("ModeChainsModel");
var $2MoreGamesItem = require("MoreGamesItem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_MoreGamesView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._viewBgm = $2SoundCfg.SoundDefine.bgm_lobby;
    t.startGameBtn = null;
    t.videoBtn = null;
    t.knifeToggle = null;
    t.bulletsToggle = null;
    t.tideToggle = null;
    t.myGridView = null;
    t.toggleContainer = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.MoreGame_Refresh, this.refreshKnifePage, this);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Main_ResetView, this.resetView, this);
  };
  _ctor.prototype.resetView = function () {
    var e = cc.find("top/DiffSelect", this.node);
    e.setActive(this.cutMode == $2Game.Game.Mode.CHAINS && $2Manager.Manager.vo.switchVo.diffSelect);
    var t = $2GameSeting.GameSeting.getDiffDef($2ModeChainsModel.default.instance.rVo.selectDiffType);
    e.getComByChild(cc.RichText).text = cc.js.formatStr("<outline color=black width=3>当前难度:<color=%s>%s</c>", t.colorStr, t.name);
  };
  _ctor.prototype.setInfo = function () {
    var e;
    var t;
    var o;
    this.resetList();
    this.nodeArr[2].getComponent($2MoreGamesItem.default).updateRole();
    this.knifeToggle.node.active = 1 == $2Manager.Manager.vo.switchVo.GameKnife;
    this.tideToggle.node.active = 1 == $2Manager.Manager.vo.switchVo.GameZombieDef;
    if (0 == this._openArgs.param.selectTab && 1 == $2Manager.Manager.vo.switchVo.GameKnife) {
      this.knifeToggle.isChecked = true;
      this.onPageSelect($2Game.Game.Mode.THROWINGKNIFE);
      this.refreshKnifePage(this._openArgs.param.openState);
    } else {
      this.bulletsToggle.isChecked = true;
      if (this._openArgs.param.pageIndex) {
        var i = "0";
        for (var n in this.toggleContainer.toggleItems) {
          if ((null === (e = this.toggleContainer.toggleItems[n]) || undefined === e ? undefined : e.checkEvents) && (null === (o = null === (t = this.toggleContainer.toggleItems[n]) || undefined === t ? undefined : t.checkEvents[0]) || undefined === o ? undefined : o.customEventData) == this._openArgs.param.pageIndex) {
            i = n;
            break;
          }
        }
        this.toggleContainer.toggleItems[i] && this.toggleContainer.toggleItems[i].check();
        this.onPageSelect(this._openArgs.param.pageIndex);
      } else {
        this.onPageSelect($2Game.Game.Mode.PICKUPBULLETS);
      }
    }
    this.nodeArr[1].getComponent(cc.Layout).paddingTop = $2GameUtil.GameUtil.getDesignSize.height - 602;
  };
  _ctor.prototype.refreshKnifePage = function (e) {
    var t = this;
    switch (e) {
      case -1:
        break;
      case 0:
        this.showDownAnim();
        cc.tween(this).delay(.6).call(function () {
          $2Manager.Manager.vo.userVo.knifePassLv = 0;
          t.resetList();
        }).delay(.3).call(function () {
          t.scrollToPos();
        }).start();
        break;
      case 1:
        this.showUpAnim();
        cc.tween(this).delay(.6).call(function () {
          var e = $2Cfg.Cfg.MiniGameLv.filter({
            type: 30
          });
          var o = $2Manager.Manager.vo.userVo.knifePassLv;
          if (!(o >= e.length)) {
            $2Manager.Manager.vo.userVo.knifePassLv++;
            t.checkKnifeBtn();
            var i = t.nodeArr[1].children[o];
            i && i.getComponent($2MoreGamesItem.default).setWin();
            t.checkKnifeBtn();
          }
        }).delay(.3).call(function () {
          t.scrollToPos();
        }).start();
    }
  };
  _ctor.prototype.showDownAnim = function () {
    cc.tween(this.nodeArr[5]).to(.3, {
      opacity: 255
    }).delay(.15).to(.3, {
      opacity: 0
    }).union().repeat(3).start();
  };
  _ctor.prototype.showUpAnim = function () {
    cc.tween(this.nodeArr[6]).to(.3, {
      opacity: 255
    }).delay(.15).to(.3, {
      opacity: 0
    }).union().repeat(3).start();
  };
  _ctor.prototype.resetList = function () {
    var e = this;
    var t = 0;
    var o = $2Cfg.Cfg.MiniGameLv.filter({
      type: 30
    });
    o.forEach(function (i) {
      e.setItem(i, t, t == o.length - 1);
      t++;
    });
    this.checkKnifeBtn();
  };
  _ctor.prototype.checkKnifeBtn = function () {
    var e = $2Cfg.Cfg.MiniGameLv.filter({
      type: 30
    });
    var t = $2Manager.Manager.vo.userVo.knifePassLv;
    t >= e.length && (t = e.length - 1);
    var o = e[t];
    var i = o.type;
    var n = $2Manager.Manager.leveMgr.vo.curPassLv >= o.unlockLv || $2Manager.Manager.vo.userVo.unLockMode.includes(i + "_" + o.id);
    var r = cc.js.formatStr("通关第%d章解锁", o.unlockLv);
    this.labelArr[0].string = r;
    this.labelArr[0].node.active = !n;
    this.startGameBtn.node.setActive(n);
    this.startGameBtn.clickEvents[0].customEventData = o.id + "";
    var s = $2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + i + "_" + o.id);
    this.videoBtn.node.setActive(!n);
    this.videoBtn.clickEvents[0].customEventData = o.id + "";
    this.videoBtn.setAttribute({
      eventPram: {
        unlockID: i + "_" + o.id
      }
    });
    this.videoBtn.node.getComByChild(cc.RichText, "num").text = "<outline color=black width=4><color=#ffe064>" + s + "</c>/ " + a.getUnlockNum(o.id) + "</outline>";
    this.nodeArr[2].getComponent($2MoreGamesItem.default).updateRoleLv();
  };
  _ctor.prototype.setItem = function (e, t, o) {
    undefined === o && (o = false);
    if (e.type == $2Game.Game.Mode.THROWINGKNIFE) {
      (this.nodeArr[1].children[t] || cc.instantiate(this.nodeArr[1].children[0]).setAttribute({
        parent: this.nodeArr[1]
      })).getComponent($2MoreGamesItem.default).onRefresh(e, t);
      this.nodeArr[1].getComponent(cc.Layout).updateLayout();
    }
  };
  _ctor.prototype.onClickOpenGame = function (e, t) {
    var o = this;
    var i = +t;
    var n = $2Cfg.Cfg.MiniGameLv.get(i);
    var r = function () {
      var e = $2Game.Game.getMouth(n.type);
      $2Notifier.Notifier.send(e.mouth, n.type, $2MVC.MVC.openArgs().setParam({
        id: i
      }));
      o.close();
    };
    if (n.type == $2Game.Game.Mode.THROWINGKNIFE) {
      this.nodeArr[7].active = true;
      cc.tween(this.nodeArr[7]).delay(.3).to(.15, {
        scale: 1.8
      }).to(.15, {
        scale: 1
      }).delay(.3).union().call(function () {
        r();
      }).start();
      this.scrollToPos();
    } else {
      r();
    }
  };
  _ctor.prototype.scrollToPos = function () {
    var e = $2Cfg.Cfg.MiniGameLv.filter({
      type: 30
    });
    var t = $2Manager.Manager.vo.userVo.knifePassLv;
    t >= e.length && (t = e.length - 1);
    var o = this.nodeArr[1].children[t];
    var i = this.nodeArr[1].height;
    var n = o.position.y - o.height / 2 - 333;
    var r = this.nodeArr[1].getComponent(cc.Layout).paddingTop;
    this.nodeArr[8].getComponent(cc.ScrollView).scrollToPercentVertical(n / (i - 333 - r - o.height), .3, true);
  };
  _ctor.prototype.onVideoUnlock = function (e, t) {
    var o = +t;
    var i = $2Cfg.Cfg.MiniGameLv.get(o);
    var n = i.type;
    $2Manager.Manager.vo.knapsackVo.addGoods("ModeUnlockVideo_" + n + "_" + i.id);
    if ($2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + n + "_" + i.id) >= a.getUnlockNum(i.id)) {
      $2Manager.Manager.vo.userVo.unLockMode.push(n + "_" + i.id);
      $2Manager.Manager.vo.saveUserData();
      $2AlertManager.AlertManager.showNormalTips("解锁成功");
    }
    this.resetList();
  };
  _ctor.prototype.onClickToggle = function (e, t) {
    this.onPageSelect(+t);
  };
  _ctor.prototype.onPageSelect = function (e) {
    switch (e) {
      case $2Game.Game.Mode.THROWINGKNIFE:
        this.nodeArr[3].active = false;
        this.nodeArr[4].active = true;
        this.myGridView.loadData([]);
        break;
      case $2Game.Game.Mode.PICKUPBULLETS:
      case $2Game.Game.Mode.BULLETSREBOUND:
      case $2Game.Game.Mode.CHAINS:
      case $2Game.Game.Mode.TIDEDEFEND:
      case $2Game.Game.Mode.MANGUARDS:
      case $2Game.Game.Mode.ALLOUTATTACK:
      case $2Game.Game.Mode.DRAGONWAR:
        this.nodeArr[3].active = true;
        this.nodeArr[4].active = false;
        var t = $2Cfg.Cfg.MiniGameLv.filter({
          type: e
        }).filter(function (e) {
          return !$2Manager.Manager.vo.switchVo.miniGameLv.includes(e.id);
        });
        t.sort(function (e, t) {
          return e.sortId - t.sortId;
        });
        this.myGridView.loadData(t);
    }
    this.cutMode = e;
    this.resetView();
  };
  cc__decorate([ccp_property(cc.Button)], _ctor.prototype, "startGameBtn", undefined);
  cc__decorate([ccp_property($2VideoButton.default)], _ctor.prototype, "videoBtn", undefined);
  cc__decorate([ccp_property(cc.Toggle)], _ctor.prototype, "knifeToggle", undefined);
  cc__decorate([ccp_property(cc.Toggle)], _ctor.prototype, "bulletsToggle", undefined);
  cc__decorate([ccp_property(cc.Toggle)], _ctor.prototype, "tideToggle", undefined);
  cc__decorate([ccp_property($2GridView.default)], _ctor.prototype, "myGridView", undefined);
  cc__decorate([ccp_property(cc.ToggleContainer)], _ctor.prototype, "toggleContainer", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Setting/MoreGamesView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Main), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Panel)], _ctor);
}($2Pop.Pop);
exports.default = def_MoreGamesView;
(function (e) {
  e.getUnlockNum = function (e) {
    var t = $2Cfg.Cfg.MiniGameLv.get(e);
    if (t.unlockAd) {
      return t.unlockAd + $2Manager.Manager.vo.switchVo.miniGameAdUnlock;
    } else {
      return 0;
    }
  };
})(a = exports.MoreGames || (exports.MoreGames = {}));