var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2RewardEvent = require("RewardEvent");
var $2SkillModel = require("SkillModel");
var $2BaseEntity = require("BaseEntity");
var $2Vehicle = require("Vehicle");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var y = cc.v2();
var def_NPC = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.isOpenView = false;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.entityType = $2BaseEntity.EntityType.Neutrality;
    this.isOpenView = false;
  };
  _ctor.prototype.set = function (e) {
    this.type = e;
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    e.comp.isDead || e.comp.entityType != $2BaseEntity.EntityType.Role || this.access();
  };
  _ctor.prototype.onCollisionStay = function (e) {
    if (e.comp) {
      y = e.comp.position.sub(this.node.position).normalize().add(e.comp.position);
      e.comp.setPosition(y);
    }
  };
  _ctor.prototype.access = function () {
    var e;
    var t = this;
    this.isOpenView = true;
    switch (this.type) {
      case $2RewardEvent.RewardEvent.Type.Skill:
        var o = $2SkillModel.default.getInstance.cutLevelSkill;
        var i = $2SkillModel.default.getInstance.getSkillPool(o.lvupPool);
        var n = $2SkillModel.default.getInstance.randomSkill(i, 5, o.maxWeight[1]);
        e = {
          view: $2ListenID.ListenID.Skill_OpenSelcectView,
          args: $2MVC.MVC.openArgs().setParam(n)
        };
        break;
      case $2RewardEvent.RewardEvent.Type.SkillBuff:
      case $2RewardEvent.RewardEvent.Type.Buff:
        e = {
          view: $2ListenID.ListenID.Buff_OpenSelcectView,
          args: $2MVC.MVC.openArgs().setParam(this.type)
        };
        break;
      case $2RewardEvent.RewardEvent.Type.Role:
        e = {
          view: $2ListenID.ListenID.Fight_OpenRoleTryView,
          args: $2MVC.MVC.openArgs().setParam({
            cfg: $2Cfg.Cfg.RoleUnlock.get(5)
          })
        };
        break;
      case $2RewardEvent.RewardEvent.Type.Pet:
        e = {
          view: $2ListenID.ListenID.Fight_OpenPetTryView,
          args: $2MVC.MVC.openArgs().setParam({
            roleID: 100
          })
        };
    }
    e.args.setCallback(function (e) {
      cc.log(e);
      1 == e && (t.isDead = true);
    });
    $2Notifier.Notifier.send(e.view, e.args);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2Vehicle.default);
exports.default = def_NPC;