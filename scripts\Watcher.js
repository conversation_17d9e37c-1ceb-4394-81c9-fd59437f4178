Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Watcher = undefined;
var $2BaseEntity = require("BaseEntity");
var $2Log = require("Log");
var exp_Watcher = function () {
  function _ctor() {}
  Object.defineProperty(_ctor.prototype, "id", {
    get: function () {
      return this._id;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "nextTime", {
    get: function () {
      return this._nextTime;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "delay", {
    get: function () {
      return this._delay;
    },
    set: function (e) {
      this._delay = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "times", {
    get: function () {
      return this._times;
    },
    set: function (e) {
      this._times = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "args", {
    get: function () {
      return this._args;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "enable", {
    get: function () {
      return !(this._target instanceof $2BaseEntity.default && !this._target.isActive) && (this._times > 0 || -1 == this.times);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setTag = function (e) {
    this.tag = e;
    return this;
  };
  _ctor.prototype._setId = function (e) {
    this._id = e;
  };
  _ctor.prototype.initWithCallback = function (e, t, o, i, n, r) {
    undefined === i && (i = null);
    undefined === n && (n = null);
    undefined === r && (r = 1);
    r < 0 && (r = -1);
    this._nextTime = e;
    this._delay = t;
    this._times = r;
    this._func = o;
    this._args = i;
    this._target = n;
    this.tag = undefined;
  };
  _ctor.prototype.cancel = function (e) {
    undefined === e && (e = false);
    e && this._callBack();
    this._times = 0;
    this._args = undefined;
    this._func = undefined;
    this._args = undefined;
    this._target = undefined;
    this.tag = undefined;
  };
  _ctor.prototype._callBack = function () {
    if (this.enable) {
      if (this.times > 0) {
        --this._times, this._nextTime = this.nextTime + this.delay;
      } else if (-1 == this.times) {
        this._nextTime = this.nextTime + this.delay;
      } else {
        $2Log.Log.error("Watcher._CallBack times error:", this.times);
      }
      null != this._func && this._func.call(this._target, this._args);
    }
  };
  _ctor.prototype.toString = function () {
    var e = "[Watcher] index:" + this.id + " time: " + this.nextTime + " times: " + this.times;
    if (null == this._args) {
      e += " func: " + this._func;
    } else {
      e += " argfunc: " + this._func + " " + this._args;
    }
    return e;
  };
  return _ctor;
}();
exports.Watcher = exp_Watcher;