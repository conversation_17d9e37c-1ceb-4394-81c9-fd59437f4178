var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.State = undefined;
var $2GameUtil = require("GameUtil");
(function (e) {
  var t;
  var o = cc.Vec2.ZERO;
  (function (e) {
    e[e.NONE = -1] = "NONE";
    e[e.IDLE = 0] = "IDLE";
    e[e.MOVE = 1] = "MOVE";
    e[e.ATTACK = 2] = "ATTACK";
    e[e.GLOBAL = 3] = "GLOBAL";
    e[e.BEHIT = 4] = "BEHIT";
    e[e.DEAD = 5] = "DEAD";
    e[e.SPRINT = 6] = "SPRINT";
    e[e.ESCAPE = 7] = "ESCAPE";
    e[e.SKILL = 8] = "SKILL";
    e[e.WANDER = 9] = "WANDER";
    e[e.FOLLOW = 10] = "FOLLOW";
    e[e.THINK = 11] = "THINK";
    e[e.HIDE = 12] = "HIDE";
    e[e.APPEAR = 13] = "APPEAR";
  })(t = e.Type || (e.Type = {}));
  var i = function () {
    function e(e, o) {
      undefined === o && (o = true);
      this._isReEnter = true;
      this._transitionMap = {};
      this._stateMap = {};
      this._curSubStateTag = t.NONE;
      this._preSubStateTag = t.NONE;
      this._parentStateTag = t.NONE;
      this._duration = 0;
      this._owner = e;
      this.isReEnter = o;
    }
    Object.defineProperty(e.prototype, "curStateTag", {
      get: function () {
        return this.tag;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "curSubStateTag", {
      get: function () {
        return this._curSubStateTag;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "isReEnter", {
      get: function () {
        return this._isReEnter;
      },
      set: function (e) {
        this._isReEnter = e;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.setParentState = function (e) {
      this._parentState = e;
    };
    e.prototype.addTransition = function (e) {
      var t = this._transitionMap[e.fromTag];
      if (t) {
        var o = 0;
        for (var i = t.length; o < i; o++) {
          var n = t[o];
          if (n.fromTag == e.fromTag && n.toTag == e.toTag) {
            return false;
          }
        }
        t.push(e);
      } else {
        (t = this._transitionMap[e.fromTag] = []).push(e);
      }
      return true;
    };
    e.prototype.onSteerMove = function (e, t) {
      var i = e.steering.calculate();
      cc.Vec2.multiplyScalar(o, i, t);
      cc.Vec2.add(o, o, e.velocity);
      $2GameUtil.GameUtil.Vec2Truncate(o, e.maxSpeed);
      e.velocity.set(o);
      cc.Vec2.multiplyScalar(o, e.velocity, t);
      cc.Vec2.add(o, o, e.position);
      e.enforceNonPeretration(o);
      e.setPosition(o);
      if (e.velocity.magSqr() > 1e-5) {
        cc.Vec2.normalize(o, e.velocity);
        e.heading = o;
      }
      e.updateDir(t);
    };
    e.prototype.getTransition = function (e, t) {
      var o = this._transitionMap[e];
      var i = null;
      if (o) {
        var n = 0;
        for (var r = o.length; n < r; n++) {
          var a = o[n];
          if (a.fromTag == e && a.toTag == t) {
            i = a;
            break;
          }
        }
      }
      return i;
    };
    e.prototype.checkTransition = function () {
      var e = t.NONE;
      var o = this._transitionMap[this._curSubStateTag];
      var i = 0;
      for (var n = o.length; i < n; i++) {
        var r = o[i];
        if (r.onCheck(this._owner) && r.onCompletaCallBack(this._owner)) {
          e = r.toTag;
          break;
        }
      }
      e != t.NONE && this.changeState(e);
    };
    e.prototype.onSteMove = function (e, t) {
      var i = e.steering.calculate();
      cc.Vec2.multiplyScalar(o, i, t);
      cc.Vec2.add(o, o, e.velocity);
      $2GameUtil.GameUtil.Vec2Truncate(o, e.maxSpeed);
      e.velocity.set(o);
      cc.Vec2.multiplyScalar(o, e.velocity, t);
      cc.Vec2.add(o, o, e.position);
      e.enforceNonPeretration(o);
      this.moveLimtX && (o.x = cc.misc.clampf(o.x, this.moveLimtX[0], this.moveLimtX[1]));
      this.moveLimtY && (o.y = cc.misc.clampf(o.y, this.moveLimtY[0], this.moveLimtY[1]));
      e.setPosition(o);
      if (e.velocity.magSqr() > 1e-5) {
        cc.Vec2.normalize(o, e.velocity);
        e.heading = o;
      }
      e.updateDir(t);
    };
    e.prototype.setMoveLimt = function (e, t) {
      this.moveLimtX = e;
      this.moveLimtY = t;
    };
    e.prototype.addState = function (e) {
      if (this._stateMap[e.tag]) {
        return console.warn("repeat addState Tag = ", e.tag), false;
      } else {
        return this._stateMap[e.tag] = e, e.setParentState(this), this._transitionMap[e.tag] || (this._transitionMap[e.tag] = []), true;
      }
    };
    e.prototype.cleanState = function (e) {
      this._stateMap[e] && delete this._stateMap[e];
    };
    e.prototype.getStateByTag = function (e) {
      return this._stateMap[e];
    };
    e.prototype.onEnter = function (e) {
      var t = [];
      for (var o = 1; o < arguments.length; o++) {
        t[o - 1] = arguments[o];
      }
      var i = this._stateMap[this._curSubStateTag];
      i && i.onEnter(e);
      this.time = 0;
    };
    e.prototype.onUpdate = function (e, t) {
      var o = this._stateMap[this._curSubStateTag];
      o && o.onUpdate(e, t);
      this.time += t;
    };
    e.prototype.onExit = function (e) {
      var o = this._stateMap[this._curSubStateTag];
      if (o) {
        o.onExit(e);
        this._preSubStateTag = this._curSubStateTag;
      } else {
        this._preSubStateTag = t.NONE;
      }
    };
    e.prototype.onMessage = function (e, t) {
      var o = this._stateMap[this._curSubStateTag];
      return !!o && o.onMessage(e, t);
    };
    e.prototype.changeState = function (e, t, o) {
      if (e < 0) {
        console.warn("<State call changeState>:trying to assign null state to current");
      } else if (this._stateMap[e]) {
        if (this._curSubStateTag != e || this._stateMap[this._curSubStateTag].isReEnter) {
          this._preSubStateTag = this._curSubStateTag, this._stateMap[this._curSubStateTag] && this._stateMap[this._curSubStateTag].onExit(this._owner), this._curSubStateTag = e, this._stateMap[this._curSubStateTag] && this._stateMap[this._curSubStateTag].onEnter(this._owner, t, o);
        }
      } else {
        cc.warn(e + " 状态未定义");
      }
    };
    e.prototype.revertToPreviousState = function () {
      this.changeState(this._preSubStateTag);
    };
    e.prototype.isInState = function (e) {
      return e == this._curSubStateTag;
    };
    e.prototype.isHasState = function (e) {
      return e.indexOf(this._curSubStateTag) >= 0;
    };
    Object.defineProperty(e.prototype, "curState", {
      get: function () {
        return this._stateMap[this._curSubStateTag];
      },
      enumerable: false,
      configurable: true
    });
    return e;
  }();
  e.BaseModel = i;
  var a = function () {
    function e(e, t) {
      this._fromTag = e;
      this._toTag = t;
    }
    Object.defineProperty(e.prototype, "fromTag", {
      get: function () {
        return this._fromTag;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(e.prototype, "toTag", {
      get: function () {
        return this._toTag;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.onCheck = function () {
      return true;
    };
    e.prototype.onCompletaCallBack = function () {
      return true;
    };
    return e;
  }();
  e.StateTransition = a;
  var s = function (e) {
    function o(t, o) {
      var i = e.call(this, t) || this;
      i._param = null;
      i._globalState = null;
      i._param = o;
      return i;
    }
    cc__extends(o, e);
    Object.defineProperty(o.prototype, "tag", {
      get: function () {
        return 0;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.update = function (e) {
      if (this._stateMap[this._curSubStateTag] && this._curSubStateTag != t.NONE) {
        this._stateMap[this._curSubStateTag].onUpdate(this._owner, e);
        this.checkTransition();
      }
      this._globalState && this._globalState.onUpdate(this._owner, e);
    };
    o.prototype.handleMessage = function (e) {
      return !!(this._curSubStateTag && this._stateMap[this._curSubStateTag] && this._stateMap[this._curSubStateTag].onMessage(this._owner, e)) || !(!this._globalState || !this._globalState.onMessage(this._owner, e));
    };
    o.prototype.registerGlobalState = function (e) {
      this._globalState = e;
      this._globalState.setParentState(this);
    };
    o.prototype.get = function (e) {
      return this._param && this._param[e];
    };
    o.prototype.set = function (e, t) {
      this._param && (this._param[e] = t);
    };
    return o;
  }(i);
  e.Machine = s;
})(exports.State || (exports.State = {}));