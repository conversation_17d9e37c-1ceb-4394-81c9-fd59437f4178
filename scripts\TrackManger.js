Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TrackManger = undefined;
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2TrackItem = require("TrackItem");
(function (e) {
  var t;
  var o;
  (function (e) {
    e[e.RewardBuild = 1] = "RewardBuild";
    e[e.RewardRole = 2] = "RewardRole";
    e[e.RewardPet = 3] = "RewardPet";
    e[e.Prop = 4] = "Prop";
    e[e.PropMagnet = 5] = "PropMagnet";
    e[e.PropHp = 6] = "PropHp";
    e[e.PropBox = 7] = "PropBox";
  })(o = e.Type || (e.Type = {}));
  e.MsgList = ((t = {})[o.RewardBuild] = {
    icon: "",
    bg: "v1/images/fight/icon/tpdg_bg_frame_2",
    arrow: "v1/images/fight/icon/tpdg_bg_frame_goldarrow"
  }, t[o.RewardRole] = {
    icon: "",
    bg: "v1/images/fight/icon/tpdg_bg_frame_1",
    arrow: "v1/images/fight/icon/tpdg_bg_frame_goldarrow"
  }, t[o.RewardPet] = {
    icon: "",
    bg: "v1/images/fight/icon/tpdg_bg_frame_2",
    arrow: "v1/images/fight/icon/tpdg_bg_frame_goldarrow"
  }, t[o.PropBox] = {
    icon: "",
    bg: "v1/images/fight/icon/tpdg_bg_suiji",
    arrow: "v1/images/fight/icon/tpdg_bg_suiji_jiantou"
  }, t[o.PropHp] = {
    icon: "",
    bg: "v1/images/fight/icon/tpdg_bg_suiji",
    arrow: "v1/images/fight/icon/tpdg_bg_suiji_jiantou"
  }, t[o.PropMagnet] = {
    icon: "",
    bg: "v1/images/fight/icon/tpdg_bg_suiji",
    arrow: "v1/images/fight/icon/tpdg_bg_suiji_jiantou"
  }, t);
  var s = function () {
    function e(t) {
      this.view = t;
      this.trackList = new Map();
      this.changeListener(true);
      e._instance = this;
    }
    Object.defineProperty(e, "instance", {
      get: function () {
        return e._instance;
      },
      enumerable: false,
      configurable: true
    });
    e.prototype.addTrack = function (e) {
      var t = this;
      if (this.trackList.has(e.trackType)) {
        this.trackList.get(e.trackType).addTarget(e);
      } else {
        $2Manager.Manager.loader.loadPrefab("ui/fight/TrackItem").then(function (o) {
          o.setParent(t.view);
          var i = o.getComponent($2TrackItem.default);
          t.trackList.set(e.trackType, i);
          i.addTarget(e);
        });
      }
    };
    e.prototype.onDestroy = function () {
      this.changeListener(false);
    };
    e.prototype.changeListener = function (e) {
      $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_SetTrack, this.addTrack, this);
    };
    e._instance = null;
    return e;
  }();
  e.Mgr = s;
})(exports.TrackManger || (exports.TrackManger = {}));