Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UIManager = undefined;
var i;
var $2Log = require("Log");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var exp_UIManager = function () {
  function _ctor() {
    this._loadingCount = 0;
    this._startLoadingCount = 0;
    this.delayViewID = [];
    this._views = {};
    this._viewQueues = [];
    for (var e = 0; e < $2MVC.MVC.eUIQueue.None; e++) {
      this._viewQueues[e] = new Array();
    }
    $2MVC.MVC.ViewHandler.initUIEvent(this.onOpen.bind(this), this.onClose.bind(this));
  }
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      return i;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.CloseAllByLayer = function (e) {
    i.closeAllByLayer(e);
  };
  _ctor.Init = function () {
    i || (i = new _ctor()).initRoot();
  };
  _ctor.RegisterViewType = function () {};
  _ctor.getView = function (e) {
    return i.getView(e);
  };
  _ctor.getTopViewByQueue = function (e) {
    return i.getTopViewByQueue(e);
  };
  _ctor.Open = function (e, t) {
    i.open(e, t);
  };
  _ctor.OpenInQueue = function (e, t, o) {
    undefined === o && (o = 9999);
    this.queue.push({
      type: e,
      args: t,
      sort: o
    });
    1 == this.queue.length && i.open(e, t);
  };
  _ctor.cleanQueue = function () {
    this.queue.length = 0;
  };
  _ctor.StoreInQueue = function (e, t, o) {
    undefined === o && (o = 999);
    this.queue.push({
      type: e,
      args: t,
      sort: o
    });
  };
  _ctor.OpenInQueueByFirst = function () {
    this.queue.sort(function (e, t) {
      return e.sort - t.sort;
    });
    this.queue[0] && i.open(this.queue[0].type, this.queue[0].args);
  };
  _ctor.Close = function (e, t) {
    i.close(e, t);
  };
  _ctor.CloseQueues = function (e) {
    i.closeQueues(e);
  };
  _ctor.layerRoots = function (e) {
    undefined === e && (e = $2MVC.MVC.eUILayer.Panel);
    return i.getLayerRoots(e);
  };
  _ctor.getViewCountByLayerIndex = function (t) {
    return _ctor.viewCountList[t] || 0;
  };
  _ctor.addSubCanvas = function (e) {
    return i.addSubCanvas(e);
  };
  _ctor.resetPosition = function () {
    i.resetPosition();
  };
  _ctor.update = function (e) {
    null == i || i.update(e);
  };
  Object.defineProperty(_ctor.prototype, "views", {
    get: function () {
      return this._views;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initRoot = function () {
    var e = this;
    this._root = new cc.Node("_UIRoot");
    cc.view.on("design-resolution-changed", function () {
      e.resetPosition();
    });
    this._root.parent = cc.director.getScene();
    this._root.width = cc.winSize.width;
    this._root.height = cc.winSize.height;
    this._root.position = cc.v2(.5 * cc.winSize.width, .5 * cc.winSize.height);
    cc.game.addPersistRootNode(this._root);
    this._layerRoots = new Array();
    for (var t = $2MVC.MVC.eUILayer.Scene; t < $2MVC.MVC.eUILayer.Max; t++) {
      this._layerRoots[t] = this.addSubCanvas($2MVC.MVC.eUILayer[t]);
    }
    this.resetPosition();
  };
  _ctor.prototype.addSubCanvas = function (e) {
    var t = new cc.Node(e + "_Root");
    t.parent = this._root;
    t.width = cc.winSize.width;
    t.height = cc.winSize.height;
    t.position = cc.Vec2.ZERO;
    t.angle = 0;
    return t;
  };
  _ctor.prototype.resetPosition = function () {
    var e = cc.view.getDesignResolutionSize();
    var t = e.width;
    var o = e.height;
    var i = Math.min(cc.view.getCanvasSize().width / t, cc.view.getCanvasSize().height / o);
    var n = t * i;
    var a = o * i;
    var s = t * (cc.view.getCanvasSize().width / n);
    var c = o * (cc.view.getCanvasSize().height / a);
    this._root.width = s;
    this._root.height = c;
    for (var l = $2MVC.MVC.eUILayer.Scene; l < $2MVC.MVC.eUILayer.Max; l++) {
      var u = this._layerRoots[l];
      u.width = s;
      u.height = c;
      u.position = cc.Vec2.ZERO;
      u.angle = 0;
    }
  };
  _ctor.prototype.cleanDelayView = function () {
    this.delayViewID.forReverse(function (e) {
      return $2Time.Time.timeDelay.cancelBy(e);
    });
    this.delayViewID.length = 0;
  };
  _ctor.prototype.open = function (e, t) {
    var o = this;
    undefined === t && (t = $2MVC.MVC.openArgs().setIsNeedLoading(true));
    var i = e.split("/");
    var n = i[i.length - 1];
    var c = $2Time.Time.delay(t.dailyTime, function () {
      if (null == o._views[e]) {
        o._views[e] = {
          asset: e,
          args: t,
          node: null
        };
        if (t.isNeedLoading) {
          o._loadingCount++;
          o._startLoadingCount <= 0 && (o._startLoadingCount = .1);
        }
        $2MVC.MVC.ViewHandler.loadAssetHandler(e, cc.Prefab, function (t, i) {
          if (o._views[e].args.isNeedLoading) {
            o._loadingCount--;
            if (o._loadingCount <= 0) {
              $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false), o._startLoadingCount = 0;
            }
          }
          if (t) {
            console.error(".loadCallback GameObject null:" + e);
            return void (o._views[e] = null);
          }
          o.onLoadCallback(i, e);
        });
      } else if (o._views[e].node) {
        var i = o._views[e].node.getComponent($2MVC.MVC.BaseView);
        t || (t = $2MVC.MVC.openArgs());
        i.setOpenArgs(t);
        i.open();
      }
    });
    c.setTag(n);
    this.delayViewID.push(c.id);
  };
  _ctor.prototype.getView = function (e) {
    var t = this._views[e];
    if (t && t.node && cc.isValid(t.node)) {
      return t.node.getComponent($2MVC.MVC.BaseView);
    } else {
      return null;
    }
  };
  _ctor.prototype.onOpen = function (t) {
    var o = t.uiLayer;
    _ctor.viewCountList[o] || (_ctor.viewCountList[o] = 0);
    _ctor.viewCountList[o]++;
    if (t.uiQueue != $2MVC.MVC.eUIQueue.None) {
      var i = this._viewQueues[t.uiQueue];
      if (i.length > 0) {
        var n = i[i.length - 1];
        n.isShowed && n.hide();
      }
      i.push(t);
    }
  };
  _ctor.prototype.close = function (e, t) {
    var o = this._views[e];
    if (null != o && null != o.node) {
      o.node.getComponent($2MVC.MVC.BaseView).close(t);
    } else {
      $2Log.Log.error("UIManager.close:" + e + " null");
    }
  };
  _ctor.prototype.getTopViewByQueue = function (e) {
    var t = this._viewQueues[e];
    if (t && t.length > 0) {
      return t[t.length - 1];
    } else {
      return null;
    }
  };
  _ctor.prototype.onClose = function (t) {
    var o = t.uiLayer;
    _ctor.viewCountList[o]--;
    _ctor.viewCountList[o] < 0 && (_ctor.viewCountList[o] = 0);
    var i = t.assetPath;
    var a = function (t) {
      if (_ctor.queue[t].type == i && (_ctor.queue.splice(t, 1), _ctor.queue.length >= 1)) {
        var o = 0;
        _ctor.queue[t].type == i && (o = .05);
        $2Time.Time.delay(o, function () {
          _ctor.Open(_ctor.queue[t].type, _ctor.queue[t].args);
        });
        return "break";
      }
    };
    for (var s = 0; s < _ctor.queue.length && "break" !== a(s); s++) {
      ;
    }
    this._views[t.assetPath] = null;
    if (t.uiQueue != $2MVC.MVC.eUIQueue.None) {
      var c = this._viewQueues[t.uiQueue];
      if (!(c.length <= 0)) {
        var u = c[c.length - 1];
        if (u != t) {
          var p = c.indexOf(t);
          var f = true;
          p < 0 && (f = false);
          c.splice(p, 1);
          return void (f || $2Log.Log.warn("UIManager.onClose:" + t.assetPath + " can't find, last:" + u.assetPath));
        }
        c.pop();
        c.length > 0 && (u = c[c.length - 1]).show();
      }
    }
  };
  _ctor.prototype.resetRootPos = function () {
    this._root.width = cc.winSize.width;
    this._root.height = cc.winSize.height;
    this._root.position = cc.v2(.5 * cc.winSize.width, .5 * cc.winSize.height);
  };
  _ctor.prototype.getLayerRoots = function (e) {
    return this._layerRoots[e];
  };
  _ctor.prototype.closeAllByLayer = function (e) {
    for (var t in this._views) {
      var o = this._views[t];
      if (o && o.node) {
        var i = o.node.getComponent($2MVC.MVC.BaseView);
        if (i && i.uiQueue == $2MVC.MVC.eUIQueue.None && i.uiLayer == e) {
          i.close();
          this._views[t] = null;
        }
      }
    }
    this.closeQueues();
  };
  _ctor.prototype.onLoadCallback = function (e, t) {
    var o = this._views[t].args;
    o || (o = new $2MVC.MVC.OpenArgs());
    var i = $2Manager.Manager.loader.instantiate(e);
    var n = i.getComponent($2MVC.MVC.BaseView);
    var a = $2MVC.MVC.getTransitionByView(n);
    var s = $2MVC.MVC.getUILayerByView(n);
    var l = $2MVC.MVC.getUIQueueByView(n);
    var u = $2MVC.MVC.getViewBgmByView(n);
    n.init(s, l, new a(), t, u);
    n.setNodeInfo(this._layerRoots[s]);
    i.groupIndex = o.nodeGroup;
    this._views[t].node = i;
    n.setOpenArgs(o);
    n.open();
  };
  _ctor.prototype.closeQueues = function (e) {
    if (undefined !== e && e != $2MVC.MVC.eUIQueue.None) {
      this.closeQueue(e);
    } else {
      for (var t = 0; t < this._viewQueues.length; t++) {
        this.closeQueue(t);
      }
    }
  };
  _ctor.prototype.closeQueue = function (e) {
    if (!(this._viewQueues[e].length <= 0)) {
      var t = copy(this._viewQueues[e]);
      this._viewQueues[e] = new Array();
      for (var o = 0; o < t.length; o++) {
        t[o].close();
      }
    }
  };
  _ctor.prototype.update = function (e) {
    if (this._startLoadingCount > 0) {
      this._startLoadingCount -= e;
      this._startLoadingCount <= 0 && this._loadingCount > 0 && $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, true);
    }
  };
  _ctor.m_func2viewTypes = {};
  _ctor.queue = [];
  _ctor.viewCountList = {};
  return _ctor;
}();
exports.UIManager = exp_UIManager;