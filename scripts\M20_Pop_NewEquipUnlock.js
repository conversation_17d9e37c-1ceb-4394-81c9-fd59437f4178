var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2WonderSdk = require("WonderSdk");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_M20_Pop_NewEquipUnlock = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.img = null;
    t.gname = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  _ctor.prototype.onOpen = function () {
    var e = this;
    var t = $2Cfg.Cfg.RoleUnlock.find({
      id: this.param.id
    });
    $2Manager.Manager.loader.loadSpriteAsync(t.icon).then(function (t) {
      e.img.spriteFrame = t;
    });
    this.gname.string = t.roleName;
  };
  _ctor.prototype.onClose = function () {
    var e;
    var t;
    null === (t = (e = this.param).cb) || undefined === t || t.call(e);
    if (15 == $2Manager.Manager.vo.userVo.guideIndex && $2UIManager.UIManager.queue.length <= 1) {
      var o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
      $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
        targetNode: o.toggleContainer.toggleItems[3].node
      });
    }
    var i = $2WonderSdk.WonderSdk._instance.isGoogleAndroid;
    5 == this.mode.rVo.curPassLv && i && $2Manager.Manager.vo.userVo.ispoplikegame && $2UIManager.UIManager.OpenInQueue("ui/ModeCatGame/M3_PopLikeGameView", $2MVC.MVC.openArgs());
  };
  _ctor.prototype.setInfo = function () {};
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "img", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "gname", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_Pop_NewEquipUnlock"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_Pop_NewEquipUnlock;