Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PoolArray = undefined;
var exp_PoolArray = function () {
  function _ctor() {
    this.max_count = 0;
    this.template = null;
    this.parent = null;
    this.firstLoad = [];
    this.m_item_list = {};
  }
  _ctor.prototype.hasLoadObj = function (e) {
    var t = e % this.max_count;
    return this.m_item_list[t];
  };
  _ctor.prototype.getObj = function (e, t) {
    undefined === t && (t = true);
    if (0 === this.max_count) {
      cc.log("最大容量必须大于0");
      return null;
    }
    var o = e % this.max_count;
    var i = null;
    if (this.m_item_list[o]) {
      i = this.m_item_list[o];
    } else {
      i = cc.instantiate(this.template);
      this.m_item_list[o] = i;
      i.parent = this.parent;
      var n = 0;
      for (var r = this.firstLoad; n < r.length; n++) {
        (0, r[n])(i, e, o);
      }
    }
    t && (i.active = true);
    return i;
  };
  _ctor.prototype.hideOther = function (e) {
    var t = e;
    t < 0 && (t = 0);
    var o = Object.keys(this.m_item_list).length;
    for (var i = t; i < o; i++) {
      null !== this.m_item_list[i] && (this.m_item_list[i].active = false);
    }
  };
  _ctor.prototype.clear = function () {
    for (var e in this.m_item_list) {
      Object.prototype.hasOwnProperty.call(this.m_item_list, e) && delete this.m_item_list[e];
    }
  };
  return _ctor;
}();
exports.PoolArray = exp_PoolArray;