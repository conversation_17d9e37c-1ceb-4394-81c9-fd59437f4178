var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MonsterCfgReader = undefined;
var $1$2TConfig = require("TConfig");
var exp_MonsterCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "Monster";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($1$2TConfig.TConfig);
exports.MonsterCfgReader = exp_MonsterCfgReader;