Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LanguageFun = undefined;
var $2SdkConfig = require("SdkConfig");
var exp_LanguageFun = function () {
  function _ctor() {}
  _ctor.init = function (e, t) {
    cc.sys.language = e;
    console.log("[languageFun][init]语言包初始化");
    this.language = e;
    t();
  };
  _ctor.check = function (e) {
    if (cc.sys.language == cc.sys.LANGUAGE_CHINESE || "tc" == cc.sys.language) {
      return e;
    }
    var t = window.tpdg;
    if (t && t[e]) {
      return t[e][cc.sys.language] || t[e][cc.sys.LANGUAGE_ENGLISH];
    } else {
      return e;
    }
  };
  _ctor.language = $2SdkConfig.Language.zh;
  return _ctor;
}();
exports.LanguageFun = exp_LanguageFun;