var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FightUIView = undefined;
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc__decorator.menu;
cc.v2();
var exp_FightUIView = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    this.game.uiView = this;
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_ShowGameTips, this.showGameToast, this);
    $2Notifier.Notifier.changeCall(e, $2CallID.CallID.View_GetFightUIView, this.getFightUIView, this);
  };
  _ctor.prototype.pauseGame = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    $2UIManager.UIManager.OpenInQueue("ui/setting/SettingView", $2MVC.MVC.openArgs());
  };
  _ctor.prototype.getFightUIView = function () {
    return this;
  };
  _ctor.prototype.showGameToast = function (e, t) {
    var o = this;
    undefined === t && (t = "");
    $2Manager.Manager.loader.loadPrefab(m[e].TitleToast).then(function (e) {
      e.setAttribute({
        scale: 1,
        parent: o.node,
        y: .15 * o.node.height,
        opacity: 255
      });
      var ske = e.getComponentInChildren(sp.Skeleton);
      ske.setAnimation(0, "animation", false);
      cc.tween(e).delay(3).call(function () {
        e.destroy();
      }).start();
      // e.setAttribute({
      //   scale: 0,
      //   parent: o.node,
      //   y: .15 * o.node.height,
      //   opacity: 255
      // });
      // t && (e.getComByChild(cc.Label).string = t);
      // cc.tween(e).by(.2, {
      //   y: -100,
      //   scale: 1.2
      // }).to(.2, {
      //   scale: 1
      // }).delay(1).by(.3, {
      //   scale: -0,
      //   y: 100,
      //   opacity: -255
      // }).call(function () {
      //   e.destroy();
      // }).start();
    });
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2Pop.Pop);
exports.FightUIView = exp_FightUIView;
var m = {
  0: {
    TitleToast: "ui/fight/GameTitleToast_Start"
  },
  1: {
    TitleToast: "ui/fight/GameTitleToast_1"
  },
  2: {
    TitleToast: "ui/fight/GameTitleToast_2"
  },
  3: {
    TitleToast: "ui/fight/GameTitleToast_3"
  }
};