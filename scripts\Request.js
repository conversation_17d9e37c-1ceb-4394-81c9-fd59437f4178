Object.defineProperty(exports, "__esModule", {
  value: true
});
var def_Request = function () {
  function _ctor() {}
  _ctor.get = function (e) {
    if (window.tt) {
      e.method = "GET";
      e.header = {
        "content-type": "application/x-www-form-urlencoded"
      };
      return window.wxapi.request(e);
    }
  };
  _ctor.post = function (e) {
    if (window.tt) {
      e.method = "POST";
      e.header = {
        "content-type": "application/x-www-form-urlencoded"
      };
      return window.wxapi.request(e);
    }
  };
  return _ctor;
}();
exports.default = def_Request;