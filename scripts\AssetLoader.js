Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.assetLoader = exports.LoadResArgs = undefined;
var $2Log = require("Log");
var $2ResKeeper = require("ResKeeper");
var exp_LoadResArgs = function () {};
exports.LoadResArgs = exp_LoadResArgs;
var a = cc.js.isChildClassOf;
a || (a = cc.isChildClassOf);
var def_AssetLoader = function () {
  function _ctor() {
    this._resKeeper = null;
  }
  _ctor.makeLoadResArgs = function () {
    if (arguments.length < 1) {
      $2Log.Log.error("_makeLoadResArgs error " + arguments);
      return null;
    }
    if (1 == arguments.length && arguments[0] instanceof exp_LoadResArgs) {
      return arguments[0];
    }
    var e = {};
    if ("string" == typeof arguments[0]) {
      e.url = arguments[0];
    } else {
      if (!(arguments[0] instanceof Array)) {
        $2Log.Log.error("_makeLoadResArgs error " + arguments);
        return null;
      }
      e.urls = arguments[0];
    }
    for (var t = 1; t < arguments.length; ++t) {
      if (1 == t && a(arguments[t], cc.Asset)) {
        e.type = arguments[t];
      } else if (t == arguments.length - 1 && arguments[t] instanceof cc.AssetManager.Bundle) {
        e.bundle = arguments[t];
      } else if ("function" == typeof arguments[t]) {
        if (arguments.length > t + 1 && "function" == typeof arguments[t + 1]) {
          e.onProgess = arguments[t];
        } else {
          e.onCompleted = arguments[t];
        }
      }
    }
    e.bundle || (e.bundle = cc.resources);
    return e;
  };
  _ctor.prototype._finishItem = function () {};
  _ctor.prototype.loadRes = function () {
    var t;
    var o = this;
    var i = _ctor.makeLoadResArgs.apply(this, arguments);
    var n = function (e, n) {
      e || o._finishItem(i.url, i.type, i.use, t);
      i.onCompleted && i.onCompleted(e, n);
    };
    var r = i.bundle.get(i.url, i.type);
    if (r) {
      n(null, r);
    } else if (i.url.startsWith("https:")) {
      cc.assetManager.loadRemote(i.url, n);
    } else {
      i.bundle.load(i.url, i.type, i.onProgess, n);
    }
  };
  _ctor.prototype.preloadRes = function () {
    var t;
    var o = this;
    var i = _ctor.makeLoadResArgs.apply(this, arguments);
    var n = function (e, n) {
      e || o._finishItem(i.url, i.type, i.use, t);
      i.onCompleted && i.onCompleted(e, n);
    };
    var r = i.bundle.get(i.url, i.type);
    if (r) {
      n(null, r);
    } else {
      i.bundle.preload(i.url, i.type, i.onProgess, n);
    }
  };
  _ctor.prototype.preloadResDir = function () {
    var t;
    var o = this;
    var i = _ctor.makeLoadResArgs.apply(this, arguments);
    var n = function (e, n) {
      var r = i.bundle.getDirWithPath(i.url, i.type);
      !e && r && r.map(function (e) {
        o._finishItem(e.path, i.type, i.use, t);
      });
      i.onCompleted && i.onCompleted(e, n);
    };
    i.bundle.preloadDir(i.url, i.type, i.onProgess, n);
  };
  _ctor.prototype.loadArray = function () {
    var t;
    var o = this;
    var i = _ctor.makeLoadResArgs.apply(this, arguments);
    var n = function (e, n) {
      if (!e) {
        for (var r = 0; r < i.urls.length; ++r) {
          o._finishItem(i.urls[r], i.type, i.use, t);
        }
      }
      i.onCompleted && i.onCompleted(e, n);
    };
    i.bundle.load(i.urls, i.type, i.onProgess, n);
  };
  _ctor.prototype.loadResDir = function () {
    var t;
    var o = this;
    var i = _ctor.makeLoadResArgs.apply(this, arguments);
    var n = function (e, n) {
      var r = i.bundle.getDirWithPath(i.url, i.type);
      !e && r && r.map(function (e) {
        o._finishItem(e.path, i.type, i.use, t);
      });
      i.onCompleted && i.onCompleted(e, n);
    };
    i.bundle.loadDir(i.url, i.type, i.onProgess, n);
  };
  _ctor.prototype.releaseAsset = function (e) {
    e && cc.assetManager.releaseAsset(e);
  };
  _ctor.prototype.getResKeeper = function () {
    this._resKeeper || (this._resKeeper = new $2ResKeeper.default());
    return this._resKeeper;
  };
  return _ctor;
}();
exports.default = def_AssetLoader;
exports.assetLoader = new def_AssetLoader();