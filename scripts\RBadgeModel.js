var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RBadge = undefined;
var r;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Time = require("Time");
var $2GameUtil = require("GameUtil");
(function (e) {
  var t;
  var o;
  (function (e) {
    e[e.Shop = 1e3] = "Shop";
    e[e.Role = 1001] = "Role";
    e[e.Fight = 1002] = "Fight";
    e[e.Equip = 1003] = "Equip";
    e[e.Activity = 1004] = "Activity";
    e[e.Task = 1005] = "Task";
    e[e.Shop_FreeDiamond = 1006] = "Shop_FreeDiamond";
    e[e.Shop_FreeCoin = 1007] = "Shop_FreeCoin";
    e[e.Fight_NavReward = 1008] = "Fight_NavReward";
    e[e.Fight_DeskReward = 1009] = "Fight_DeskReward";
    e[e.Equip_FragmentUpgrade = 1010] = "Equip_FragmentUpgrade";
    e[e.Role_FragmentUpgrade = 1011] = "Role_FragmentUpgrade";
    e[e.Activity_Challenge = 1012] = "Activity_Challenge";
    e[e.Activity_Challenge_Num = 1013] = "Activity_Challenge_Num";
    e[e.Activity_Challenge_Reward = 1014] = "Activity_Challenge_Reward";
    e[e.Activity_ChallengeCoin = 1015] = "Activity_ChallengeCoin";
    e[e.Activity_ChallengeCoin_Num = 1016] = "Activity_ChallengeCoin_Num";
    e[e.Activity_ChallengeCoin_Reward = 1017] = "Activity_ChallengeCoin_Reward";
    e[e.TaskDay = 1018] = "TaskDay";
    e[e.TaskWeek = 1019] = "TaskWeek";
    e[e.TaskAchieve = 1020] = "TaskAchieve";
    e[e.DayTaskBox = 1021] = "DayTaskBox";
    e[e.DayTaskItem = 1022] = "DayTaskItem";
    e[e.WeekTaskBox = 1023] = "WeekTaskBox";
    e[e.WeekTaskItem = 1024] = "WeekTaskItem";
  })(o = e.Key || (e.Key = {}));
  e.Tree = ((t = {})[o.Shop] = [o.Shop_FreeCoin, o.Shop_FreeDiamond], t[o.Equip] = [o.Equip_FragmentUpgrade], t[o.Role] = [o.Role_FragmentUpgrade], t[o.Fight] = [o.Fight_NavReward, o.Fight_DeskReward], t[o.Activity] = [o.Activity_Challenge, o.Activity_ChallengeCoin], t[o.Activity_Challenge] = [o.Activity_Challenge_Num, o.Activity_Challenge_Reward], t[o.Activity_ChallengeCoin] = [o.Activity_ChallengeCoin_Num, o.Activity_ChallengeCoin_Reward], t[o.Task] = [o.TaskDay, o.TaskWeek, o.TaskAchieve], t[o.TaskDay] = [o.DayTaskBox, o.DayTaskItem], t[o.TaskWeek] = [o.WeekTaskBox, o.WeekTaskItem], t);
  var i = function () {
    function t(t) {
      for (var o in t) {
        this[o] = t[o];
      }
      this.children = [];
      this.isTree = !!e.Tree[this.key];
    }
    Object.defineProperty(t.prototype, "parent", {
      get: function () {
        return this._parent;
      },
      set: function (e) {
        this._parent = e;
        this._parent.children.push(this);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "childCont", {
      get: function () {
        return this.children.length;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.unuse = function () {
      this.parent && $2GameUtil.GameUtil.deleteArrItem(this.parent.children, this);
    };
    return t;
  }();
  e.Data = i;
})(r = exports.RBadge || (exports.RBadge = {}));
var def_RBadgeModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.pointList = [];
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setPoint = function (e, t, o) {
    var i = this;
    undefined === o && (o = 0);
    var n = this.getItem(e, o);
    if (t) {
      if (!n) {
        var a;
        var s = this.getPath(e);
        s = s.slice(0, s.length - 1);
        var c = [];
        s.forEach(function (e, t) {
          if (!(a = 0 == t ? i.pointList.find(function (t) {
            return t.key == e;
          }) : a.children.find(function (t) {
            return t.key == e;
          }))) {
            var o = s[t - 1];
            a = new r.Data({
              key: e,
              id: 0
            });
            if (o) {
              a.parent = c[t - 1];
            } else {
              i.pointList.push(a);
            }
          }
          c.push(a);
        });
        (a = new r.Data({
          key: e,
          id: o
        })).parent = c[c.length - 1];
        c.push(a);
      }
    } else if (n) {
      n.unuse();
      n = null;
    }
    this.updatBadge();
  };
  _ctor.prototype.getItem = function (e, t) {
    var o;
    var i = this;
    undefined === t && (t = 0);
    this.getPath(e).forEach(function (n, r) {
      if (0 == r) {
        o = i.pointList.find(function (e) {
          return e.key == n;
        });
      } else {
        o && (o = n == e ? o.children.find(function (e) {
          return e.key == n && e.id == t;
        }) : o.children.find(function (e) {
          return e.key == n;
        }));
      }
    });
    return o;
  };
  _ctor.prototype.getParent = function (e) {
    for (var t in r.Tree) {
      if (r.Tree[t].includes(e)) {
        return +t;
      }
    }
  };
  _ctor.prototype.getPath = function (e) {
    var t = [e];
    var o = this.getParent(e);
    if (!o) {
      return t;
    }
    t.push(o);
    for (var i = o; o;) {
      if (o = this.getParent(i)) {
        t.push(o);
        i = o;
      }
    }
    t.reverse();
    return t;
  };
  _ctor.prototype.getChildBadge = function (e) {
    var t;
    var o = this;
    var i = [e];
    null === (t = r.Tree[e]) || undefined === t || t.forEach(function (e) {
      i.push.apply(i, o.getChildBadge(e));
    });
    return i;
  };
  _ctor.prototype.updatBadge = function () {
    $2Time.Time.timeDelay.cancelBy(this.sTimer);
    this.sTimer = $2Time.Time.delay(.5, function () {
      $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Update);
    }).id;
  };
  _ctor.prototype.onLogin_Finish = function () {
    this.getItem(r.Key.Shop_FreeCoin);
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_RBadgeModel;