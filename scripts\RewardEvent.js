var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RewardEvent = undefined;
var $2ListenID = require("ListenID");
var $2Notifier = require("Notifier");
var $2GameUtil = require("GameUtil");
var $2NodePool = require("NodePool");
var $2Game = require("Game");
var $2NPC = require("NPC");
var $2TrackManger = require("TrackManger");
var $2CompManager = require("CompManager");
(function (e) {
  var t;
  var o;
  var f;
  var h;
  (function (e) {
    e[e.Not = 0] = "Not";
    e[e.LvUPSkill = 1] = "LvUPSkill";
    e[e.DroppedBuff = 2] = "DroppedBuff";
    e[e.Skill = 7] = "Skill";
    e[e.SkillBuff = 8] = "SkillBuff";
    e[e.Buff = 9] = "Buff";
    e[e.Role = 20] = "Role";
    e[e.Pet = 21] = "Pet";
    e[e.RandomBuild_998 = 998] = "RandomBuild_998";
    e[e.RandomBuild_999 = 999] = "RandomBuild_999";
    e[e.FiringBuff = 1e3] = "FiringBuff";
  })(f = e.Type || (e.Type = {}));
  e.RandomBuild = ((t = {})[f.RandomBuild_998] = [f.Skill, f.SkillBuff], t[f.RandomBuild_999] = [f.Skill, f.SkillBuff, f.Buff], t);
  e.TrackMsg = ((o = {})[f.Skill] = {
    trackType: $2TrackManger.TrackManger.Type.RewardBuild,
    icon: "v1/images/fight/icon/actskill_icon"
  }, o[f.SkillBuff] = {
    trackType: $2TrackManger.TrackManger.Type.RewardBuild,
    icon: "v1/images/fight/icon/passskill_icon"
  }, o[f.Buff] = {
    trackType: $2TrackManger.TrackManger.Type.RewardBuild,
    icon: "v1/images/fight/icon/passskill_icon"
  }, o[f.Role] = {
    trackType: $2TrackManger.TrackManger.Type.RewardRole,
    icon: "img/role/touxiang05"
  }, o[f.Pet] = {
    trackType: $2TrackManger.TrackManger.Type.RewardPet,
    icon: "v1/images/fight/icon/pet_fw_icon"
  }, o);
  (function (e) {
    e[e.Order = 1] = "Order";
    e[e.Fix = 2] = "Fix";
  })(h || (h = {}));
  var d = function () {
    function t(e) {
      var t;
      this.eventList = ((t = {})[h.Order] = {
        time: 0,
        list: []
      }, t[h.Fix] = {
        time: 0,
        list: []
      }, t);
      this.bronMgr = e;
    }
    Object.defineProperty(t.prototype, "orderEvent", {
      get: function () {
        return this.eventList[h.Order];
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "fixEvent", {
      get: function () {
        return this.eventList[h.Fix];
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "game", {
      get: function () {
        return $2Game.Game.Mgr.instance;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.createEventList = function () {
      var t;
      var o = this;
      var i = $2Game.ModeCfg.Skiilpool.get(this.bronMgr.level);
      null === (t = JSON.parse(i.element)) || undefined === t || t.forEach(function (t) {
        var i = t[0];
        i > 900 && (i = $2GameUtil.GameUtil.getRandomInArray(e.RandomBuild[i], 1)[0]);
        var n = {
          type: i,
          time: t[1] + $2GameUtil.GameUtil.random(-5, 5),
          timeType: $2GameUtil.GameUtil.CheckSection(7, i, 10) ? h.Order : h.Fix
        };
        o.eventList[n.timeType].list.push(n);
      });
      for (var n in this.eventList) {
        this.eventList[n].list.sort(function (e, t) {
          return e.time - t.time;
        });
      }
    };
    t.prototype.onUpdate = function (e) {
      if (this.orderEvent.list.length > 0 && !this._orderTarget) {
        this.orderEvent.time += e;
        this.orderEvent.time >= this.orderEvent.list[0].time && this.createNpc(this.orderEvent.list[0], this.bronMgr.getRandomPos(1500));
      } else {
        this._orderTarget && !this._orderTarget.target.isValid && (this._orderTarget = null);
      }
      this.fixEvent.time += e;
      this.fixEvent.list.length > 0 && this.fixEvent.time >= this.fixEvent.list[0].time && this.createNpc(this.fixEvent.list[0], this.bronMgr.getRandomPos(1500));
    };
    t.prototype.createNpc = function (t, o) {
      var a = this;
      return new Promise(function () {
        var c = a.eventList[t.timeType].list.splice(0, 1)[0];
        cc.log("创建RewardEvent", c);
        $2NodePool.NodePool.spawn("entity/fight/RewardEvent_" + c.type).setNodeAssetFinishCall(function (s) {
          var u = s.getComponent($2NPC.default);
          s.parent = a.bronMgr.game._entityNode;
          u.setPosition(cc.v2(o.x, o.y));
          u.init();
          u.set(c.type);
          c.target = u;
          a._orderTarget = c;
          $2CompManager.default.Instance.registerComp(u);
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetTrack, cc__assign({
            node: s
          }, e.TrackMsg[t.type]));
        });
      });
    };
    return t;
  }();
  e.Manager = d;
})(exports.RewardEvent || (exports.RewardEvent = {}));