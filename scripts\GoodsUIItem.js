var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2StorageID = require("StorageID");
var $2GameUtil = require("GameUtil");
var $2EnergyStamp = require("EnergyStamp");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_GoodsUIItem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.Ename = "";
    t.KnapsackName = $2StorageID.StorageID.KnapsackData;
    t.setlab = null;
    t.iconWordPos = cc.v2();
    return t;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  _ctor.get = function (e) {
    return this.List.get(e).slice(-1)[0];
  };
  _ctor.getIconPos = function (e) {
    var t;
    return (null === (t = o.get(e)) || undefined === t ? undefined : t.iconWordPos) || cc.v2(0, 800);
  };
  _ctor.getNode = function (e) {
    var t;
    return (null === (t = o.get(e)) || undefined === t ? undefined : t.node) || null;
  };
  _ctor.prototype.onLoad = function () {
    this.label = this.setlab;
    this.setlab || (this.label = this.node.getComByChild(cc.Label));
    this.label || (this.label = this.node.getComponentInChildren(cc.Label));
  };
  _ctor.prototype.onEnable = function () {
    var e = $2Notifier.Notifier.call($2CallID.CallID.Fight_GetKnapsackMgr, this.KnapsackName);
    if (!e) {
      return cc.warn(this.KnapsackName + "背包没创建");
    }
    this.set(this.KnapsackName, this.Ename, e);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(false);
    $2GameUtil.GameUtil.deleteArrItem(o.List.get(+this.Ename), this);
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.onGoodsChange, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.ScreenMatch_Finish, this.onScreenMatch_Finish, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Shop_InfoUpDate, this.onShop_InfoUpDate, this);
  };
  _ctor.prototype.set = function (e, t, i) {
    this.KnapsackName = e;
    this.Ename = t;
    this.myKnapsackMgr = i;
    this.label.string = this.value;
    o.List.has(+this.Ename) || o.List.set(+this.Ename, []);
    o.List.get(+this.Ename).push(this);
    this.changeListener(true);
    this.iconWordPos.set(this.node.getChildByName("head").wordPos);
    this.onShop_InfoUpDate();
  };
  Object.defineProperty(_ctor.prototype, "value", {
    get: function () {
      return Math.trunc(this.myKnapsackMgr.getVal(this.Ename));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onShop_InfoUpDate = function () {
    +this.Ename == $2CurrencyConfigCfg.CurrencyConfigDefine.Energy && (this.node.getComByPath(cc.Label, "limt").string = "/" + $2EnergyStamp.default.EnergyLimt);
  };
  _ctor.prototype.onScreenMatch_Finish = function () {
    this.iconWordPos.set(this.node.getChildByName("head").wordPos);
  };
  _ctor.prototype.onGoodsChange = function (e, t) {
    this.KnapsackName == e && this.Ename == t && (this.label.string = this.value);
  };
  Object.defineProperty(_ctor.prototype, "wordPos", {
    get: function () {
      return this.node.worldPosition;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.openView = function (e, t) {
    t.includes("ui/") && $2UIManager.UIManager.Open(t, $2MVC.MVC.openArgs());
  };
  _ctor.List = new Map();
  cc__decorate([ccp_property({
    displayName: "货币唯一标示"
  })], _ctor.prototype, "Ename", undefined);
  cc__decorate([ccp_property({
    displayName: "背包名称"
  })], _ctor.prototype, "KnapsackName", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "setlab", undefined);
  return o = cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Common/GoodsUIItem")], _ctor);
}(cc.Component);
exports.default = def_GoodsUIItem;