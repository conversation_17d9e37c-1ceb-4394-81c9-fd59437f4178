var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();
var def_Bullet_Path = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.path = [];
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.set = function (e) {
    var t;
    this.path.length = 0;
    (t = this.path).push.apply(t, $2GameUtil.GameUtil.deepCopy(e));
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this._vo.lifeTime < 0 || this.isDead || 0 != this.path.length && (p.setVal(this.path[0].x, this.path[0].y), cc.Vec2.squaredDistance(p, this.position) < 400 && (this.path.splice(0, 1), p.setVal(this.path[0].x, this.path[0].y)), this.vo.shootDir.set(p.sub(this.position).normalize()), cc.Vec2.multiplyScalar(p, this._vo.shootDir, this.maxSpeed * t), cc.Vec2.add(p, this.position, p), this.setPosition(p), this.isBanRotate || 0 != this.isRotate || this.updateDir(t));
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_Path")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_Path;