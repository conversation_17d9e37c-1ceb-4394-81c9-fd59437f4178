var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TwoDHorizontalLayoutObject = undefined;
var $1$2TwoDLayoutObject = require("TwoDLayoutObject");
var exp_TwoDHorizontalLayoutObject = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.getRowByIndex = function (t) {
    return e.prototype.getColumnByIndex.call(this, t);
  };
  _ctor.prototype.getColumnByIndex = function (t) {
    return e.prototype.getRowByIndex.call(this, t);
  };
  _ctor.prototype.rows = function () {
    return e.prototype.columns.call(this);
  };
  _ctor.prototype.columns = function () {
    return e.prototype.rows.call(this);
  };
  _ctor.prototype.getIndex = function (e, t) {
    return e + t * this.key_count;
  };
  return _ctor;
}($1$2TwoDLayoutObject.TwoDLayoutObject);
exports.TwoDHorizontalLayoutObject = exp_TwoDHorizontalLayoutObject;