var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2Cfg = require("Cfg");
var $2Manager = require("Manager");
var $2FCollider = require("FCollider");
var $2Game = require("Game");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var $2PropertyVo = require("PropertyVo");
var $2BulletVoPool = require("BulletVoPool");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_BulletBase = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.isRotate = 0;
    t.isBanRotate = false;
    t._vo = null;
    t._midTime = 0;
    t.hitID = new Set();
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.hitID.clear();
    this.entityType = $2BaseEntity.EntityType.Bullet;
    this.initCollider();
  };
  _ctor.prototype.setBulletVo = function (e) {
    var t;
    var o = this;
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    this._vo = e;
    this.property.set({
      speed: e.speed,
      atk: e.hurt.baseVal,
      hp: 1
    }).updateVo();
    if (this.isBanRotate) {
      this.node.angle = 0;
    } else {
      this.updateDir(0);
    }
    if (e.bulletId) {
      var i = $2Cfg.Cfg.BulletEffect.get(e.bulletId);
      var n = this.node.getComponentInChildren(cc.Sprite);
      var r = this.node.getComponentInChildren(sp.Skeleton);
      n && i.res && $2Manager.Manager.loader.loadSpriteToSprit(i.res, n);
      if (r && i.spine && i.spine[1]) {
        r.defaultAnim = i.spine[1];
      }
      r && i.spine && $2Manager.Manager.loader.loadSpine(i.spine[0], this.node).then(function (e) {
        r.skeletonData = e;
        r.setAnimation(0, i.spine[1] ? i.spine[1] : "animation", "1" == i.spine[2]);
        if (i.spine[3]?.length > 0) {
          r.addAnimation(0, i.spine[3], "i" == i.spine[4]);
        }
      });
    }
    this.vo.belongSkill.audioID && $2Manager.Manager.audio.playAudio(this.vo.belongSkill.audioID);
    (null === (t = this.vo.belongSkill) || undefined === t ? undefined : t.subSkill.find(function (e) {
      return e.release == $2GameSeting.GameSeting.Release.Process;
    })) && this.delayByGame(function () {
      o.isActive && o.vo.belongSkill.checkSubSkill($2GameSeting.GameSeting.Release.Process, {
        pos: o.position,
        start: o
      });
    }, .1, 1e3);
  };
  _ctor.prototype.getHurt = function () {
    return this.vo.hurt;
  };
  _ctor.prototype.unuse = function () {
    this.node.opacity = 0;
    if (this._vo) {
      $2BulletVoPool.BulletVoPool.despawn(this._vo);
      this._onCollisionCall = null;
    }
    e.prototype.unuse.call(this);
  };
  Object.defineProperty(_ctor.prototype, "onCollisionCall", {
    set: function (e) {
      this._onCollisionCall = e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setDead = function () {
    this.isDead || (this.isDead = true);
  };
  _ctor.prototype.initCollider = function () {
    if (this.vo.size) {
      if (this.collider.type == $2FCollider.ColliderType.Box) {
        this.collider.size = cc.size(this.vo.size, this.vo.size);
      } else {
        this.collider.type == $2FCollider.ColliderType.Circle && (this.collider.radius = this.vo.size / 2);
      }
    }
  };
  _ctor.prototype.updateDir = function () {
    if (0 != this.vo.shootDir.x || 0 != this.vo.shootDir.y) {
      var e = cc.v2(this._vo.shootDir).signAngle(cc.Vec2.UP);
      this.node.angle = -cc.misc.radiansToDegrees(e);
    }
  };
  Object.defineProperty(_ctor.prototype, "vo", {
    get: function () {
      return this._vo;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
    this.initialSize = this.node.getContentSize().clone();
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (!(this._vo.lifeTime < 0 || this.isDead)) {
      this.isRotate && (this.node.angle -= this.isRotate * t);
      if (!this._vo.isForever) {
        this._vo.lifeTime -= t, this._vo.lifeTime <= 0 && (this.bulletExit($2GameSeting.GameSeting.Release.TimeEnd), this.isDead = true);
      }
    }
  };
  _ctor.prototype.bulletExit = function (e, t) {
    var o = {
      pos: this.position.clone(),
      start: t || this,
      ignoreID: [this.ID]
    };
    t && o.ignoreID.push(t.ID);
    this.vo.belongSkill.checkSubSkill(e, o);
    e == $2GameSeting.GameSeting.Release.HitEnd && this.vo.belongSkill.cutVo.hitEffect && $2Game.Game.mgr.showEffectByType("entity/fight/effect/" + this.vo.belongSkill.cutVo.hitEffect, this.position, true, .5);
  };
  _ctor.prototype.setHurt = function (e) {
    var t;
    if (e.behit(this.vo.hurt)) {
      this.vo.hurt.hitPos.set(this.position);
      null === (t = this.vo.belongSkill) || undefined === t || t.excuteBuffToEnemy(e, $2GameSeting.GameSeting.Release.Hit);
      this.vo.hitAudioId && $2Manager.Manager.audio.playAudio(this.vo.hitAudioId);
      this.hitID.add(e.ID);
    }
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    var t;
    var o;
    if (!this.isDead && e.comp && this.vo.atkCamp.includes(e.comp.campType)) {
      if (null === (t = this.vo.ignore) || undefined === t ? undefined : t.includes(e.comp.ID)) {
        return;
      }
      this.setHurt(e.comp);
      if (this.vo.crossNum < 900 && this.hitID.size >= this.vo.crossNum) {
        this.bulletExit($2GameSeting.GameSeting.Release.HitEnd, e.comp);
        this.isDead = true;
      }
      this.bulletExit($2GameSeting.GameSeting.Release.Hit, e.comp);
      null === (o = this._onCollisionCall) || undefined === o || o.call(this, e.comp);
      return true;
    }
  };
  _ctor.prototype.onCollisionStay = function (e) {
    this.vo.belongSkill.cutVo.dur && e.comp && this.vo.atkCamp.includes(e.comp.campType) && this.setHurt(e.comp);
  };
  Object.defineProperty(_ctor.prototype, "settingScale", {
    get: function () {
      return this.vo.scale;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.OnBuff = function (t) {
    e.prototype.OnBuff.call(this, t);
    this.vo.hurt.baseVal = this.property.cut.atk;
  };
  cc__decorate([ccp_property({
    displayName: "自动旋转速度"
  })], _ctor.prototype, "isRotate", undefined);
  cc__decorate([ccp_property({
    displayName: "禁止旋转"
  })], _ctor.prototype, "isBanRotate", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/BulletBase")], _ctor);
}($2OrganismBase.default);
exports.default = def_BulletBase;