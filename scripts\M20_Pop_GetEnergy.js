var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2VideoButton = require("VideoButton");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;
var def_M20_Pop_GetEnergy = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.coincost = null;
    t.coinget = null;
    t.coincount = null;
    t.adcost = null;
    t.adget = null;
    t.adcount = null;
    t.adgray = null;
    t.costgray = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.onOpen = function () {
    this.mode.dailyAdpack.has("energycoin") || this.mode.dailyAdpack.addGoods("energycoin", $2Manager.Manager.vo.switchVo.diamondBuyStamina[2]);
    this.mode.dailyAdpack.has("energyad") || this.mode.dailyAdpack.addGoods("energyad", $2Manager.Manager.vo.switchVo.adBuyStamina[2]);
    this.data = {
      coin: {
        cost: $2Manager.Manager.vo.switchVo.diamondBuyStamina[0],
        get: $2Manager.Manager.vo.switchVo.diamondBuyStamina[1],
        count: this.mode.dailyAdpack.getVal("energycoin")
      },
      ad: {
        cost: $2Manager.Manager.vo.switchVo.adBuyStamina[0],
        get: $2Manager.Manager.vo.switchVo.adBuyStamina[1],
        count: this.mode.dailyAdpack.getVal("energyad")
      }
    };
    this.adgray.getChildByName("gray").active = this.data.ad.count <= 0;
    this.costgray.node.getChildByName("gray").active = this.data.coin.count <= 0;
    this.adgray.getComponent($2VideoButton.default).interactable = this.data.ad.count > 0;
    this.costgray.interactable = this.data.coin.count > 0;
    this.coincost.string = this.data.coin.cost;
    this.coincount.string = cc.js.formatStr("今日剩余%d次", this.data.coin.count);
    this.coinget.string = this.data.coin.get;
    this.adcount.string = cc.js.formatStr("今日剩余%d次", this.data.ad.count);
    this.adget.string = this.data.ad.get;
  };
  _ctor.prototype.costGet = function () {
    if (this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, this.data.coin.cost)) {
      this.mode.dailyAdpack.useUp("energycoin");
      $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, this.data.coin.cost);
      $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, this.data.coin.get);
      $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("体力+%d", this.data.coin.get));
      $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
      this.close();
    } else {
      $2AlertManager.AlertManager.showNormalTips("灵币不足~");
    }
  };
  _ctor.prototype.adGet = function () {
    cc.log(this.data.ad);
    this.mode.dailyAdpack.useUp("energyad");
    $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, this.data.ad.get);
    $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("体力+%d", this.data.ad.get));
    $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
    this.close();
  };
  _ctor.prototype.onClose = function () {};
  _ctor.prototype.setInfo = function () {};
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "coincost", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "coinget", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "coincount", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "adcost", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "adget", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "adcount", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "adgray", undefined);
  cc__decorate([ccp_property(cc.Button)], _ctor.prototype, "costgray", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeBackpackHero/M20_Pop_GetEnergy"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_M20_Pop_GetEnergy;