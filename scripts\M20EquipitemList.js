var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2Notifier = require("Notifier");
var $2M20Equipitem = require("M20Equipitem");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_M20EquipitemList = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.dropNode = null;
    t.btndel = null;
    t.btnuse = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onEnable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onDisable = function () {
    this.changeListener(true);
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.resetState, this, -200);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetState, this, -200);
  };
  _ctor.prototype.setInfo = function (t) {
    undefined === t && (t = this.equipcfg.id);
    e.prototype.setInfo.call(this, t);
    this.oldZindex = this.node.zIndex;
    this.resetState();
  };
  _ctor.prototype.resetState = function (t) {
    e.prototype.resetState.call(this);
    this.btndel.setActive(this.isEquip);
    this.btnuse.setActive(!this.isEquip);
    this.dropNode.setActive(false);
    this.node.zIndex = this.oldZindex;
    cc.tween(this.node).stopLast().to(.1, {
      scale: 1,
      opacity: 255
    }).start();
    if (2 == this.view.stateType) {
      if (this.isEquip) {
        cc.tween(this.node).stopLast().to(.3, {
          scale: 1.1
        }).to(.3, {
          scale: 1
        }).union().repeatForever().start();
      } else if (this == t) {
        cc.tween(this.node).stopLast().to(.1, {
          scale: 1.1
        }).start();
      } else {
        cc.tween(this.node).to(.1, {
          opacity: 100
        }).start();
      }
    }
  };
  _ctor.prototype.onClick = function () {
    if (2 != this.view.stateType || this.isEquip) {
      this.dropNode.setActive(!this.dropNode.active);
      this.node.zIndex = this.dropNode.active ? 999 : this.oldZindex;
      this._onClickCall && this._onClickCall(this);
    }
  };
  _ctor.prototype.select = function () {
    this.view.onSelectItem(this);
  };
  _ctor.prototype.delete = function () {
    this.mode.UnAssembleEquip(this.equipID);
    $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "dropNode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btndel", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnuse", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}($2M20Equipitem.default);
exports.default = def_M20EquipitemList;