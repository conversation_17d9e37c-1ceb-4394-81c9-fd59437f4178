var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.M33_FightScene = undefined;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2FightScene = require("FightScene");
var $2Game = require("Game");
var $2MChains = require("MChains");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var exp_M33_FightScene = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.game = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_End, this.onOpenEndView, this);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_Win, this.onFightWin, this);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OpenReliveView, this.onOpenReliveView, this);
  };
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    var e = this;
    if (this.game) {
      this.game.destroy();
      this.game = null;
    }
    this.game = new $2MChains.MChains.Mgr(this._openArgs.param || {});
    this.game.loadMap(this.gameNode, function () {
      $2UIManager.UIManager.Open("ui/ModeChains/M33_FightUIView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setOpenCallback(function () {}));
      e.game.gameState = $2Game.Game.State.START;
    });
  };
  _ctor.prototype.onFightWin = function () {
    this.onOpenEndView(true);
  };
  _ctor.prototype.onOpenEndView = function (e) {
    $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_GameEnd", $2MVC.MVC.openArgs().setParam({
      isWin: e,
      cfg: this.game.miniGameCfg
    }).setDailyTime(.2).setIsNeedLoading(false));
  };
  _ctor.prototype.onOpenReliveView = function () {
    if (this.game.gameState != $2Game.Game.State.PAUSE) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
      $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_Revive", $2MVC.MVC.openArgs());
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeChains/M33_FightScene"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Scene), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Scene)], _ctor);
}($2FightScene.FightScene);
exports.M33_FightScene = exp_M33_FightScene;