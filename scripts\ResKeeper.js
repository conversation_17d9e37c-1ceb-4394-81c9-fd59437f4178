var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_ResKeeper = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.autoRes = [];
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onDestroy = function () {
    this.releaseAutoRes();
  };
  _ctor.prototype.releaseAutoRes = function () {
    for (var e = 0; e < this.autoRes.length; e++) {
      var t = this.autoRes[e];
      if (t.asset) {
        t.asset.decRef();
        t.asset = null;
      }
    }
    this.autoRes = null;
  };
  _ctor.prototype.autoReleaseRes = function (e) {
    e.asset.addRef();
    this.autoRes.push(e);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_ResKeeper;