var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2GameatrCfg = require("GameatrCfg");
var $2SoundCfg = require("SoundCfg");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2BaseEntity = require("BaseEntity");
var $2Dragon = require("Dragon");
var $2Game = require("Game");
var $2PropertyVo = require("PropertyVo");
var $2MChains = require("MChains");
var $2TideDefendModel = require("TideDefendModel");
cc.v2();
cc.v2();
var v = cc._decorator.ccclass;
var def_MonstarTideDragon = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.baglvCfg = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2TideDefendModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function (t, o) {
    var i;
    var n;
    var r = this;
    e.prototype.setInfo.call(this, t, o);
    var s = this.baglvCfg.monId ? this.baglvCfg.monId[0] : 999999991;
    this.roundMonster = Object.values(this.mode.dragonList[s]);
    var c = $2Manager.Manager.vo.switchVo.dragonDiff.filter(function (e) {
      return e[0] == r.game.miniGameCfg.lvid;
    });
    this.roundMonster.forEach(function (e, t) {
      var o = c.find(function (e) {
        return t >= e[1] && t < e[2];
      });
      o && (e.hp *= 1 + o[3]);
    });
    var l = this.roundMonster[0];
    this.property = new $2PropertyVo.Property.Vo(this, {
      speed: l.speed,
      atk: l.atk,
      hp: l.hp
    });
    this.rageSpeed = .5;
    this.maxSpeed = l.speed;
    this.monCfg = $2Cfg.Cfg.Monster.get($2GameUtil.GameUtil.randomArr(l.monId));
    this.setAmClip();
    this.isAngleHade = this.monCfg.spine.includes("_f");
    null === (i = this.monCfg.buff) || undefined === i || i.forEach(function (e) {
      return r.addBuff(e);
    });
    null === (n = this.monCfg.skill) || undefined === n || n.forEach(function (e) {
      return r.addSkill(e);
    });
  };
  _ctor.prototype.setAmClip = function () {
    var e = this;
    if (this.monCfg && this.monCfg.spine) {
      $2Manager.Manager.loader.loadSpineNode(this.monCfg.spine, {
        nodeAttr: {
          parent: this.node,
          position: cc.Vec2.ZERO
        },
        spAttr: {
          loop: true,
          defaultAnim: "idle",
          isPlayerOnLoad: true
        }
      }).then(function (t) {
        var o;
        e.mySkeleton = t;
        e.roleNode = t.node;
        ((null === (o = e.buffMgr) || undefined === o ? undefined : o.attrMapAll.getor($2GameatrCfg.GameatrDefine.movespeed, 0)) || 0) > 0 && e.playAction("anger", true);
      });
    } else {
      console.log("没有配置龙骨");
    }
  };
  _ctor.prototype.createBody = function (t, o) {
    var i = this;
    return new Promise(function (n) {
      t.hp > 1 && (t.hp = Math.max(1, Math.round(t.hp * (1 + i.mode.gmDiff))));
      var r = i.getDragonHeadCfg();
      r && (t.hp = t.hp + r.hp);
      e.prototype.createBody.call(i, t, o).then(function (e) {
        n(e);
      });
    });
  };
  _ctor.prototype.toDead = function () {
    this.bodyList.forReverse(function (e) {
      e.isActive = false;
      e.removeEntityToUpdate();
    });
    e.prototype.toDead.call(this);
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this.disMonster();
  };
  _ctor.prototype.disMonster = function () {
    var e = this;
    if (this.loadIndex > 2 && 0 == this.bodyList.length) {
      this.toDead();
    } else if (this.mode.bDragonBoss(this.baglvCfg) && this.position.y < this.mode.role.haedPosition.y) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
      this.scheduleOnce(function () {
        e.toDead();
      }, .5);
    } else {
      this.lastPosY < -100 && this.toDead();
    }
  };
  _ctor.prototype.getDragonHeadCfg = function () {
    return this.baglvCfg;
  };
  _ctor.prototype.onKillMonster = function (e) {
    this.killLen++;
    this.game.killMonsterNum++;
    var t = e.section;
    var o = $2Manager.Manager.vo.switchVo.dragonCrzay.find(function (e) {
      return t >= e[0] && t < e[1];
    });
    if (o && $2Game.Game.weightFloat(o[2])) {
      var i = {
        id: 999991,
        name: "狂暴",
        type: 1,
        time: 4,
        attr: [$2GameatrCfg.GameatrDefine.movespeed],
        value: [[this.rageSpeed]]
      };
      this.addBuffByData(i);
    }
    this.addBuffByData({
      id: 999992,
      name: "弱化",
      type: 1,
      time: 1,
      attr: [$2GameatrCfg.GameatrDefine.movespeed],
      value: [[-.5]]
    });
    this.isRetreat = true;
    $2Game.Game.tween(this).stopLast().delay(.6).set({
      isRetreat: false
    }).start();
    if (this.game.passType == $2MChains.MChains.PassType.Move) {
      this.curIndex -= Math.max(Math.round(e.size / 2), 0);
    } else {
      this.curIndex -= Math.max(Math.round(e.size), 0);
    }
    this.bodyList.delete(e);
    0 == this.bodyList.length && this.loadIndex > 5 && this.toDead();
    this._dt = 0;
  };
  _ctor.prototype.onCollisionEnter = function (e) {
    var t;
    if ((null === (t = e.comp) || undefined === t ? undefined : t.entityType) == $2BaseEntity.EntityType.Bullet) {
      var o = cc.v2(e.comp.position.x + 5, e.comp.position.y + 5);
      $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.skill_zj);
      $2Game.Game.mgr.showEffectByType("entity/fight/effect/Effect_Bomb", o, true, .3, {
        parent: this.game.botEffectNode
      });
    }
  };
  return cc__decorate([v], _ctor);
}($2Dragon.Dragon);
exports.default = def_MonstarTideDragon;