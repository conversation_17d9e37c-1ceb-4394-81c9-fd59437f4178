var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Manager = require("Manager");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_TestItem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.itemName = null;
    t.itemCount = null;
    t.itemIcon = null;
    t.leftBtn = null;
    t.rightBtn = null;
    t.count = 1;
    t.close = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.Mgr.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setCloseCallBack = function (e) {
    this.close = e;
  };
  _ctor.prototype.setdata = function (e) {
    this.cfg = e;
    this.itemName.string = e.name;
    $2Manager.Manager.loader.loadSpriteToSprit(e.icon, this.itemIcon, this.node.parent);
    this.checkBtn();
  };
  _ctor.prototype.checkBtn = function () {
    this.itemCount.string = this.count + "";
    this.leftBtn.interactable = this.count > 1;
  };
  _ctor.prototype.onBtn = function (e, t) {
    switch (t) {
      case "minus":
        this.count -= 1;
        this.checkBtn();
        break;
      case "plus":
        this.count += 1;
        this.checkBtn();
        break;
      case "get":
        if (!this.game) {
          return void $2AlertManager.AlertManager.showNormalTips("请在战斗背包页面设置");
        }
        var o = this.game.packView;
        if (!o) {
          return void $2AlertManager.AlertManager.showNormalTips("请在战斗背包页面设置");
        }
        for (var i = 0; i < this.count; i++) {
          o.newProp(this.cfg).then(function (e) {
            o.setInSpareBox(e);
            o.checkViewoUnLock(e);
          });
        }
        this.close && this.close();
    }
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "itemName", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "itemCount", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "itemIcon", undefined);
  cc__decorate([ccp_property(cc.Button)], _ctor.prototype, "leftBtn", undefined);
  cc__decorate([ccp_property(cc.Button)], _ctor.prototype, "rightBtn", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_TestItem;