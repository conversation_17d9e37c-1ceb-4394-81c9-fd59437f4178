var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2StateMachine = require("StateMachine");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2MonsterState = require("MonsterState");
var $2MonsterTidalState = require("MonsterTidalState");
var $2Monster = require("Monster");
cc.v2();
cc.v2();
var f = cc._decorator.ccclass;
var def_MonsterTidal = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    var t;
    var o = this;
    e.prototype.init.call(this);
    null === (t = this.monCfg.skill) || undefined === t || t.forEach(function (e) {
      return o.addSkill(e);
    });
  };
  _ctor.prototype.registerState = function () {
    if (!this._stateMachine) {
      this._stateMachine = new $2StateMachine.State.Machine(this);
      this._stateMachine.addState(new $2MonsterState.MonsterState.IdleState(this, false));
      this._stateMachine.addState(new $2MonsterTidalState.MonsterTidalState.AppearState(this, false));
      this._stateMachine.addState(new $2MonsterTidalState.MonsterTidalState.MoveState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.AttackState(this, true));
      this._stateMachine.addState(new $2MonsterState.MonsterState.DeadState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.BeHit(this, false));
      this._stateMachine.registerGlobalState(new $2MonsterState.MonsterState.GlobalState(this));
    }
  };
  _ctor.prototype.isOffScreen = function () {
    return false;
  };
  _ctor.prototype.isInAttackRange = function () {
    if (!this.steering.targetAgent1) {
      return -1;
    }
    var e = Math.abs(this.node.y - this.steering.targetAgent1.position.y);
    var t = this.property.cut.atkArea + this.steering.targetAgent1.radius;
    if (e > t) {
      return -1;
    } else {
      if (e < .1 * t) {
        return 1;
      } else {
        return 0;
      }
    }
  };
  _ctor.prototype.toDead = function () {
    var e;
    if (!this._stateMachine.isInState($2StateMachine.State.Type.DEAD)) {
      this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
      (null === (e = this.skillMgr) || undefined === e ? undefined : e.hasMainID(2060)) && this.skillMgr.use(2060);
    }
  };
  _ctor.prototype.onSkill = function (e) {
    if ($2SkillManager.Skill.SelfDestructSkill.includes(e.id)) {
      this.curHp -= 990;
      this.toDead();
    }
  };
  _ctor.prototype.setPosition = function (t) {
    t.y = Math.max(t.y, this.game.scenceSize[2]);
    e.prototype.setPosition.call(this, t);
  };
  return cc__decorate([f], _ctor);
}($2Monster.Monster);
exports.default = def_MonsterTidal;