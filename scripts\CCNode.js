var o;
var i = new Map();
var n = [];
var r = new cc.Vec2();
new cc.Mat4();
!CC_EDITOR && Object.defineProperty(cc.Node.prototype, "worldPosition", {
    get: function() {
        this._updateWorldMatrix();
        return cc.Vec2.transformMat4(r, cc.Vec2.ZERO, this._worldMatrix).clone();
    },
    set: function(e) {
        this._updateWorldMatrix();
        this.setPosition(this.parent.convertToNodeSpaceAR(e));
    }
});
!CC_EDITOR && Object.defineProperty(cc.Node.prototype, "scenePosition", {
    get: function() {
        o || (o = cc.v2(-cc.view.getCanvasSize().width / 2, -cc.view.getCanvasSize().height / 2));
        return this.worldPosition.add(o);
    }
});
!CC_EDITOR && Object.defineProperty(cc.Node.prototype, "wordPos", {
    get: function() {
        return this.worldPosition.sub(cc.v2(cc.winSize.width / 2, cc.winSize.height / 2));
    },
    set: function() {}
});
cc.Vec2.prototype.setVal = function(e, t) {
    this.x = e;
    this.y = t;
    return this;
};
cc.Vec2.prototype.setX = function(e) {
    this.x = e;
    return this;
};
cc.Vec2.prototype.setY = function(e) {
    this.y = e;
    return this;
};
cc.Size.prototype.mulSelf = function(e) {
    this.width *= e;
    this.height *= e;
    return this;
};
cc.Size.prototype.multiplySelf = function(e, t) {
    this.width *= e;
    this.height *= t;
    return this;
};
cc.Node.prototype.setActive = function(e) {
    if (this && this.active != e) {
        return this.active = e, this;
    } else {
        return this;
    }
};
cc.Node.prototype.hideAllChildren = function() {
    this.children.forEach(function(e) {
        return e.setActive(false);
    });
    return this;
};
cc.Node.prototype.setAttribute = function(e) {
    for (var t in e) {
        if (this[t] instanceof cc.Vec2) {
            this[t].set(e[t]);
        } else {
            this[t] = e[t];
        }
    }
    return this;
};
cc.Node.prototype.filterChild = function(e) {
    var t = [];
    this.children.forEach(function(o) {
        var i = 1;
        for (var n in e) {
            o[n] != e[n] && i--;
        }
        i && t.push(o);
    });
    return t;
};
cc.Node.prototype.getORaddComponent = function(e) {
    var t = this.getComponent(e);
    t && cc.isValid(t) || (t = this.addComponent(e));
    return t;
};
cc.Node.prototype.getORaddChildByName = function(e) {
    var t = this.getChildByName(e);
    t || (t = new cc.Node(e)).setParent(this);
    return t;
};
cc.Node.prototype.getComByChild = function(e, t) {
    for (var o = 0; o < this.childrenCount; o++) {
        if (!t || this.children[o].name == t) {
            var i = this.children[o].getComponent(e);
            if (i) {
                return i;
            }
        }
    }
    return null;
};
cc.Node.prototype.getChildByPath = function(e) {
    return cc.find(e, this);
};
cc.Node.prototype.getComByPath = function(e, t) {
    var o = this;
    var i = t.split("/");
    var n = null;
    i.forEach(function(e) {
        return n = (n || o).getChildByName(e);
    });
    var r = null == n ? undefined : n.getComponent(e);
    r || cc.warn("请检查路径是否正确", t);
    return r;
};
cc.Node.prototype.spliceNode = function(e, t) {
    for (; this.children[e] && t > 0;) {
        this.children[e].destroy();
        t--;
        e++;
    }
    return this;
};
cc.Node.prototype.setGray = function(e, t) {
    undefined === e && (e = false);
    undefined === t && (t = cc.Sprite);
    if (this) {
        var o = this.getComponent(t);
        if (o) {
            o.autoSwitchMaterial = 2;
            o.setMaterial(0, cc.Material.getBuiltinMaterial(e ? "2d-gray-sprite" : "2d-sprite"));
        }
    }
};
cc.Node.prototype.setGrayAllChild = function(e, t) {
    undefined === e && (e = false);
    undefined === t && (t = cc.Sprite);
    this && this.getComponentsInChildren(t).forEach(function(t) {
        t.autoSwitchMaterial = 2;
        t.setMaterial(0, cc.Material.getBuiltinMaterial(e ? "2d-gray-sprite" : "2d-sprite"));
    });
};
cc.Node.prototype.changeListener = function(e, t, o, i, n) {
    if (e) {
        this.on(t, o, i, n);
    } else {
        this.off(t, o, i, n);
    }
    return this;
};
cc.Component.prototype.setAttribute = function(e) {
    for (var t in e) {
        this[t] = e[t];
    }
    return this;
};
cc.ActionInterval.prototype.step = function(e) {
    if (!i.get(this.tag)) {
        if (this._firstTick && !this._goto) {
            this._firstTick = false;
            this._elapsed = 0;
        } else {
            this._elapsed += e;
        }
        var t = this._elapsed / (this._duration > 1.192092896e-7 ? this._duration : 1.192092896e-7);
        t = 1 > t ? t : 1;
        this.update(t > 0 ? t : 0);
        if (this._repeatMethod && this._timesForRepeat > 1 && this.isDone()) {
            this._repeatForever || this._timesForRepeat--;
            this.startWithTarget(this.target);
            this.step(this._elapsed - this._duration);
        }
    }
};
cc.Tween.prototype.union = function() {
    var e = this._union();
    e.tag = this._tag;
    this._actions.length = 0;
    this._actions.push(e);
    return this;
};
cc.Tween.prototype.shake = function(e) {
    e || (e = {});
    e.lv || (e.lv = 1);
    e.loopNum || (e.loopNum = 1);
    e.scaleTime || (e.scaleTime = 1);
    var t = this._target;
    var o = t.x;
    var i = t.y;
    var n = e.lv;
    e.defaultPos && this.set({
        position: e.defaultPos
    });
    for (var r = 0; r < e.loopNum; r++) {
        this.sequence(cc.tween().call(function() {
            o = t.x;
            i = t.y;
        }), cc.tween().to(.03 * e.scaleTime, {
            position: cc.v2(o + 2 * n, i + 3 * n)
        }), cc.tween().to(.03 * e.scaleTime, {
            position: cc.v2(o - 3 * n, i - 3 * n)
        }), cc.tween().to(.03 * e.scaleTime, {
            position: cc.v2(o + 8 * n, i + 2 * n)
        }), cc.tween().to(.03 * e.scaleTime, {
            position: cc.v2(o - 8 * n, i - 3 * n)
        }), cc.tween().to(.03 * e.scaleTime, {
            position: cc.v2(o + 3 * n, i + 3 * n)
        }), cc.tween().to(.03 * e.scaleTime, {
            position: cc.v2(o - 8 * n, i - 2 * n)
        }), cc.tween().to(.03 * e.scaleTime, {
            x: o,
            y: i
        })).repeat(3);
    }
    return this;
};
cc.Tween.prototype.destroySelf = function() {
    var e = this._target;
    this.call(function() {
        e.destroy();
    });
    return this;
};
cc.Tween.prototype.stopLast = function() {
    cc.Tween.stopAllByTarget(this._target);
    return this;
};
cc.Tween.prototype.pause = function() {
    this._finalAction.paused = true;
};
cc.Tween.prototype.resume = function() {
    this._finalAction.paused = false;
};
cc.Tween.prototype.speed = function(e) {
    this._finalAction._speedMethod = true;
    this._finalAction._speed = e;
};
cc.Tween.prototype.duration = function() {
    return this._finalAction._duration;
};
cc.Tween.prototype.elapsed = function() {
    return this._finalAction._elapsed;
};
cc.Tween.prototype.goto = function(e) {
    this._finalAction._goto = true;
    this._finalAction._elapsed = e;
};
cc.ActionManager.prototype.pauseByTag = function(e, t) {
    cc.Action.TAG_INVALID;
    i.set(e, t);
};
var a = function(e) {
    n.length = 0;
    var t = cc.director.getActionManager()._hashTargets;
    for (var o in t) {
        var i = 0;
        for (var r = t[o].actions.length; i < r; ++i) {
            var a = t[o].actions[i];
            a && a.getTag() === e && n.push(a);
        }
    }
    return n;
};
cc.Tween.pauseByTag = function(e) {
    cc.director.getActionManager().pauseByTag(e, true);
};
cc.Tween.resumeByTag = function(e) {
    cc.director.getActionManager().pauseByTag(e, false);
};
cc.Tween.setSpeedByTag = function(e, t) {
    a(e).forEach(function(e) {
        e._speed = t;
    });
};
sp.Skeleton.prototype.setPause = function(e) {
    this.paused = e;
};
sp.Skeleton.prototype.hasAnimation = function(e) {
    return this.animations.includes(e);
};
sp.Skeleton.prototype.reset = function(e) {
    var t;
    this.animations || (this.animations = []);
    this.animations.length = 0;
    this.skeletonData = e;
    if (e) {
        (t = this.animations).push.apply(t, this.skeletonData._skeletonCache.animations.map(function(e) {
            return e.name;
        }));
        this.paused = false;
    }
    return this;
};
sp.Skeleton.prototype.playQueue = function(e, t) {
    var o = this;
    undefined === t && (t = true);
    e.forEach(function(i, n) {
        if (0 == n) {
            o.setAnimation(0, i, false);
        } else {
            o.addAnimation(0, i, t && n == e.length - 1, 0);
        }
    });
};
Array.prototype.delete = function(e) {
    var t = this.indexOf(e);
    return t >= 0 && (this.splice(t, 1), true);
};
Array.prototype.splitInPairs = function(e) {
    var t = [];
    for (var o = 0; o < this.length; o += e) {
        var i = [];
        for (var n = 0; n < e; n++) {
            i.push(this[o + n]);
        }
        t.push(i);
    }
    return t;
};
Array.prototype.add = function(e) {
    this.includes(e) || this.push(e);
    return this;
};
Array.prototype.forReverse = function(e) {
    for (var t = this.length - 1; t >= 0; t--) {
        e(this[t], t);
    }
};
!CC_EDITOR && Object.defineProperty(Array.prototype, "lastVal", {
    get: function() {
        return this[this.length - 1];
    },
    set: function() {}
});
Date.prototype.getMonthDay = function() {
    return this.getMonth() + 1 + "-" + this.getDate();
};
Number.prototype.getFixed = function(e) {
    undefined === e && (e = 2);
    return +(+this).toFixed(e);
};
!CC_EDITOR && Object.defineProperty(cc.RichText.prototype, "text", {
    get: function() {
        return this._string;
    },
    set: function(e) {
        var t;
        if (cc.sys.language != cc.sys.LANGUAGE_CHINESE && "tc" != cc.sys.language && (null === (t = window.tpdg) || undefined === t ? undefined : t[e])) {
            var o = window.tpdg[e][cc.sys.language];
            if (o) {
                e = o;
            } else {
                cc.log("[没有找到翻译]", e);
            }
        }
        // this.string = "<b>" + e;
        this.string = e;
        this._updateRichTextStatus();
    }
});
cc.ParticleSystem.prototype.onDisable = function() {
    this.node._renderComponent = null;
    this.disableRender();
    this.resetSystem();
};
cc.ParticleSystem.__assembler__.prototype.fillBuffers = function(e, t) {
    if (this._ia) {
        var o = e._simulator.particles.length;
        if (0 !== o) {
            var i = cc.ParticleSystem.PositionType;
            if (e.positionType === i.RELATIVE) {
                t.node = e.node.parent;
            } else {
                t.node = e.node;
            }
            var n = this.getBuffer();
            var r = cc.renderer._handle._meshBuffer;
            var a = r.request(4 * o, 6 * o);
            var s = a.byteOffset >> 2;
            var c = r._vData;
            var l = n._vData;
            var u = n._iData;
            var p = 20 * o;
            if (p + s > c.length) {
                c.set(l.subarray(0, c.length - s), s);
            } else {
                c.set(l.subarray(0, p), s);
            }
            var f = r._iData;
            var h = a.indiceOffset;
            var d = a.vertexOffset;
            var g = 6 * o;
            for (var y = 0; y < g; y++) {
                f[h++] = d + u[y];
            }
        }
    }
};