var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2Intersection = require("Intersection");
var $2GameUtil = require("GameUtil");
var $2Buff = require("Buff");
var $2BaseEntity = require("BaseEntity");
var $2OrganismBase = require("OrganismBase");
var $2Game = require("Game");
var $2SkillManager = require("SkillManager");
var $2PropertyVo = require("PropertyVo");
var $2MMGuards = require("MMGuards");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_MMGRole = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.line = null;
    t._myData = null;
    t.roleId = 30500;
    t.touchPos = cc.v2();
    t.bulletIndex = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "myData", {
    get: function () {
      return this._myData;
    },
    set: function (e) {
      var t;
      var o = this;
      this._myData = e;
      if (e.spine) {
        this.mySkeleton = this.node.getComponentInChildren(sp.Skeleton);
        return void (this.mySkeleton && (null === (t = this.mySkeleton) || undefined === t || t.clearTracks(), $2Manager.Manager.loader.loadSpine(e.uiSpine, this.mySkeleton.node).then(function (e) {
          o.mySkeleton.reset(e);
          o.setAnimation("idle", true);
          o.delayByGame(function () {
            o.onNewSize(o.roleNode.getContentSize());
          });
        })));
      }
      this.skillMgr.launchPoint.set(this.roleNode.position.add(cc.v2(0, this.roleNode.height / 2)));
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onNewSize = function (t) {
    var o;
    t.mulSelf(1);
    this.node.setContentSize(t.width, t.height);
    this.collider.size = t;
    this.collider.offset = cc.v2(0, t.height / 2);
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale / 2);
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
  };
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.buffMgr || (this.buffMgr = new $2Buff.Buff.BuffManager(this));
    this.skillMgr || (this.skillMgr = new $2SkillManager.Skill.SkillManager(this));
    this.entityType = $2BaseEntity.EntityType.Role;
    this.campType = $2BaseEntity.CampType.One;
    this.bulletIndex = 0;
    this.knifeController = null;
    this.knifeController = new $2MMGuards.MMGuards.KnifeController().setAttribute({
      ower: this
    });
  };
  _ctor.prototype.setRotateData = function (e) {
    this._rotateData = e;
  };
  _ctor.prototype.setRole = function () {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(this.roleId);
    this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    var e = $2Cfg.Cfg.Role.find({
      roleId: this.roleId
    });
    this.property.set(e);
    this.updateProperty();
    this.skillMgr.add(this.myData.startSkill, false);
    this.initHp();
  };
  _ctor.prototype.changeRole = function (e) {
    this.myData = $2Cfg.Cfg.RoleUnlock.get(e);
  };
  _ctor.prototype.updateProperty = function () {
    this.property && e.prototype.updateProperty.call(this);
  };
  _ctor.prototype.behit = function (e) {
    var t = this;
    if (!this.isDead && !this.buffMgr.isInvincible && this.hurtMgr.checkHurt(e)) {
      this.curHp -= e.val;
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      this.materialTwinkle();
      this.mySkeleton.playQueue(["hit", "idle"], true);
      this.game.showDamageDisplay(e, this.haedPosition);
      $2Time.Time.delay(.5, function () {
        t.toDead();
        wonderSdk.vibrate(0);
      });
      return e;
    }
  };
  _ctor.prototype.materialTwinkle = function () {};
  _ctor.prototype.toDead = function () {
    this.isDead || (this.isDead = true);
  };
  Object.defineProperty(_ctor.prototype, "bulletID", {
    get: function () {
      return $2Cfg.Cfg.BagSkill.filter({
        id: this.myData.startSkill
      })[0].bulletId;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setLine = function (e) {
    var t = this;
    var o = (e * this._rotateData.rotateDirection + 180 + this._rotateData.startAngle) % 360;
    this.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(o % 360));
    var i = this.bodyPosition;
    var n = $2GameUtil.GameUtil.AngleAndLenToPos(o, 2e3).add(this.bodyPosition);
    var r = [];
    this.game.physicsPolygonColliders.forEach(function (e) {
      e.tag != t.node.nodeTag && e.points.forEach(function (t, o) {
        var a;
        var s;
        var c = $2Intersection.Intersection.getLineSegmentIntersection(i, n, t.add(e.offset), (null === (a = e.points[o + 1]) || undefined === a ? undefined : a.add(e.offset)) || (null === (s = e.points[0]) || undefined === s ? undefined : s.add(e.offset)));
        c && r.push({
          pos: c,
          d: cc.Vec2.squaredDistance(i, c)
        });
      });
    });
    r.sort(function (e, t) {
      return e.d - t.d;
    });
    r[0] && n.set(r[0].pos);
    this.line.setAttribute({
      parent: this.game.botEffectNode,
      active: true,
      position: this.bodyPosition,
      angle: o,
      height: cc.Vec2.distance(i, n)
    });
  };
  _ctor.prototype.hideLine = function () {
    this.line.setActive(false);
  };
  cc__decorate([ccp_property({
    type: cc.Node,
    displayName: "瞄准辅助线"
  })], _ctor.prototype, "line", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/ModeManGuards/MMGRole")], _ctor);
}($2OrganismBase.default);
exports.default = def_MMGRole;