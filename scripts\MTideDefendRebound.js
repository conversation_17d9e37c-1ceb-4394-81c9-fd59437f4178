var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MTideDefendRebound = undefined;
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2FPolygonCollider = require("FPolygonCollider");
var $2Intersection = require("Intersection");
var $2GameUtil = require("GameUtil");
var $2RecordVo = require("RecordVo");
var $2Game = require("Game");
var $2BronMonsterManger = require("BronMonsterManger");
var $2CompManager = require("CompManager");
var $2M33_TestBox = require("M33_TestBox");
var $2NodePool = require("NodePool");
var $2TideDefendModel = require("TideDefendModel");
var $2MonstarTideDragon = require("MonstarTideDragon");
var $2MTideDefendRmod = require("MTideDefendRmod");
(function (e) {
  var t;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.BATTLE = 1] = "BATTLE";
    e[e.ONTICK = 2] = "ONTICK";
    e[e.SELECTEQUIP = 3] = "SELECTEQUIP";
    e[e.END = 4] = "END";
  })(t = e.RoundStatus || (e.RoundStatus = {}));
  (function (e) {
    e[e.DRAGON1 = 999999991] = "DRAGON1";
    e[e.DRAGON2 = 999999992] = "DRAGON2";
    e[e.DRAGON3 = 999999993] = "DRAGON3";
    e[e.DRAGON4 = 999999910] = "DRAGON4";
  })(e.TideDefendDragonType || (e.TideDefendDragonType = {}));
  var o;
  var i;
  var C = function (e) {
    function t() {
      var t;
      var o = null !== e && e.apply(this, arguments) || this;
      o.poolADMap = ((t = {})[i.NormalBuff] = {
        resetNum: 3,
        getAll: 1
      }, t[i.HighBuff] = {
        resetNum: 1,
        getAll: 0
      }, t);
      o.freeTime = 1;
      o.killNum = 0;
      o.countdownTime = 0;
      o.adRefreshEquip = 9999;
      o.ADNum = 0;
      o.testNum = 1;
      return o;
    }
    cc__extends(t, e);
    return t;
  }($2RecordVo.RecordVo.Data);
  e.RecordData = C;
  (function (e) {
    e[e.Forward = 0] = "Forward";
    e[e.D360 = 1] = "D360";
    e[e.Move = 2] = "Move";
  })(o = e.PassType || (e.PassType = {}));
  (function (e) {
    e[e.NormalBuff = 1] = "NormalBuff";
    e[e.HighBuff = 2] = "HighBuff";
  })(i = e.poolType || (e.poolType = {}));
  var w = function (e) {
    function t(t) {
      var i = e.call(this, t) || this;
      i.cameraZoomRatio = 1;
      i.recordVo = new $2RecordVo.RecordVo.Mgr("MTideDefendRebound", function () {
        return new C();
      });
      i.passType = o.Forward;
      i.bossSkilling = new Set();
      i.bossHp = 9999;
      i.passParam = t;
      return i;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "mode", {
      get: function () {
        return $2TideDefendModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "rVo", {
      get: function () {
        return this.recordVo.vo;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.loadMap = function (e, t) {
      var o = this;
      this.gameNode = e;
      this.miniGameCfg = $2Cfg.Cfg.MiniGameLv.get(this.passParam.id);
      this.mode.miniGameCfg = this.miniGameCfg;
      this._entityNode = e.getChildByName("entityNode");
      this._mapNode = e.getChildByName("mapNode");
      this._bulletNode = e.getChildByName("bulletNode").setAttribute({
        zIndex: 500
      });
      this._topEffectNode = e.getChildByName("topEffectNode").setAttribute({
        zIndex: 1e3
      });
      this.LifeBarUI = e.getORaddChildByName("LifeBarUI").setAttribute({
        zIndex: 1001
      });
      this.topUINode = e.getORaddChildByName("topUINode").setAttribute({
        zIndex: 1002
      });
      this._botEffectNode = e.getChildByName("botEffectNode").setAttribute({
        zIndex: 1003
      });
      this.behitUI = e.getORaddChildByName("behitUI");
      this._finishCall = t;
      $2Manager.Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then(function (e) {
        e.setAttribute({
          parent: o._mapNode,
          zIndex: -1
        });
        o.myTiledMap = o._mapNode.getComByChild(cc.TiledMap);
        o.createRole(cc.v2(0, 90), o.miniGameCfg.roleId).then(function () {});
        o.gameCamera.lookPos.set(cc.v2(0, $2GameUtil.GameUtil.getDesignSize.height / 2));
        o.sendEvent("Start");
      });
      this.gameCamera.setZoomRatio(this.cameraZoomRatio);
      this.gameCamera.setPosition(cc.Vec2.ZERO);
      this.bronMonsterMgr = this.gameNode.getORaddComponent(k);
      this.bronMonsterMgr.init();
      this.topUINode.active = !!this.mode.bShowMonsterBlob(this.miniGameCfg);
      this.mode.bShowMatrix(this.miniGameCfg) && $2Manager.Manager.setGroupMatrixByStr("Monsetr", "Role", true);
      this.scenceSize = [-375 / this.gameCamera.cutZoomRatio * .8, 375 / this.gameCamera.cutZoomRatio * .8, 0, 0];
      this._finishCall && this._finishCall();
    };
    t.prototype.getObjPos = function (e) {
      return cc.v2(e.x - this.myTiledMap.node.width / 2 + e.width / 2, e.y - this.myTiledMap.node.height / 2 - e.height);
    };
    t.prototype.gameEnd = function () {
      var e = this;
      $2Game.Game.timerOnce(function () {
        e.gameState = $2Game.Game.State.NONE;
      }, .5);
    };
    t.prototype.createRole = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        $2NodePool.NodePool.spawn("entity/fight/ModeTideDefend/roleMod").setNodeAssetFinishCall(function (n) {
          var r = n.getComponent($2MTideDefendRmod.default);
          n.parent = o._topEffectNode;
          r.setPosition(e);
          r.init();
          r._logicTime = 0;
          t && (r.roleId = t);
          r.setRole();
          $2CompManager.default.Instance.registerComp(r);
          o.mainRole = r;
          i(r);
        });
      });
    };
    t.prototype.gamePause = function (t) {
      e.prototype.gamePause.call(this, t);
      $2Manager.Manager.setPhysics(!t);
    };
    t.prototype.onUpdate = function (t) {
      e.prototype.onUpdate.call(this, t);
    };
    Object.defineProperty(t.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.checkBossSkill = function () {
      var e;
      var t = this;
      if (!(this.bossSkilling.size > 0)) {
        var o = [];
        var i = (null === (e = this.bossDiff.find(function (e) {
          return t.bossHp >= e[2] && t.bossHp < e[3];
        })) || undefined === e ? undefined : e[4]) || 1;
        this.monsterMap.forEach(function (e) {
          e instanceof $2MonstarTideDragon.default && o.push.apply(o, e.skillMgr.skills.filter(function (e) {
            return e.isReady;
          }));
        });
        $2GameUtil.GameUtil.getRandomInArray(o, i).forEach(function (e) {
          e.mgr.use(e.skillCfg.id);
        });
      }
    };
    t.prototype.setTestMode = function (e) {
      var t = this;
      if (this._mapTestMode) {
        this._mapTestMode.open(e);
      } else {
        $2Manager.Manager.loader.loadPrefab("ui/ModeChains/M33_TestBox").then(function (e) {
          e.setParent(t.gameNode);
          t._mapTestMode = e.getORaddComponent($2M33_TestBox.default);
        });
      }
    };
    return t;
  }($2Game.Game.Mgr);
  e.Mgr = w;
  cc.v2();
  e.maxPower = 200;
  var S = function (e) {
    function t() {
      var t = e.call(this) || this;
      t.bulletNum = 4;
      t.line = t.game.botEffectNode.getComByChild(cc.Sprite, "line");
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "aimData", {
      get: function () {
        return this._aimData;
      },
      set: function (e) {
        var t = this;
        this._aimData = e;
        this.line.node.active || this.ower.setAnimation("aim", true);
        var o = this.ower.bodyPosition;
        var i = $2GameUtil.GameUtil.AngleAndLenToPos((e.angle + 180) % 360, 2e3).add(this.ower.bodyPosition);
        var n = [];
        this.game.myTiledMap.getComponent($2FPolygonCollider.default).points = [];
        this.game.myTiledMap.getComponents(cc.PhysicsPolygonCollider).forEach(function (e) {
          e.points.forEach(function (r, a) {
            var s = $2Intersection.Intersection.getLineSegmentIntersection(o, i, r, e.points[a + 1] || e.points[0]);
            s && n.push({
              pos: s,
              d: cc.Vec2.squaredDistance(o, s)
            });
            t.game.myTiledMap.getComponent($2FPolygonCollider.default).points.push(r);
          });
        });
        n.sort(function (e, t) {
          return e.d - t.d;
        });
        n[0] && i.set(n[0].pos);
        this.line.node.setAttribute({
          active: true,
          position: this.ower.bodyPosition,
          angle: (e.angle + 180) % 360,
          height: cc.Vec2.distance(o, i)
        });
      },
      enumerable: false,
      configurable: true
    });
    return t;
  }($2GameSeting.GameSeting.CompBase);
  e.KnifeController = S;
  var k = function (e) {
    function o() {
      var o = null !== e && e.apply(this, arguments) || this;
      o._batchNum = 0;
      o.buffOffset = 0;
      o.haveLife = false;
      o.cutStatus = t.NONE;
      o.reTime = 1;
      o.MonsterType = {
        1: "MonsterTideDefend",
        2: "MonsterTideDefend",
        3: "MonsterTideDefend",
        4: "MonsterTideDefend"
      };
      o.countDown = 0;
      o.addOnce = false;
      o.dragonIndex = 0;
      return o;
    }
    cc__extends(o, e);
    Object.defineProperty(o.prototype, "batchNum", {
      get: function () {
        return this._batchNum;
      },
      set: function (e) {
        var t = this;
        this._batchNum = e;
        if (0 != e) {
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
          this._batchSumCount = 0;
          this.RoundMonster = this.MonsterLv.filter(function (e) {
            return e.round == t._batchNum;
          });
          this.RoundMonster.forEach(function (e) {
            t._batchSumCount += e.sumCount;
          });
          $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "mode", {
      get: function () {
        return $2TideDefendModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.init = function () {
      this.MonsterLv = JSON.parse(JSON.stringify($2Game.ModeCfg.MonsterLv.filter({
        lv: this.game.miniGameCfg.lvid
      })));
      this.batchNum++;
      this.haveLife = false;
    };
    Object.defineProperty(o.prototype, "curBullet", {
      get: function () {
        return this.game.curBullet;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.onUpdate = function (e) {
      if (this.game.gameState != $2Game.Game.State.PAUSE) {
        if (this.cutStatus == t.BATTLE) {
          (this._dtTime += e) >= 1 && (this._dtTime = 0), this._batchSumCount > 0 && this.checkAddMonster(e), this.checkMonDe();
        }
        this.reTime = $2GameUtil.GameUtil.random(1, 100);
      }
    };
    Object.defineProperty(o.prototype, "survivalMonsterNum", {
      get: function () {
        return this.game.monsterMap.size;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.checkMonDe = function () {
      this._batchSumCount <= 0 && 0 == this.survivalMonsterNum && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, true);
    };
    Object.defineProperty(o.prototype, "role", {
      get: function () {
        return this.game.mainRole;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.changeListener = function (t) {
      e.prototype.changeListener.call(this, t);
    };
    o.prototype.changeGameStatus = function (e) {
      switch (e) {
        case t.BATTLE:
      }
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRoundType, e);
      this.cutStatus = e;
    };
    Object.defineProperty(o.prototype, "maxLen", {
      get: function () {
        return $2GameUtil.GameUtil.getDesignSize.height / this.game.gameCamera.cutZoomRatio;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "randomL", {
      get: function () {
        var e = 100;
        this.mode.miniGameCfg && this.mode.miniGameCfg.randomL && (e = this.mode.miniGameCfg.randomL);
        return $2Game.Game.random(this.maxLen + 200, this.maxLen + 200 + e);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "randomPos", {
      get: function () {
        return cc.v2($2Game.Game.random(this.game.scenceSize[0], this.game.scenceSize[1]), this.randomL);
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.getMonSpPos = function (e, t) {
      undefined === t && (t = 0);
      var o = this.randomL;
      var i = $2Cfg.Cfg.bagMonsterLv.get(e.id);
      var n = cc.winSize.width;
      var a = n / 2;
      var s = 1;
      var c = n / 2;
      if (i && i.bronArea && i.bronArea.length) {
        var l = this.reTime % 2;
        var u = i.bronArea[l];
        s = $2Game.Game.random(u[0] - c, u[1] - c);
        if (i.bronMatrix) {
          s = 0;
          o = this.maxLen;
          var p = i.bronMatrix;
          if (p[t - 1]) {
            return cc.v2(s + 100 * p[t - 1][0], o + 100 * p[t - 1][1]);
          } else {
            return cc.v2(s, o);
          }
        }
      } else {
        s = $2GameUtil.GameUtil.random(-a, a);
      }
      return cc.v2(s, o);
    };
    o.prototype.checkAddMonster = function (e) {
      var t = this;
      this.mainRole && !this.mainRole.isDead && (this.isBossRound || this._RoundMonster.forEach(function (o) {
        o.letTime += e;
        if (o.createNum < o.sumCount && o.letTime > o.bronTime) {
          o.letTime = 0;
          o.createNum++;
          o.Count > 200 && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ShowGameTips, 3, "尸潮来袭");
          if (t.bDrongin(o)) {
            t.addDragon(o), t._batchSumCount--;
          } else {
            t.addMonsterById(o, null);
          }
        }
      }));
    };
    o.prototype.addDragon = function (e) {
      var t = [];
      var o = e.monId ? e.monId[0] : 999999991;
      t[999999991] = [{
        x: -477,
        y: 1808
      }, {
        x: -156,
        y: 1156
      }, {
        x: -303,
        y: 945
      }, {
        x: -173,
        y: 684
      }, {
        x: -273,
        y: 480
      }, {
        x: -150,
        y: 280
      }, {
        x: -288,
        y: 51
      }, {
        x: -183,
        y: -151
      }, {
        x: -298,
        y: -380
      }, {
        x: -174,
        y: -621
      }, {
        x: -326,
        y: -948
      }, {
        x: -292,
        y: -1269
      }, {
        x: -1236,
        y: -1601
      }];
      t[999999992] = [{
        x: 23,
        y: 1781
      }, {
        x: 47,
        y: 1198
      }, {
        x: -57,
        y: 954
      }, {
        x: 14,
        y: 693
      }, {
        x: -82,
        y: 484
      }, {
        x: 37,
        y: 242
      }, {
        x: -105,
        y: 10
      }, {
        x: -8,
        y: -234
      }, {
        x: -136,
        y: -430
      }, {
        x: -12,
        y: -717
      }, {
        x: -172,
        y: -927
      }, {
        x: -155,
        y: -1356
      }, {
        x: -1236,
        y: -1601
      }];
      t[999999993] = [{
        x: 355,
        y: 1665
      }, {
        x: 215,
        y: 1457
      }, {
        x: 367,
        y: 1243
      }, {
        x: 237,
        y: 1e3
      }, {
        x: 396,
        y: 722
      }, {
        x: 255,
        y: 515
      }, {
        x: 426,
        y: 263
      }, {
        x: 306,
        y: -5
      }, {
        x: 436,
        y: -267
      }, {
        x: 310,
        y: -545
      }, {
        x: 443,
        y: -900
      }, {
        x: 172,
        y: -1330
      }, {
        x: 91,
        y: -2493
      }];
      t[999999910] = [{
        x: -678,
        y: 1100
      }, {
        x: -294,
        y: 1100
      }, {
        x: 404,
        y: 1100
      }, {
        x: 412,
        y: 1e3
      }, {
        x: -403,
        y: 1e3
      }, {
        x: -392,
        y: 900
      }, {
        x: 399,
        y: 900
      }, {
        x: 405,
        y: 800
      }, {
        x: -406,
        y: 800
      }, {
        x: -424,
        y: 700
      }, {
        x: 396,
        y: 700
      }, {
        x: 400,
        y: 600
      }, {
        x: -409,
        y: 600
      }, {
        x: -411,
        y: 500
      }, {
        x: 414,
        y: 500
      }, {
        x: 394,
        y: 400
      }, {
        x: -405,
        y: 400
      }, {
        x: -383,
        y: 300
      }, {
        x: 0,
        y: 300
      }, {
        x: 0,
        y: -1454
      }];
      this.addDragonByindex([t[o]], e);
    };
    o.prototype.addDragonByindex = function (e, t) {
      if (e && e[0]) {
        this.createDragon(e, this.dragonIndex, t);
        console.log("this.dragonIndex: ", this.dragonIndex);
        this.dragonIndex++;
      } else {
        console.log("路径为空");
      }
    };
    o.prototype.createDragon = function (e, t, o) {
      var i = this;
      return new Promise(function (n) {
        var r = i.game.gameNode.getORaddChildByName("Chains_" + t).getORaddChildByName("Chains");
        r.setAttribute({
          zIndex: 300,
          x: e[0][0].x,
          y: e[0][0].y
        });
        var a = r.addComponent($2MonstarTideDragon.default);
        a.init();
        a.baglvCfg = o;
        a.setInfo(t, e);
        $2CompManager.default.Instance.registerComp(a);
        i.game.chainsList.push(a);
        n(a);
      });
    };
    o.prototype.addMonsterById = function (e, t, o) {
      var i = this;
      undefined === o && (o = 0);
      var n = e.Count;
      $2Game.Game.timer(function () {
        var o = $2Cfg.Cfg.bagMonsterLv.get(e.id);
        if (o && o.bronArea && o.bronArea.length) {
          n--;
          t = i.getMonSpPos(e, e.Count - n);
        }
        i.createMonster(e, t || i.randomPos).then(function (e) {
          e.toMove();
        });
      }, e.bronSpeed, e.Count);
      this._batchSumCount--;
    };
    o.prototype.getMonsterSetType = function (e) {
      var t;
      if (this.bDrongin(e)) {
        return "ModeTideDefend/MonstarTideDragon";
      } else {
        return e.dropExpRatio ? t = "MonsterTideDefend" : e.buffList ? 1 == e.buffList.length ? t = "ModeTideDefend/MonstarRailingSingle" : 2 == e.buffList.length && (t = "ModeTideDefend/MonstarRailing") : t = "MonsterTideDefend", t && (t = $2NodePool.NodePool.spawn("entity/fight/" + t)), t;
      }
    };
    o.prototype.bDrongin = function (e) {
      return 10 == $2Cfg.Cfg.Monster.get(e.monId[0]).type || undefined;
    };
    return o;
  }($2BronMonsterManger.BronMonsterManger);
  e.SpawningMgr = k;
})(exports.MTideDefendRebound || (exports.MTideDefendRebound = {}));