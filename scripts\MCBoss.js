var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MCBoss = undefined;
var $2StateMachine = require("StateMachine");
var $2Monster = require("Monster");
var $2Game = require("Game");
var $2MCBossState = require("MCBossState");
var $2MonsterState = require("MonsterState");
var $2ModeChainsModel = require("ModeChainsModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc__decorator.menu;
var exp_MCBoss = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._skilldt = 0;
    return t;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.init = function () {
    var t;
    var o = this;
    e.prototype.init.call(this);
    null === (t = this.monCfg.skill) || undefined === t || t.forEach(function (e) {
      return o.addSkill(e, false);
    });
    this.skillMgr && (this.skillMgr.onReady = function () {
      o.skillMgr.getReadySkill(0);
    });
    this.isHead = 3 == this.monCfg.type;
    this.property.base.speed = this.property.cut.speed = 0;
    this.forwardDirection = cc.v2(0, -100);
  };
  _ctor.prototype.isOffScreen = function () {
    return false;
  };
  _ctor.prototype.isInAttackRange = function () {
    return -1;
  };
  _ctor.prototype.behit = function (t) {
    var o = this;
    if (this.isHead && this.curHp < 0) {
      return this.game.monsterMap.find(function (e) {
        return e.ID != o.ID;
      }).behit(t);
    } else {
      return e.prototype.behit.call(this, t);
    }
  };
  _ctor.prototype.onNewSize = function (t) {
    e.prototype.onNewSize.call(this, t);
    this.isHead && (this.skillMgr.launchPoint.y -= 80);
  };
  _ctor.prototype.unuse = function () {
    e.prototype.unuse.call(this);
  };
  _ctor.prototype.toDead = function (t) {
    if (!this.isHead) {
      this.game.bossSkilling.delete(this);
      e.prototype.toDead.call(this, t);
    }
  };
  _ctor.prototype.onSkill = function (e) {
    var t = this;
    this.game.bossSkilling.add(this);
    this.game.timeDelay.cancelBy(this._skillWatcher);
    this._skillWatcher = this.delayByGame(function () {
      t.game.bossSkilling.delete(t);
    }, 5 + (e.cutVo.dur + e.cutVo.barrageNum * e.cutVo.barrageCd) * e.cutVo.releaseCount).id;
  };
  _ctor.prototype.onUpdate = function (t) {
    if (this.isHead) {
      this.game.bossHp = this.game.monsterMap.arr.reduce(function (e, t) {
        if (t instanceof o && t.curHp >= 0) {
          return e + t.curHp;
        } else {
          return e;
        }
      }, 0);
      (this._skilldt += t) > .1 && this.game.checkBossSkill();
    }
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.registerState = function () {
    if (!this._stateMachine) {
      this._stateMachine = new $2StateMachine.State.Machine(this);
      this._stateMachine.addState(new $2MCBossState.MCBossState.IdleState(this, false));
      this._stateMachine.addState(new $2MCBossState.MCBossState.MoveState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.AttackState(this, true));
      this._stateMachine.addState(new $2MonsterState.MonsterState.DeadState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.BeHit(this, false));
      this._stateMachine.registerGlobalState(new $2MonsterState.MonsterState.GlobalState(this));
    }
  };
  return o = cc__decorate([ccp_ccclass], _ctor);
}($2Monster.Monster);
exports.MCBoss = exp_MCBoss;