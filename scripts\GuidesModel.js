var i;
var cc__extends = __extends;
var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $1$2MVC = require("MVC");
var def_GuidesModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.set = function (e, t) {
    this.pack.setVal(e, t);
  };
  _ctor.prototype.get = function (e) {
    return this.pack.has(e);
  };
  _ctor.prototype.has = function (e, t) {
    return t <= this.pack.has(e);
  };
  _ctor.prototype.check = function (e) {
    if (this.pack.has(e.chapter) + 1 == e.section) {
      var t = $2Cfg.Cfg.Guide.find({
        chapter: e.chapter,
        section: e.section
      });
      if (t && this.checkCondition(t)) {
        return e = cc__assign(cc__assign({}, e), t);
      } else {
        return undefined;
      }
    }
  };
  _ctor.prototype.checkCondition = function (e) {
    if (!e.condition) {
      return true;
    }
    switch (e.condition[0]) {
      case 1:
        var t = $2Cfg.Cfg.Guide.get(e.condition[1]);
        if (this.has(t.chapter, t.section)) {
          return true;
        }
    }
    return false;
  };
  _ctor._instance = null;
  return _ctor;
}($1$2MVC.MVC.BaseModel);
exports.default = def_GuidesModel;