var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Bullet = require("Bullet");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var def_TornadoBullet = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onUpdate = function (t) {
    this.vo.lifeTime > 1 && this._vo.speed > 20 && (this._vo.speed -= 2);
    e.prototype.onUpdate.call(this, t);
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/TornadoBullet")], _ctor);
}($2Bullet.default);
exports.default = def_TornadoBullet;