var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Pop = require("Pop");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2WonderSdk = require("WonderSdk");
var $2BaseNet = require("BaseNet");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var def_ExchangeCodeView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.codeEditBox = null;
    t.btnConfirm = null;
    t.isGet = false;
    t.code_list = [];
    t.daily_code_list = [];
    t.received_gift_code_list = [];
    t.isLive = false;
    t.isRequest = false;
    t.giftCode = "";
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onOpen = function () {
    var e;
    this.isLive = null === (e = this.param) || undefined === e ? undefined : e.isLive;
    this.codeEditBox.placeholderLabel.string = this.isLive ? "请输入直播福袋兑换码" : "请输入兑换码";
    this.labelArr[0].string = this.isLive ? "直播福袋兑换" : "兑换码";
    var t = $2Manager.Manager.storage.getString("isGetCodeDay", "false");
    var o = $2Manager.Manager.storage.getString("SaveServerTime");
    var i = new Date(Number(o));
    var n = new Date($2Time.Time.serverTimeMs);
    if (cc.js.formatStr("%d%d", i.getMonth(), i.getDate()) != cc.js.formatStr("%d%d", n.getMonth(), n.getDate())) {
      this.isGet = true;
      $2Manager.Manager.storage.setString("SaveServerTime", $2Time.Time.serverTimeMs + "");
      $2Manager.Manager.storage.setString("isGetCodeDay", "false");
      $2Manager.Manager.storage.setString("isGetCodeWeek", "false");
    }
    var r = cc.sys.localStorage.getItem("GetExchangeCode", []);
    if (r && "" != r) {
      this.code_list = JSON.parse(r);
      this.code_list = this.code_list ? this.code_list : [];
    }
    var s = cc.sys.localStorage.getItem("daily_code_list", []);
    if (s && "" != s) {
      this.daily_code_list = JSON.parse(s);
      this.daily_code_list = this.daily_code_list ? this.daily_code_list : [];
    }
    var c = cc.sys.localStorage.getItem("received_gift_code_list", []);
    if (c && "" != c) {
      this.received_gift_code_list = JSON.parse(c);
      this.received_gift_code_list = this.received_gift_code_list ? this.received_gift_code_list : [];
    }
    var l = $2Notifier.Notifier.call($2CallID.CallID.Platform_CdKey);
    this.isLive && l && !this.received_gift_code_list.includes(l) && (this.codeEditBox.string = l);
    this.isGet = this.isGet || "false" == t;
  };
  _ctor.prototype.onClickConfirm = function () {
    var e = this.codeEditBox.string;
    if (this.isLive) {
      if (null == e || "" == e) {
        return void $2AlertManager.AlertManager.showNormalTips("请输入直播福袋兑换码");
      }
      if (this.received_gift_code_list.includes(e)) {
        return void $2AlertManager.AlertManager.showNormalTips("您已经领取过奖励了");
      }
      this.giftCode = e;
      var t = {
        app_name: $2WonderSdk.WonderSdk._instance.BmsName,
        open_id: $2Manager.Manager.vo.openId,
        gift_code: e,
        env_type: "production",
        uuid: this.generateUUID()
      };
      console.log("礼包领取参数", t);
      this.giftReceiveReward(t);
    } else {
      if (!this.isGet && this.code_list.includes(e)) {
        return void $2AlertManager.AlertManager.showNormalTips("您已经领取过奖励了");
      }
      this.handleCode(e);
    }
  };
  _ctor.prototype.generateUUID = function () {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (e) {
      var t = 16 * Math.random() | 0;
      return ("x" === e ? t : 3 & t | 8).toString(16);
    });
  };
  _ctor.prototype.giftReceiveReward = function (e) {
    var t = this;
    if (!this.isRequest) {
      this.isRequest = true;
      $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.GIFT_RECEIVE_REWARD, e, "POST").then(function (e) {
        var o;
        console.log("礼包返回", e);
        if (0 == (null == e ? undefined : e.code)) {
          var i = null === (o = null == e ? undefined : e.data) || undefined === o ? undefined : o.prop_list;
          if (null != i && i.length > 0) {
            var n = i[0].prop_id;
            t.handleCode(n);
          }
        } else if (28006040 == (null == e ? undefined : e.code)) {
          $2AlertManager.AlertManager.showNormalTips("您已经领取过奖励了");
        } else if (28001007 == (null == e ? undefined : e.code)) {
          $2AlertManager.AlertManager.showNormalTips("兑换失败，请稍候重试！【错误码：28001007】");
        } else if (28006041 == (null == e ? undefined : e.code)) {
          $2AlertManager.AlertManager.showNormalTips("兑换失败，请稍候重试！【错误码：28006041】");
        } else {
          $2AlertManager.AlertManager.showNormalTips("兑换失败，请稍候重试！");
        }
        t.isRequest = false;
      }).catch(function () {
        t.isRequest = false;
        $2AlertManager.AlertManager.showNormalTips("兑换失败，请稍候重试！");
      });
    }
  };
  _ctor.prototype.handleCode = function (e) {
    var t;
    var o = $2Manager.Manager.vo.switchVo;
    var i = (null == o ? undefined : o.redeemCode) || [];
    for (var n = 0; n < i.length; n++) {
      if (e == i[n].code) {
        if (1 == i[n].type) {
          if ($2Time.Time.serverTime >= i[n].startTime && $2Time.Time.serverTime <= i[n].endTime && "daily" != (null == (t = i[n]) ? undefined : t.name) && !this.isLive) {
            this.code_list.push(e);
            var r = JSON.stringify(this.code_list);
            cc.sys.localStorage.setItem("GetExchangeCode", r);
          }
        } else if (2 == i[n].type) {
          var a = [7, 1, 2, 3, 4, 5, 6][(u = new Date($2Time.Time.serverTimeMs)).getDay()];
          a >= i[n].startTime && a <= i[n].endTime && (t = i[n]);
        }
        break;
      }
    }
    if (t) {
      if (!this.isLive && 2 == t.type && "true" == $2Manager.Manager.storage.getString("isGetCodeWeek", "false")) {
        return void $2AlertManager.AlertManager.showNormalTips("您已经领取过奖励了");
      }
      var l = [];
      if ("daily" == (null == t ? undefined : t.name)) {
        var u = new Date($2Time.Time.serverTimeMs);
        var d = cc.js.formatStr("%d%d%d", u.getFullYear(), u.getMonth(), u.getDate());
        var g = t.code + "_" + d;
        if (!this.isLive && this.daily_code_list.includes(g)) {
          return void $2AlertManager.AlertManager.showNormalTips("您已经领取过奖励了");
        }
        var y = $2GameUtil.GameUtil.getRandomByWeightInList(null == t ? undefined : t.rewardCount, 1)[0].id || 1;
        console.log("奖励个数", y);
        y > t.reward.length && (y = t.reward.length);
        var v = $2GameUtil.GameUtil.getRandomByWeightInList(t.reward, y, 2);
        console.log("奖励list", v);
        v.forEach(function (e, t) {
          l[t] = {
            id: e.val[0],
            num: e.val[1],
            type: $2Cfg.Cfg.CurrencyConfig.get(e.val[0]).type,
            rarity: $2Cfg.Cfg.CurrencyConfig.get(e.val[0]).rarity,
            isNewModel: true
          };
        });
        if (!(this.isLive || this.daily_code_list.includes(g))) {
          this.daily_code_list.push(g);
          r = JSON.stringify(this.daily_code_list);
          cc.sys.localStorage.setItem("daily_code_list", r);
        }
      } else {
        null == t || t.reward.forEach(function (e, t) {
          l[t] = {
            id: e[0],
            num: e[1],
            type: $2Cfg.Cfg.CurrencyConfig.get(e[0]).type,
            rarity: $2Cfg.Cfg.CurrencyConfig.get(e[0]).rarity,
            isNewModel: true
          };
        });
        if (1 == t.type) {
          $2Manager.Manager.storage.setString("isGetCodeDay", "true");
        } else {
          2 == t.type && $2Manager.Manager.storage.setString("isGetCodeWeek", "true");
        }
      }
      if (this.isLive && !this.received_gift_code_list.includes(this.giftCode)) {
        this.received_gift_code_list.push(this.giftCode);
        r = JSON.stringify(this.received_gift_code_list);
        cc.sys.localStorage.setItem("received_gift_code_list", r);
      }
      if (this.isLive) {
        $2Manager.Manager.storage.setString("record_live_gift_code", e);
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SetUserProperty, "user_set", {
          receiveLiveGift: e
        });
      }
      $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, l);
      this.close();
    } else {
      $2AlertManager.AlertManager.showNormalTips("兑换码已过期或输入有误");
    }
  };
  _ctor.prototype.onClickFrame = function () {
    this.close();
  };
  cc__decorate([ccp_property(cc.EditBox)], _ctor.prototype, "codeEditBox", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnConfirm", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Setting/ExchangeCodeView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel), $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)], _ctor);
}($2Pop.Pop);
exports.default = def_ExchangeCodeView;