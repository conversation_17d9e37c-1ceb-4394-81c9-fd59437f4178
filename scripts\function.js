Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getServerTime = exports.getClientTime = exports.getNonce = exports.arrSort = exports.getSign = exports.response = exports.formatDate = exports.versionFormat = undefined;
var $2Api = require("Api");
var $2md51 = require("md51");
var $2config = require("config");
function exp_arrSort(e) {
  var t = {};
  Object.keys(e).sort().forEach(function (o) {
    t[o] = e[o];
  });
  return t;
}
function exp_getNonce() {
  return $2md51.md5((exp_getClientTime() + 1e3 * Math.random()).toString());
}
function exp_getClientTime() {
  return Math.round(Date.now() / 1e3);
}
exports.versionFormat = function (e) {
  return parseInt(e.replace(".", ""));
};
exports.formatDate = function (e, t) {
  var o = {
    "M+": e.getMonth() + 1,
    "d+": e.getDate(),
    "h+": e.getHours() % 12 == 0 ? 12 : e.getHours() % 12,
    "H+": e.getHours(),
    "m+": e.getMinutes(),
    "s+": e.getSeconds(),
    "q+": Math.floor((e.getMonth() + 3) / 3),
    S: e.getMilliseconds()
  };
  /(y+)/.test(t) && (t = t.replace(RegExp.$1, (e.getFullYear() + "").substr(4 - RegExp.$1.length)));
  /(E+)/.test(t) && (t = t.replace(RegExp.$1, (RegExp.$1.length > 1 ? RegExp.$1.length > 2 ? "星期" : "周" : "") + "日一二三四五六".charAt(e.getDay())));
  for (var i in o) {
    new RegExp("(" + i + ")").test(t) && (t = t.replace(RegExp.$1, 1 == RegExp.$1.length ? o[i] : ("00" + o[i]).substr(("" + o[i]).length)));
  }
  return t;
};
exports.response = function (e, t, o) {
  undefined === e && (e = 0);
  undefined === t && (t = "");
  undefined === o && (o = {});
  return {
    code: e,
    msg: t,
    data: o
  };
};
exports.getSign = function (e) {
  e.timestamp = exp_getClientTime();
  e.nonce = exp_getNonce();
  e = exp_arrSort(e);
  var t = [];
  for (var o in e) {
    t.push(o + "=" + e[o]);
  }
  var i = t.join("&") + $2config.API_SECRET;
  e.sign = $2md51.md5(i);
  return e;
};
exports.arrSort = exp_arrSort;
exports.getNonce = exp_getNonce;
exports.getClientTime = exp_getClientTime;
exports.getServerTime = function (e) {
  $2Api.serverTime(e);
};