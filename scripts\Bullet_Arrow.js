var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();
cc.v2();
var def_Bullet_Arrow = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.ve = new cc.Vec2();
    t.time = 0;
    t.isStop = false;
    t.startPos = cc.v2();
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.set = function (e) {
    this.ve.set($2GameUtil.GameUtil.AngleAndLenToPos(e.angle, e.power / 200).mul(40));
    this.isStop = false;
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    this._vo.lifeTime < 0 || this.isActive && (this.isStop || (p.set(cc.v2(this.ve.x * t, this.ve.y * t - -4.9 * t * t)), p.normalizeSelf(), this._vo.shootDir.set(p), cc.Vec2.multiplyScalar(p, this._vo.shootDir, this.maxSpeed * t), cc.Vec2.add(p, this.position, p), this.setPosition(p), 0 == this.isRotate && this.updateDir(t), this.ve.y += -9.8 * t * t * 100));
  };
  _ctor.prototype.onCollisionEnter = function (t, o) {
    e.prototype.onCollisionEnter.call(this, t, o);
    if ("map" == t.node.name) {
      this.isStop = true;
      this.vo.lifeTime = 1;
    }
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/Bullet_Arrow")], _ctor);
}($2BulletBase.default);
exports.default = def_Bullet_Arrow;