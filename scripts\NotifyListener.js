var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NotifyListener = exports.ListenerManager = undefined;
var $2Log = require("Log");
var $2ListenID = require("ListenID");
var $2NotifyID = require("NotifyID");
var s = function () {
  function e(e, t, o) {
    this._prior = 0;
    this._sendTimes = 0;
    this._callback = e;
    this._context = t;
    this._prior = o;
  }
  Object.defineProperty(e.prototype, "context", {
    get: function () {
      return this._context;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "callback", {
    get: function () {
      return this._callback;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(e.prototype, "prior", {
    get: function () {
      return this._prior;
    },
    enumerable: false,
    configurable: true
  });
  return e;
}();
var exp_ListenerManager = function () {
  function _ctor(e) {
    this._sendTimes = 0;
    this._listenId = 0;
    this._listenId = e;
    this._handlers = new Array();
  }
  Object.defineProperty(_ctor.prototype, "handlers", {
    get: function () {
      return this._handlers;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "listenId", {
    get: function () {
      return this._listenId;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.toString = function () {
    return "<ListenerManager id:%{m_id}, times:%{m_sendTimes}>";
  };
  _ctor.prototype.IsExistHandler = function (e, t) {
    var o = this._handlers.length;
    if (o > 0) {
      for (var i = o - 1; i >= 0; i--) {
        var n = this._handlers[i];
        if (n.callback === e && n.context == t) {
          return true;
        }
      }
    }
    return false;
  };
  _ctor.prototype.RegisterHandler = function (e, t, o) {
    var i = new s(e, t, o);
    var n = this._handlers.length;
    if (n > 0) {
      var r = false;
      for (var a = n - 1; a >= 0; a--) {
        if (i.prior >= this._handlers[a].prior) {
          this._handlers.splice(a + 1, 0, i);
          r = true;
          break;
        }
      }
      r || this._handlers.unshift(i);
    } else {
      this._handlers.push(i);
    }
    return true;
  };
  _ctor.prototype.RemoveHandler = function (e, t) {
    var o = -1;
    var i = this._handlers.length;
    if (i > 0) {
      for (var n = i - 1; n >= 0; n--) {
        var r = this._handlers[n];
        if (r.callback === e && r.context == t) {
          o = n;
          break;
        }
      }
    }
    return -1 != o && (this._handlers.splice(o, 1), true);
  };
  _ctor.prototype.Send = function () {
    var e;
    var t = [];
    for (var o = 0; o < arguments.length; o++) {
      t[o] = arguments[o];
    }
    var n = this._handlers.length;
    for (var r = n - 1; r >= 0; r--) {
      var a = this._handlers[r];
      (e = a.callback).call.apply(e, cc__spreadArrays([a.context], t));
    }
  };
  return _ctor;
}();
exports.ListenerManager = exp_ListenerManager;
var exp_NotifyListener = function () {
  function e() {
    this._managers = {};
    this._callStacks = [];
  }
  e.prototype.GetCellStackString = function () {
    var e = "[";
    var t = 0;
    for (var o = this._callStacks; t < o.length; t++) {
      e += o[t] + ",";
    }
    return e + "]";
  };
  e.prototype.CheckAndPushCallStack = function (e) {
    var t = this._callStacks.length;
    if (t >= 15) {
      return cc.error("[NotifyListener].Send out call stack:" + this.GetCellStackString() + " msg:" + e), false;
    } else {
      if (t >= 10) {
        return cc.warn("[NotifyListener].Send warning call stack:" + this.GetCellStackString() + " msg:" + e), false;
      } else {
        return this._callStacks.push(e), true;
      }
    }
  };
  e.prototype.PopCallStack = function () {
    this._callStacks.pop();
  };
  e.prototype.Register = function (e, t, o, i) {
    if (null != t) {
      var s = this._managers[e];
      if (null == s) {
        s = new exp_ListenerManager(e);
        this._managers[e] = s;
      } else if (s.IsExistHandler(t, o)) {
        return void $2Log.Log.error("[NotifyListener].Register:" + ($2ListenID.ListenID[e] || $2NotifyID.NotifyID[e]) + " callback repeat, skip " + o);
      }
      s.RegisterHandler(t, o, i);
    } else {
      $2Log.Log.error("[NotifyListener].Register:" + e + " callback null");
    }
  };
  e.prototype.Unregister = function (e, t, o) {
    var i = this._managers[e];
    if (null != i) {
      i.RemoveHandler(t, o) || $2Log.Log.warn("[NotifyListener].Unregister:" + e + " can't find callback:" + t);
      0 == i.handlers.length && delete this._managers[i.listenId];
    } else {
      $2Log.Log.warn("[NotifyListener].Unregister can't find ListenerManager:" + e + " callback:" + t);
    }
  };
  e.prototype.Send = function (e) {
    var t = [];
    for (var o = 1; o < arguments.length; o++) {
      t[o - 1] = arguments[o];
    }
    var i = this._managers[e];
    if (null != i && this.CheckAndPushCallStack(e)) {
      i.Send.apply(i, t);
      this.PopCallStack();
    }
  };
  e.prototype.IsExist = function (e) {
    return null != this._managers[e];
  };
  return e;
}();
exports.NotifyListener = exp_NotifyListener;