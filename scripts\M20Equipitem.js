var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__spreadArrays = __spreadArrays;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_M20Equipitem = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.equipName = null;
    t.equipImg = null;
    t.equipBg = null;
    t.equipCellIcon = null;
    t.equipFrame = null;
    t.equipLv = null;
    t.upgradeNode = null;
    t.puzzleNode = null;
    t.eueiplvcfgs = [];
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "equipID", {
    get: function () {
      return this.equipcfg.id;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isEquip", {
    get: function () {
      var e;
      if (null === (e = this.mode.userEquipPack.getItem(this.equipID)) || undefined === e) {
        return undefined;
      } else {
        return e.isFitOut;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeBackpackHeroModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "view", {
    get: function () {
      return $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Equip");
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function (e) {
    var t = this.equipcfg = $2Cfg.Cfg.RoleUnlock.find({
      id: e
    });
    this.eueiplvcfgs.length = 0;
    if (3 == t.type) {
      this.eueiplvcfgs = cc__spreadArrays($2Cfg.Cfg.RoleLv.filter({
        roleId: e
      }));
    } else {
      this.eueiplvcfgs = cc__spreadArrays($2Cfg.Cfg.EquipLv.filter({
        equipId: e
      }));
    }
    if (3 != this.equipcfg.type) {
      // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/bg_option_0" + this.equipcfg.rarity, this.equipBg, this.view.node);
      $2Manager.Manager.loader.loadSpriteToSprit("v1/images/bg/bg_option_0" + this.equipcfg.rarity, this.equipBg, this.view.node);
      $2Manager.Manager.loader.loadSpriteToSprit(this.equipcfg.icon, this.equipImg, this.view.node);
    }
    this.equipName.string = this.equipcfg.roleName;
    this.resetState();
  };
  _ctor.prototype.resetState = function () {
    var e = 3 == this.equipcfg.type || this.mode.userEquipPack.has(this.equipID);
    this.equipFrame.node.parent.setActive(e);
    var t = 3 == this.equipcfg.type ? Math.max(this.mode.fightinfopack.getVal("role" + this.equipcfg.id), 1) : this.mode.getEquipLv(this.equipcfg.id);
    this.equipLv.string = t >= this.eueiplvcfgs[this.eueiplvcfgs.length - 1].lv ? "已满级" : cc.js.formatStr("等级%d", t);
    if (e) {
      var o = this.mode.fragmentsPack.getVal(this.equipcfg.id);
      var i = this.eueiplvcfgs.find(function (e) {
        return e.lv == t;
      }).upgradeNeedle;
      this.equipFrame.string = o + "/" + i;
      this.getComponentInChildren(cc.ProgressBar).progress = o / i;
      this.upgradeNode.setActive(o >= i);
      if (this.upgradeNode.active) {
        cc.tween(this.upgradeNode).to(.5, {
          y: 0
        }).to(.5, {
          y: 10
        }).union().repeatForever().start();
      } else {
        cc.Tween.stopAllByTarget(this.upgradeNode);
        this.upgradeNode.setPosition(0, -5);
      }
      this.puzzleNode.setActive(!(o >= i));
    }
  };
  _ctor.prototype.showInfo = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.M20_ShowEquipInfo, $2MVC.MVC.openArgs().setParam({
      equipid: this.equipcfg.id
    }));
  };
  _ctor.prototype.setClickCall = function (e) {
    this._onClickCall = e;
  };
  _ctor.prototype.onClick = function () {
    this._onClickCall && this._onClickCall(this);
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "equipName", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "equipImg", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "equipBg", undefined);
  cc__decorate([ccp_property(cc.Sprite)], _ctor.prototype, "equipCellIcon", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "equipFrame", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "equipLv", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "upgradeNode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "puzzleNode", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_M20Equipitem;