var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FightScene = undefined;
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2ADModel = require("ADModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
var exp_FightScene = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.gameNode = null;
    t.gameUiNode = null;
    t.game = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_AddSkill, this.addSkill, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_AddBuff, this.addBuff, this);
  };
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.addSkill = function (e) {
    this.role.skillMgr.add(e);
  };
  _ctor.prototype.addBuff = function (e) {
    this.role.addBuff(e);
  };
  _ctor.prototype.onOpen = function () {};
  _ctor.prototype.setInfo = function () {
    if (this.game) {
      this.game.destroy();
      this.game = null;
    }
  };
  _ctor.prototype.onClose = function () {
    this.unscheduleAllCallbacks();
  };
  _ctor.prototype.onShowFinish = function () {};
  _ctor.prototype.onHideFinish = function () {};
  _ctor.prototype.onShow = function () {};
  _ctor.prototype.onHide = function () {
    if (this.game) {
      this.game.destroy();
      this.game = null;
    }
  };
  _ctor.prototype.update = function (e) {
    e > .3 && (e = 1 / cc.game.getFrameRate());
    this.game && !$2ADModel.default.instance.isVideoIng && this.game.onUpdate(e * this.game.gameSpeed);
  };
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "gameNode", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "gameUiNode", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Fight/FightScene"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Scene), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Scene)], _ctor);
}($2MVC.MVC.BaseView);
exports.FightScene = exp_FightScene;