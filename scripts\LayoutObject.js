Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LayoutObject = undefined;
var exp_LayoutObject = function () {
  function _ctor() {}
  _ctor.prototype.getBoundingRect = function () {
    return cc.Vec2.ZERO;
  };
  _ctor.prototype.getPosByIndex = function (e) {
    return this.doLayout(e);
  };
  _ctor.prototype.doLayout = function () {
    return cc.Vec2.ZERO;
  };
  return _ctor;
}();
exports.LayoutObject = exp_LayoutObject;