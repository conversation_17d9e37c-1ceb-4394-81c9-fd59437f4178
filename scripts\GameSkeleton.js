var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Game = require("Game");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_menu = cc__decorator.menu;
var ccp_property = cc__decorator.property;
var def_GameSkeleton = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.type = $2GameSeting.GameSeting.TweenType.Game;
    t.isPlayerOnLoad = false;
    t.defaultAnim = "";
    t.AnimList = [];
    t.cutIndex = 0;
    t.cutQueue = [];
    t.lastIsLoop = false;
    return t;
  }
  var o;
  cc__extends(_ctor, e);
  o = _ctor;
  _ctor.prototype.set = function (e) {
    this.type = e;
    return this;
  };
  _ctor.prototype.onLoad = function () {
    this.changeListener(true);
  };
  _ctor.prototype.onEnable = function () {
    e.prototype.onEnable.call(this);
    if (this.isPlayerOnLoad) {
      if (this.AnimList.length > 0) {
        this.playQueue(this.AnimList.slice(0, 2), true);
      } else {
        this.setAnimation(0, this.defaultAnim || this.defaultAnimation, this.loop);
      }
      cc.tween(this.node).to(.1, {
        opacity: 255
      }).start();
    }
  };
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onDisable = function () {
    var t;
    if (this.AnimList[2]) {
      this.setAnimation(0, this.AnimList[2], false);
      var i = cc.instantiate(this.node).setAttribute({
        group: "Game",
        parent: this.game.botEffectNode,
        position: this.node.wordPos,
        scale: this.node.scale * ((null === (t = this.node.parent) || undefined === t ? undefined : t.scale) || 1)
      });
      i.getComponent(o).setAttribute({
        isPlayerOnLoad: false,
        AnimList: []
      }).setAnimation(0, this.AnimList[2], false);
      cc.tween(i).delay(.2).to(.3, {
        opacity: 0
      }).destroySelf().start();
    }
    e.prototype.onDisable.call(this);
  };
  _ctor.prototype.onDestroy = function () {
    e.prototype.onDestroy.call(this);
    this.changeListener(false);
  };
  _ctor.prototype.changeListener = function (e) {
    this.type == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_OnGameState, this.onGameState, this);
  };
  _ctor.prototype.onGameState = function (e) {
    this.setPause(e == $2Game.Game.State.PAUSE);
  };
  _ctor.prototype.playQueue = function (e, t) {
    var o = this;
    undefined === t && (t = true);
    e.forEach(function (i, n) {
      if (0 == n) {
        o.setAnimation(0, i, false);
      } else {
        o.addAnimation(0, i, t && n == e.length - 1, 0);
      }
    });
  };
  cc__decorate([ccp_property({
    type: cc.Enum($2GameSeting.GameSeting.TweenType)
  })], _ctor.prototype, "type", undefined);
  cc__decorate([ccp_property({
    displayName: "显示自动播放"
  })], _ctor.prototype, "isPlayerOnLoad", undefined);
  cc__decorate([ccp_property({
    displayName: "默认动画"
  })], _ctor.prototype, "defaultAnim", undefined);
  cc__decorate([ccp_property([cc.String])], _ctor.prototype, "AnimList", undefined);
  return o = cc__decorate([ccp_ccclass, ccp_menu("GameComponent/GameSkeleton")], _ctor);
}(sp.Skeleton);
exports.default = def_GameSkeleton;