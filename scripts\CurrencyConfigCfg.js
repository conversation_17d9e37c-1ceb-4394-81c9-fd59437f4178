var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CurrencyConfigCfgReader = exports.CurrencyConfigDefine = undefined;
var $2TConfig = require("TConfig");
(function (e) {
  e[e.Energy = 1] = "Energy";
  e[e.Coin = 2] = "Coin";
  e[e.Crystal = 3] = "Crystal";
  e[e.GOLD = 4] = "GOLD";
  e[e.Vedio = 5] = "Vedio";
  e[e.<PERSON>a = 6] = "Mana";
  e[e.Amethyst = 7] = "Amethyst";
  e[e.Amethyst_In = 8] = "Amethyst_In";
  e[e.Box = 9] = "Box";
  e[e.Equipfragments = 10] = "Equipfragments";
  e[e.Diamond = 11] = "Diamond";
  e[e.BEquipfragments = 12] = "BEquipfragments";
  e[e.AEquipfragments = 13] = "AEquipfragments";
  e[e.SEquipfragments = 14] = "SEquipfragments";
  e[e.silver = 15] = "silver";
  e[e.adcoupons_in = 16] = "adcoupons_in";
  e[e.adcoupons_out = 17] = "adcoupons_out";
  e[e.score = 18] = "score";
  e[e.boxkey = 19] = "boxkey";
  e[e.advboxkey = 20] = "advboxkey";
  e[e.purchases = 21] = "purchases";
  e[e.adcoupons_gift = 22] = "adcoupons_gift";
  e[e.DayTaskCoin = 23] = "DayTaskCoin";
  e[e.WeekTaskCoin = 24] = "WeekTaskCoin";
  e[e.High_Box = 25] = "High_Box";
  e[e.Share = 26] = "Share";
  e[e.buffSelect = 27] = "buffSelect";
  e[e.dragonBall1 = 28] = "dragonBall1";
  e[e.dragonBall2 = 29] = "dragonBall2";
  e[e.dragonBall3 = 30] = "dragonBall3";
  e[e.dragonBall4 = 31] = "dragonBall4";
  e[e.dragonBall5 = 32] = "dragonBall5";
  e[e.dragonBall6 = 33] = "dragonBall6";
  e[e.dragonBall7 = 34] = "dragonBall7";
  e[e.buffDrop = 35] = "buffDrop";
  e[e.ranBuffDrop = 36] = "ranBuffDrop";
  e[e.boxBuffDrop = 37] = "boxBuffDrop";
  e[e.sbuffSelect = 38] = "sbuffSelect";
  e[e.Herofragments_dz = 103] = "Herofragments_dz";
  e[e.Herofragments_ed = 104] = "Herofragments_ed";
  e[e.Herofragments_hzg = 105] = "Herofragments_hzg";
  e[e.Herofragments_sb = 106] = "Herofragments_sb";
  e[e.daggerEquipfragments = 1e3] = "daggerEquipfragments";
  e[e.knucklesEquipfragments = 1001] = "knucklesEquipfragments";
  e[e.dartsEquipfragments = 1002] = "dartsEquipfragments";
  e[e.molotovEquipfragments = 1003] = "molotovEquipfragments";
  e[e.bowEquipfragments = 1004] = "bowEquipfragments";
  e[e.icegunEquipfragments = 1005] = "icegunEquipfragments";
  e[e.vestEquipfragments = 1006] = "vestEquipfragments";
  e[e.axeEquipfragments = 1007] = "axeEquipfragments";
  e[e.boomerangEquipfragments = 1008] = "boomerangEquipfragments";
  e[e.sheildEquipfragments = 1009] = "sheildEquipfragments";
  e[e.sniperrifleEquipfragments = 1010] = "sniperrifleEquipfragments";
  e[e.dynamiteEquipfragments = 1011] = "dynamiteEquipfragments";
  e[e.laserEquipfragments = 1012] = "laserEquipfragments";
  e[e.swordEquipfragments = 1013] = "swordEquipfragments";
  e[e.swordEquipfragments = 1014] = "swordEquipfragments";
  e[e.landminesfragments = 1015] = "landminesfragments";
  e[e.Herofragments_xs = 3e3] = "Herofragments_xs";
  e[e.Herofragments_xh = 3001] = "Herofragments_xh";
  e[e.Herofragments_xm = 3002] = "Herofragments_xm";
  e[e.fightreward = 4e3] = "fightreward";
})(exports.CurrencyConfigDefine || (exports.CurrencyConfigDefine = {}));
var exp_CurrencyConfigCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "CurrencyConfig";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($2TConfig.TConfig);
exports.CurrencyConfigCfgReader = exp_CurrencyConfigCfgReader;