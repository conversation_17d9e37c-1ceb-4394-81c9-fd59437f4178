var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2RBadgeModel = require("RBadgeModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var d = cc.Enum($2RBadgeModel.RBadge.Key);
var def_RBadgePoint = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.myKey = d.Fight;
    t.myID = 0;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2RBadgeModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onLoad = function () {
    this.node.opacity = 0;
    this.hasTree = !!$2RBadgeModel.RBadge.Tree[this.myKey];
    this.changeListener(true);
    this.numLabel = this.node.getComByChild(cc.Label);
    this.onBadge_Update();
  };
  _ctor.prototype.onDestroy = function () {
    this.changeListener(false);
  };
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Badge_Update, this.onBadge_Update, this, $2Notifier.PriorLowest);
  };
  _ctor.prototype.resetPoint = function (e) {
    for (var t in e) {
      this[t] = e[t];
    }
    this.hasTree = !!$2RBadgeModel.RBadge.Tree[this.myKey];
    if (d[this.myKey]) {
      this.onBadge_Update();
    } else {
      this.node.destroy();
    }
  };
  Object.defineProperty(_ctor.prototype, "childCont", {
    get: function () {
      var e = 0;
      this.mode.getChildBadge(this.myKey).forEach(function (t) {
        var o = $2Notifier.Notifier.call($2CallID.CallID.Badge_Get, t);
        o && !o.isTree && (e += 1);
      });
      return e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onBadge_Update = function () {
    var e = $2Notifier.Notifier.call($2CallID.CallID.Badge_Get, this.myKey, this.myID);
    var t = (null == e ? undefined : e.childCont) || 0;
    this.numLabel && (this.numLabel.string = t);
    if (this.hasTree) {
      this.node.opacity = this.childCont > 0 ? 255 : 0;
    } else {
      this.node.opacity = e ? 255 : 0;
    }
  };
  cc__decorate([ccp_property({
    type: d,
    displayName: "红点KEY"
  })], _ctor.prototype, "myKey", undefined);
  cc__decorate([ccp_property({
    displayName: "唯一ID"
  })], _ctor.prototype, "myID", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("GameComponent/RBadgePoint")], _ctor);
}(cc.Component);
exports.default = def_RBadgePoint;