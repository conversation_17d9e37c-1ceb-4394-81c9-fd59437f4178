Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ObjectPool = undefined;
var exp_ObjectPool = function () {
  function _ctor(e, t, o) {
    undefined === t && (t = null);
    undefined === o && (o = null);
    this._ctor = e;
    this._queue = [];
    this._onPop = t;
    this._onPush = o;
  }
  _ctor.prototype.pop = function () {
    var e;
    e = this._queue.length > 0 ? this._queue.shift() : this._ctor();
    null != this._onPop && this._onPop(e);
    return e;
  };
  _ctor.prototype.push = function (e) {
    null != this._onPush && this._onPush(e);
    this._queue.push(e);
  };
  _ctor.prototype.clear = function () {
    this._queue = [];
  };
  return _ctor;
}();
exports.ObjectPool = exp_ObjectPool;