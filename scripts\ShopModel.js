var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2ListenID = require("ListenID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Time = require("Time");
var $2RecordVo = require("RecordVo");
var $2AlertManager = require("AlertManager");
var $2ItemModel = require("ItemModel");
var f = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.orderList = {};
    t.subscribeList = [];
    return t;
  }
  cc__extends(t, e);
  return t;
}($2RecordVo.RecordVo.Data);
var def_ShopModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.onPayFinish = {};
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "rVo", {
    get: function () {
      return this.recordVo.vo;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.loginFinish = function () {
    this.recordVo = new $2RecordVo.RecordVo.Mgr("Shop", function () {
      return new f();
    }, {
      isAutoSave: true,
      isAutoToform: false
    });
  };
  _ctor.prototype.Buy = function (e, t) {
    var o = this;
    if (!(wonderSdk.isNative || wonderSdk.isWeChat || wonderSdk.isByteDance)) {
      return this.buyCharge(e, t);
    }
    if (t) {
      this.onPayFinish[e.id] = null;
      this.onPayFinish[e.id] = t;
    }
    $2Notifier.Notifier.send($2ListenID.ListenID.Pay_ToPay, e, function (i) {
      console.log(i);
      1 == i && o.buyCharge(e);
      t && t(1 == i);
    });
  };
  _ctor.prototype.buyCharge = function (e) {
    cc.log("[buyCharge]购买成功标记", e);
    null == this.rVo.orderList[e.id] && (this.rVo.orderList[e.id] = 0);
    this.rVo.orderList[e.id]++;
    2 == e.type && this.rVo.subscribeList.push({
      id: e.id,
      buyDay: $2Time.Time.serverTimeMs,
      getRewardDay: "",
      getRewardNum: 0
    });
    this.recordVo.SaveData();
    $2Notifier.Notifier.send($2ListenID.ListenID.Shop_InfoUpDate);
    $2Notifier.Notifier.send($2ListenID.ListenID.Shop_BuyCharge, e);
    this.onBuyChange(e);
  };
  _ctor.prototype.onBuyChange = function (e) {
    cc.log("[onBuyChange]", e);
    var t = $2ItemModel.default.instance.arrToReward(e.reward[0]);
    $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, t);
    $2AlertManager.AlertManager.showNormalTipsOnce("购买成功");
    $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_Item);
  };
  Object.defineProperty(_ctor.prototype, "shopInfo", {
    get: function () {
      return 1;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.getFromPrice = function (e) {
    var t;
    var o;
    return (null === (o = null === (t = this.payList) || undefined === t ? undefined : t.find(function (t) {
      return t.product == e.productId;
    })) || undefined === o ? undefined : o.formPrice) || e.buyValue;
  };
  _ctor.prototype.checkSubscribeCanGet = function (e) {
    var t = new Date($2Time.Time.serverTimeMs).getMonthDay();
    var o = this.rVo.subscribeList.find(function (t) {
      return t.id == e.id;
    });
    return !!o && (null == o ? undefined : o.getRewardDay) != t;
  };
  _ctor.prototype.checkOrder = function (e) {
    return this.rVo.orderList[e] || 0;
  };
  Object.defineProperty(_ctor.prototype, "Subscrib_30", {
    get: function () {
      return this.rVo.subscribeList.find(function (e) {
        return 200 == e.id;
      });
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "Subscrib_Long", {
    get: function () {
      return this.rVo.subscribeList.find(function (e) {
        return 201 == e.id;
      });
    },
    enumerable: false,
    configurable: true
  });
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_ShopModel;