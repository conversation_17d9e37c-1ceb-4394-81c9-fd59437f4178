var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
cc.v2();
cc.v2();
var def_ArcBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.lastPos = cc.v2();
    t._dt = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    var t = $2GameUtil.GameUtil.GetAngle(this.vo.startPos, this.vo.targetPos) + 90;
    var o = [];
    o.push(this.vo.startPos);
    o.push(this.vo.targetPos.add(this.vo.startPos).div(2).add($2GameUtil.GameUtil.AngleAndLenToPos(t - 90 * (this.vo.targetPos.x > 0 ? -1 : 1), 500 + $2Game.Game.random(-100, 100))));
    o.push(this.vo.targetPos);
    var i = cc.Vec2.distance(this.vo.startPos, this.vo.targetPos) / this.maxSpeed;
    $2Game.Game.tween(this.node).bezierTo(i, o[0], o[1], o[2]).by(300 / this.maxSpeed, {
      position: o[2].sub(o[1]).normalize().mul(500)
    }).start();
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/ArcBullet")], _ctor);
}($2BulletBase.default);
exports.default = def_ArcBullet;