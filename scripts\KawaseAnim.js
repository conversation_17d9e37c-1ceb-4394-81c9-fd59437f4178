var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_KawaseAnim = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.kval = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onLoad = function () {
    this.material = this.getComponent(cc.Sprite).getMaterial(0);
    cc.tween(this).to(.5, {
      kval: 5e3
    }).to(.5, {
      kval: 500
    }).union().repeatForever().start();
  };
  _ctor.prototype.update = function () {
    this.material.setProperty("resolution", cc.v2(this.kval, this.kval));
  };
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_KawaseAnim;