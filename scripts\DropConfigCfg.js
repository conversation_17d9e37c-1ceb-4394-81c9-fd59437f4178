var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DropConfigCfgReader = exports.DropConfigDefine = undefined;
var $2TConfig = require("TConfig");
(function (e) {
  e[e.ExpGoods = 1] = "ExpGoods";
  e[e.AddHpGoods = 2] = "AddHpGoods";
  e[e.PowerRedGoods = 3] = "PowerRedGoods";
  e[e.BabyBox = 4] = "BabyBox";
  e[e.ExpGoodsA = 5] = "ExpGoodsA";
  e[e.CiTie = 6] = "CiTie";
  e[e.NCryStal = 7] = "NCryStal";
  e[e.NCoin = 8] = "NCoin";
  e[e.NAmethyst = 9] = "NAmethyst";
})(exports.DropConfigDefine || (exports.DropConfigDefine = {}));
var exp_DropConfigCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "DropConfig";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($2TConfig.TConfig);
exports.DropConfigCfgReader = exp_DropConfigCfgReader;