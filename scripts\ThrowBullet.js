var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Game = require("Game");
var $2BulletBase = require("BulletBase");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var p = cc.v2();
cc.v2();
var def_ThrowBullet = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.startPoint = null;
    t.controlPoint = null;
    t.endPoint = null;
    t.t = 0;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.init = function () {
    e.prototype.init.call(this);
    this.t = 0;
    this.startPoint = this.node.position.clone();
    this.controlPoint = this.vo.targetPos.add(this.startPoint).div($2Game.Game.random(20, 30) / 10).add(cc.v2(0, $2Game.Game.random(90, 110) / 100 * 200));
    this.endPoint = this.vo.targetPos;
    this.collider.setActive(false);
  };
  _ctor.prototype.onUpdate = function (t) {
    e.prototype.onUpdate.call(this, t);
    if (!(this._vo.lifeTime < 0) && this.isActive) {
      if (this.t > 1) {
        this.vo.lifeTime = 0;
      } else {
        !this.collider.isActive && this.t > .5 && this.collider.setActive(false);
        this.t += t * this.maxSpeed / 200;
        var o = this.lerp(this.startPoint, this.controlPoint, this.t);
        var i = this.lerp(this.controlPoint, this.endPoint, this.t);
        p.set(this.lerp(o, i, this.t));
        this.vo.shootDir.set(this.position.sub(p).normalize());
        this.updateDir(0);
        this.node.position = p;
      }
    }
  };
  _ctor.prototype.lerp = function (e, t, o) {
    return new cc.Vec2((1 - o) * e.x + o * t.x, (1 - o) * e.y + o * t.y);
  };
  return cc__decorate([ccp_ccclass, ccp_menu("Bullet/ThrowBullet")], _ctor);
}($2BulletBase.default);
exports.default = def_ThrowBullet;