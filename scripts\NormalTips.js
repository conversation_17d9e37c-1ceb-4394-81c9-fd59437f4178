var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_NormalTips = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.label = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.start = function () {};
  _ctor.prototype.setText = function (e) {
    var t = this;
    this.label.string = e;
    this.node.opacity = 0;
    this.scheduleOnce(function () {
      if (t.isValid) {
        if (t.node.children[0].width > 600) {
          var e = t.node.children[0].getComponent(cc.Layout);
          e.enabled = false;
          t.label.node.width = t.node.children[0].width = 600;
          t.label.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
          e.enabled = true;
        }
        t.node.opacity = 255;
      }
    });
    return this;
  };
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "label", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}(cc.Component);
exports.default = def_NormalTips;