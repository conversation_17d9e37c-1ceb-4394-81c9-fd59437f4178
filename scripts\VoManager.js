Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VoManager = undefined;
var i;
var $2StorageID = require("StorageID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2SdkConfig = require("SdkConfig");
var $2GameUtil = require("GameUtil");
var $2SwitchVo = require("SwitchVo");
var $2UserVo = require("UserVo");
i = 1;
var p = function () {
  return i += 1;
};
var exp_VoManager = function () {
  function _ctor() {
    this.isGetData = false;
    this.designSize = null;
    this.openId = "";
    this._userVo = new $2UserVo.UserVo();
    this._switchVo = new $2SwitchVo.SwitchVo();
    this.designSize = $2GameUtil.GameUtil.getRealDesignSize();
  }
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "switchVo", {
    get: function () {
      return this._switchVo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "userVo", {
    get: function () {
      return this._userVo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "knapsackVo", {
    get: function () {
      return this._knapsackVo;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "isNewUser", {
    get: function () {
      return this._userVo.isNewUser;
    },
    set: function (e) {
      this._userVo.isNewUser = !!e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.updateSwitchVo = function (e) {
    this.switchVo.updateSwitchVo(e);
  };
  _ctor.prototype.getNewId = function () {
    return p();
  };
  _ctor.prototype.saveUserData = function () {
    if (this.isGetData) {
      var e = this.userVo.serializeAll();
      this.updateLocalUserData(e);
    }
  };
  _ctor.prototype.pushRemoteDataSave = function (e) {
    undefined === e && (e = 10);
    if ($2SdkConfig.FarDataSaveList.includes(wonderSdk.platformId)) {
      $2Time.Time.timeDelay.cancelBy(this.sTimer);
      this.sTimer = $2Time.Time.delay(e, function () {
        $2Manager.Manager.storage.pushAllDataSave();
      }).id;
    }
  };
  _ctor.prototype.updateLocalUserData = function (e) {
    try {
      var t = e;
      "" != e && null != e || (t = this.userVo.serializeAll());
      $2Manager.Manager.storage.setString($2StorageID.StorageID.UserData, t);
    } catch (o) {
      console.error(o);
    }
  };
  _ctor._instance = null;
  return _ctor;
}();
exports.VoManager = exp_VoManager;