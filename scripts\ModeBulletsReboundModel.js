var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2GameSeting = require("GameSeting");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2MBRebound = require("MBRebound");
var def_ModeBulletsReboundModel = function (e) {
  function _ctor() {
    var o;
    var i = e.call(this) || this;
    i.gameMode = $2Game.Game.Mode.BULLETSREBOUND;
    i.poolMap = ((o = {})[$2MBRebound.MBRebound.poolType.NormalBuff] = function () {
      return i.cardPool.norBuffPool;
    }, o[$2MBRebound.MBRebound.poolType.HighBuff] = function () {
      return i.cardPool.adBuffPool;
    }, o);
    null == _ctor._instance && (_ctor._instance = i);
    return i;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "cardPool", {
    get: function () {
      var e = this;
      return $2Cfg.Cfg.BagModeSkillPool.findBy(function (t) {
        var o;
        return t.modeType == e.gameMode && (!e.game.miniGameCfg || (null === (o = t.lv) || undefined === o ? undefined : o.includes(e.game.miniGameCfg.id)));
      }) || $2Cfg.Cfg.BagModeSkillPool.findBy(function (t) {
        return t.modeType == e.gameMode;
      });
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.fightBuffWidth = function (e, t, o) {
    var i = this;
    undefined === t && (t = {});
    undefined === o && (o = this.poolMap[e]());
    t.num || (t.num = 3);
    null == t.isBinding && (t.isBinding = true);
    var n = [];
    var r = o[0];
    var a = o[1];
    var u = [];
    r.forEach(function (e, o) {
      var r = $2Game.ModeCfg.Buff.get(e);
      var p = i.role.buffMgr.get(e);
      if (!r.skillId || !t.isBinding || $2GameUtil.GameUtil.hasIntersection(i.role.skillMgr.skillIDs, r.skillId)) {
        if (p) {
          if (p.isMaxLayer) {
            return;
          }
          if (1 == r.isSelect) {
            return;
          }
        }
        r.skillId && u.push(e);
        var f = {
          id: e,
          w: a[o],
          isAd: 0
        };
        i.role.buffMgr.use(3043, true, function (e) {
          [$2GameSeting.GameSeting.RarityType.B, $2GameSeting.GameSeting.RarityType.A, $2GameSeting.GameSeting.RarityType.S].includes(r.rarity) && (f.w += e.cutVo.value[0][0]);
        });
        n.push(f);
      }
    });
    for (var p = n.length - 1; p >= 0; p--) {
      n[p].w <= 0 && $2GameUtil.GameUtil.deleteArrItem(n, n[p]);
    }
    var f = [];
    $2GameUtil.GameUtil.weightGetList(n, t.num).forEach(function (e) {
      f.push({
        id: e.id,
        isAd: e.isAd
      });
    });
    return f;
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_ModeBulletsReboundModel;