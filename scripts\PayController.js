var i;
var cc__extends = __extends;
var cc__awaiter = __awaiter;
var cc__generator = __generator;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PayController = undefined;
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2Manager = require("Manager");
var $2AlertManager = require("AlertManager");
var $2PayModel = require("PayModel");
var exp_PayController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t._curParam = null;
    t.setup($2PayModel.default.getInstance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "PayController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Pay_ToPay, this.toPay, this);
    cc.game.on("payFinish", this.payFinish, this);
  };
  _ctor.prototype.setLoadingViewVisible = function (e, t) {
    undefined === t && (t = 7);
  };
  _ctor.prototype.toPay = function (e, t, o) {
    return cc__awaiter(this, undefined, undefined, function () {
      var i;
      return cc__generator(this, function () {
        this._curOrderId = null;
        this._curProductId = e.productId;
        this._curParam = o;
        if (!wonderSdk.isByteDance || cc.sys.os != cc.sys.OS_IOS && cc.sys.os != cc.sys.OS_OSX) {
          return wonderSdk.isNative || wonderSdk.isWeChat || wonderSdk.isByteDance ? (this._curPayCall = t, this._curOrderId = "", $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, true), i = wonderSdk.isTest ? 1 : +e.buyValue, 1 == e.type ? wonderSdk.toPay({
            orderId: "",
            price: i,
            goodsId: e.productId,
            goodsName: e.name,
            roleId: "",
            extraParams: o
          }) : wonderSdk.toSubscribe({
            orderId: "",
            price: i,
            goodsId: e.productId,
            goodsName: e.name,
            roleId: "",
            extraParams: o
          })) : t && t(1), [2];
        } else {
          return $2AlertManager.AlertManager.showNormalTipsOnce("暂不支持，请使用安卓手机充值"), [2];
        }
      });
    });
  };
  _ctor.prototype.payFinish = function (e, t) {
    console.log("payFinish-payController", e, t);
    $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
    if (-1 == e || 0 == e) {
      this._curPayCall && this._curPayCall(e);
      0 == e && $2AlertManager.AlertManager.showNormalTips("支付失败");
    } else if (this._curPayCall) {
      this._curPayCall(1);
    } else {
      var o = $2Cfg.Cfg.PayShop.find({
        productId: t
      });
      if (o) {
        $2Manager.Manager.Shop.buyCharge(o);
      } else {
        console.log("[payFinish]查询不到商品:" + t);
      }
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.PayController = exp_PayController;