var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BulletVo = undefined;
var $2Cfg = require("Cfg");
var $2BaseEntity = require("BaseEntity");
var $2PropertyVo = require("PropertyVo");
var s = cc._decorator.ccclass;
var exp_BulletVo = function () {
  function _ctor() {
    this._hurt = $2PropertyVo.Hurt.Pool.pop();
    this.crossNum = 0;
    this.isHitBack = true;
    this.hitBack = 0;
    this.lotId = 0;
    this._bulletId = 0;
    this._belong = $2BaseEntity.EntityType.Role;
    this.ignore = [];
    this._campType = $2BaseEntity.CampType.Not;
    this.atkCamp = [];
    this.startPos = cc.v2();
    this.targetPos = cc.v2();
    this.shootDir = cc.v2();
    this.lifeTime = 2;
    this.isForever = false;
    this.speed = 500;
    this.bulletType = 2;
    this.range = 1;
    this.effectType = 0;
    this.scale = 1;
    this.radius = null;
    this.size = 0;
  }
  _ctor.prototype.unuse = function () {};
  _ctor.prototype.reuse = function () {
    var e = [];
    for (var t = 0; t < arguments.length; t++) {
      e[t] = arguments[t];
    }
  };
  _ctor.prototype.destroy = function () {};
  Object.defineProperty(_ctor.prototype, "hurt", {
    get: function () {
      return this._hurt;
    },
    set: function (e) {
      this._hurt = null;
      this._hurt = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bulletId", {
    get: function () {
      return this._bulletId;
    },
    set: function (e) {
      this._bulletId = e;
      this.cfg = $2Cfg.Cfg.BulletEffect.get(this.bulletId);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "bulletPath", {
    get: function () {
      if (this.bulletId) {
        return "entity/fight/Bullet/" + this.cfg.prefab;
      } else {
        return "entity/fight/Bullet/" + this.belongSkill.skillMainID;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "hitAudioId", {
    get: function () {
      return this.cfg.soundhitId;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "belong", {
    get: function () {
      return this._belong;
    },
    set: function (e) {
      this._belong = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "skillCfg", {
    get: function () {
      var e;
      if (null === (e = this.belongSkill) || undefined === e) {
        return undefined;
      } else {
        return e.cutVo;
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "ower", {
    get: function () {
      var e;
      if (null === (e = this.belongSkill) || undefined === e) {
        return undefined;
      } else {
        return e.getOwner();
      }
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "campType", {
    get: function () {
      return this._campType;
    },
    set: function (e) {
      this.ignore && (this.ignore.length = 0);
      this._campType = e;
      if (3 == this.skillCfg.object) {
        this.atkCamp = [$2BaseEntity.CampType.One, $2BaseEntity.CampType.Two, $2BaseEntity.CampType.Three];
        this.ignore && this.ignore.push(this.ower.ID);
      } else if (2 == this.skillCfg.object) {
        this.atkCamp = [e];
      } else {
        this.atkCamp = [e == $2BaseEntity.CampType.One ? $2BaseEntity.CampType.Two : $2BaseEntity.CampType.One];
      }
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setStartPos = function (e) {
    this.startPos.x = e.x;
    this.startPos.y = e.y;
    return this;
  };
  _ctor.prototype.setAttribute = function (e) {
    for (var t in e) {
      this[t] = e[t];
    }
    return this;
  };
  return cc__decorate([s("BulletVo")], _ctor);
}();
exports.BulletVo = exp_BulletVo;