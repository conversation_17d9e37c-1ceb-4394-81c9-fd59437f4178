var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GameSettingCfgReader = exports.GameSettingDefine = undefined;
var $2TConfig = require("TConfig");
(function (e) {
  e[e.tdtime = 1] = "tdtime";
  e[e.tdmonappeardis = 2] = "tdmonappeardis";
})(exports.GameSettingDefine || (exports.GameSettingDefine = {}));
var exp_GameSettingCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "GameSetting";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($2TConfig.TConfig);
exports.GameSettingCfgReader = exp_GameSettingCfgReader;